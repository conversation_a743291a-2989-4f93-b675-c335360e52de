<template>
  <div 
    class="rich-text-content"
    v-html="sanitizedContent"
  ></div>
</template>

<script setup lang="ts">
interface Props {
  content: string;
}

const props = defineProps<Props>();

// 简单的HTML清理函数（在生产环境中建议使用专门的库如DOMPurify）
const sanitizedContent = computed(() => {
  // 这里可以添加HTML清理逻辑
  // 目前只是简单返回内容，因为我们控制内容源
  return props.content;
});
</script>

<style scoped>
.rich-text-content {
  @apply prose dark:prose-invert max-w-none;
}

.rich-text-content :deep(h1),
.rich-text-content :deep(h2),
.rich-text-content :deep(h3),
.rich-text-content :deep(h4),
.rich-text-content :deep(h5),
.rich-text-content :deep(h6) {
  @apply text-gray-900 dark:text-gray-100 font-bold;
}

.rich-text-content :deep(p) {
  @apply text-gray-700 dark:text-gray-300 mb-4;
}

.rich-text-content :deep(a) {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline;
}

.rich-text-content :deep(ul) {
  @apply list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300;
}

.rich-text-content :deep(ol) {
  @apply list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300;
}

.rich-text-content :deep(li) {
  @apply text-gray-700 dark:text-gray-300;
}

.rich-text-content :deep(blockquote) {
  @apply border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-400;
}

.rich-text-content :deep(code) {
  @apply bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono;
}

.rich-text-content :deep(pre) {
  @apply bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-x-auto;
}

.rich-text-content :deep(pre code) {
  @apply bg-transparent p-0;
}

/* 处理可能的白色背景覆盖 */
.rich-text-content :deep([style*="background"]) {
  background: transparent !important;
}

.rich-text-content :deep([style*="color: white"]) {
  color: inherit !important;
}
</style>
