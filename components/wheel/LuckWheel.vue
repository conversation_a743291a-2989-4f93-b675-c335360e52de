<template>
  <div class="wheel-container">
    <!-- 指示器 -->
    <div class="wheel-indicator"></div>
    
    <!-- 转盘画布 -->
    <canvas
      ref="canvasRef"
      class="wheel-canvas"
      :width="wheelSize"
      :height="wheelSize"
    ></canvas>
    
    <!-- 开始按钮 -->
    <button
      class="wheel-start-button"
      :style="{
        width: `${startButtonSize}px`,
        height: `${startButtonSize}px`,
        fontSize: `${startButtonSize / 4}px`
      }"
      @click="startRotation"
      :disabled="isSpinning"
    >
      {{ isSpinning ? 'Spinning...' : 'Start' }}
    </button>
    
    <!-- 设置面板 -->
    <div v-if="showSettings" class="settings-panel">
      <h3>Customize Options</h3>
      <div v-for="(prize, index) in localPrizes" :key="index" class="prize-item">
        <input
          v-model="prize.text"
          @input="updatePrizes"
          placeholder="Option text"
          class="input-field"
        />
        <input
          v-model="prize.color"
          type="color"
          @input="updatePrizes"
          class="color-input"
        />
        <button @click="removePrize(index)" class="btn-remove">×</button>
      </div>
      <button @click="addPrize" class="btn-secondary">Add Option</button>
    </div>
    
    <!-- 设置按钮 -->
    <button
      @click="toggleSettings"
      class="settings-toggle"
      :class="{ active: showSettings }"
    >
      ⚙️
    </button>
  </div>
  
  <!-- 结果模态框 -->
  <SelectedModal
    v-if="selectedPrize"
    :prize="selectedPrize"
    @close="selectedPrize = null"
  />
</template>

<script setup lang="ts">
import type { Prize, LuckWheelProps } from '~/types';

interface Props {
  prizes: Prize[];
  size?: number;
  startButtonSize?: number;
  wheelId?: string;
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 400,
  startButtonSize: 80
});

const emit = defineEmits<{
  prizesChange: [prizes: Prize[]];
}>();

// 响应式数据
const canvasRef = ref<HTMLCanvasElement>();
const localPrizes = ref<Prize[]>([...props.prizes]);
const isSpinning = ref(false);
const selectedPrize = ref<Prize | null>(null);
const showSettings = ref(false);
const currentRotation = ref(0);

// 计算属性
const wheelSize = computed(() => props.size);

// 监听props变化
watch(() => props.prizes, (newPrizes) => {
  localPrizes.value = [...newPrizes];
  nextTick(() => {
    drawWheel();
  });
}, { deep: true });

// 生命周期
onMounted(() => {
  loadSavedPrizes();
  nextTick(() => {
    drawWheel();
  });
});

// 方法
function drawWheel() {
  const canvas = canvasRef.value;
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  const centerX = wheelSize.value / 2;
  const centerY = wheelSize.value / 2;
  const radius = wheelSize.value / 2 - 10;
  
  // 清空画布
  ctx.clearRect(0, 0, wheelSize.value, wheelSize.value);
  
  // 绘制转盘扇形
  const anglePerPrize = (2 * Math.PI) / localPrizes.value.length;
  
  localPrizes.value.forEach((prize, index) => {
    const startAngle = index * anglePerPrize + (currentRotation.value * Math.PI / 180);
    const endAngle = startAngle + anglePerPrize;
    
    // 绘制扇形
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, startAngle, endAngle);
    ctx.closePath();
    ctx.fillStyle = prize.color;
    ctx.fill();
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // 绘制文字
    ctx.save();
    ctx.translate(centerX, centerY);
    ctx.rotate(startAngle + anglePerPrize / 2);
    ctx.textAlign = 'center';
    ctx.fillStyle = '#fff';
    ctx.font = 'bold 16px Arial';
    ctx.shadowColor = 'rgba(0,0,0,0.5)';
    ctx.shadowBlur = 2;
    ctx.fillText(prize.text, radius * 0.7, 5);
    ctx.restore();
  });
}

function startRotation() {
  if (isSpinning.value) return;
  
  isSpinning.value = true;
  const selectedIndex = Math.floor(Math.random() * localPrizes.value.length);
  
  // 计算旋转角度
  const extraSpins = 8 + Math.random() * 3;
  const sectorAngle = 360 / localPrizes.value.length;
  const targetAngle = 360 * extraSpins - selectedIndex * sectorAngle;
  
  // 动画旋转
  const startTime = Date.now();
  const duration = 3000;
  const startRotation = currentRotation.value;
  
  function animate() {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // 使用缓动函数
    const easeOut = 1 - Math.pow(1 - progress, 3);
    currentRotation.value = startRotation + targetAngle * easeOut;
    
    drawWheel();
    
    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      // 旋转完成
      isSpinning.value = false;
      selectedPrize.value = localPrizes.value[selectedIndex];
    }
  }
  
  animate();
}

function updatePrizes() {
  emit('prizesChange', localPrizes.value);
  savePrizes();
  nextTick(() => {
    drawWheel();
  });
}

function addPrize() {
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
  const randomColor = colors[Math.floor(Math.random() * colors.length)];
  
  localPrizes.value.push({
    text: `Option ${localPrizes.value.length + 1}`,
    color: randomColor
  });
  
  updatePrizes();
}

function removePrize(index: number) {
  if (localPrizes.value.length > 2) {
    localPrizes.value.splice(index, 1);
    updatePrizes();
  }
}

function toggleSettings() {
  showSettings.value = !showSettings.value;
}

function savePrizes() {
  if (process.client && props.wheelId) {
    localStorage.setItem(`wheel-${props.wheelId}`, JSON.stringify(localPrizes.value));
  }
}

function loadSavedPrizes() {
  if (process.client && props.wheelId) {
    const saved = localStorage.getItem(`wheel-${props.wheelId}`);
    if (saved) {
      try {
        localPrizes.value = JSON.parse(saved);
      } catch (e) {
        console.warn('Failed to load saved prizes:', e);
      }
    }
  }
}
</script>

<style scoped>
.wheel-container {
  position: relative;
  display: inline-block;
}

.settings-panel {
  position: absolute;
  top: 0;
  right: -320px;
  width: 300px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  z-index: 10;
}

.settings-toggle {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255,255,255,0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  font-size: 18px;
  z-index: 5;
}

.settings-toggle.active {
  background: #3b82f6;
  color: white;
}

.prize-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.color-input {
  width: 40px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-remove {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
}
</style>
