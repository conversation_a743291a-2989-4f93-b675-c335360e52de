<template>
  <button
    @click="toggleTheme"
    class="theme-toggle"
    :title="themeTitle"
    aria-label="Toggle theme"
  >
    <span class="theme-icon">{{ themeIconText }}</span>
  </button>
</template>

<script setup lang="ts">
const colorMode = useColorMode();

const themeIconText = computed(() => {
  switch (colorMode.value) {
    case 'light':
      return '☀️';
    case 'dark':
      return '🌙';
    default:
      return '💻';
  }
});

const themeTitle = computed(() => {
  switch (colorMode.value) {
    case 'light':
      return 'Switch to dark mode';
    case 'dark':
      return 'Switch to system mode';
    default:
      return 'Switch to light mode';
  }
});

function toggleTheme() {
  switch (colorMode.value) {
    case 'light':
      colorMode.preference = 'dark';
      break;
    case 'dark':
      colorMode.preference = 'system';
      break;
    default:
      colorMode.preference = 'light';
      break;
  }
}
</script>

<style scoped>
.theme-toggle {
  @apply p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors duration-200;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-icon {
  @apply w-5 h-5 text-gray-600 dark:text-gray-400;
}
</style>
