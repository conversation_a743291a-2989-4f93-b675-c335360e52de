<template>
  <div class="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
    <!-- 头部导航 -->
    <header class="bg-white dark:bg-gray-800 shadow border-b border-gray-200 dark:border-gray-700">
      <div class="container mx-auto px-4 py-4 flex justify-between items-center">
        <NuxtLink to="/" class="flex text-lg font-bold mr-20 items-center">
          <img
            src="/logo.jpg"
            width="40"
            height="40"
            class="rounded mr-2"
            alt="Decisions Maker Online Logo"
          />
          <div class="text-gray-900 dark:text-gray-100">Decisions Maker Online</div>
        </NuxtLink>
        
        <div class="flex items-center space-x-4">
          <ThemeToggle />
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main>
      <slot />
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16">
      <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- 关于我们 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              About Decision Maker
            </h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              Make decisions easily with our collection of decision wheels. 
              Perfect for when you can't decide what to eat, what to do, or need to make fair selections.
            </p>
          </div>

          <!-- 快速链接 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Popular Wheels
            </h3>
            <ul class="space-y-2 text-sm">
              <li>
                <NuxtLink 
                  to="/yes-no-decision-maker" 
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  Yes or No Decision
                </NuxtLink>
              </li>
              <li>
                <NuxtLink 
                  to="/what-to-eat-decision-wheel" 
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  What to Eat
                </NuxtLink>
              </li>
              <li>
                <NuxtLink 
                  to="/weekend-activity-planner" 
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  Weekend Activities
                </NuxtLink>
              </li>
              <li>
                <NuxtLink 
                  to="/team-member-picker" 
                  class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  Team Picker
                </NuxtLink>
              </li>
            </ul>
          </div>

          <!-- 联系信息 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Contact
            </h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              Have suggestions or feedback? We'd love to hear from you!
            </p>
          </div>
        </div>

        <div class="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center">
          <p class="text-gray-500 dark:text-gray-400 text-sm">
            © {{ currentYear }} Decision Maker Online. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
const currentYear = new Date().getFullYear();
</script>

<style scoped>
/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>
