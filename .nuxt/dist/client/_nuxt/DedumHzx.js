const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./BQiff4QA.js","./HFvYc7_N.js","./fetch.DsrG8pny.css","./index.CVzQ5JNw.css","./DyjXTDH7.js","./_slug_.Cpn4lava.css"])))=>i.map(i=>d[i]);
var Tc=Object.defineProperty;var Cc=(e,t,n)=>t in e?Tc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Tt=(e,t,n)=>Cc(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ys(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ce={},un=[],it=()=>{},Rc=()=>!1,Xn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Xs=e=>e.startsWith("onUpdate:"),Ee=Object.assign,Qs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},xc=Object.prototype.hasOwnProperty,oe=(e,t)=>xc.call(e,t),J=Array.isArray,fn=e=>Qn(e)==="[object Map]",rl=e=>Qn(e)==="[object Set]",kc=e=>Qn(e)==="[object RegExp]",Y=e=>typeof e=="function",he=e=>typeof e=="string",bt=e=>typeof e=="symbol",ue=e=>e!==null&&typeof e=="object",Zs=e=>(ue(e)||Y(e))&&Y(e.then)&&Y(e.catch),sl=Object.prototype.toString,Qn=e=>sl.call(e),Pc=e=>Qn(e).slice(8,-1),ol=e=>Qn(e)==="[object Object]",eo=e=>he(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,dn=Ys(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),$r=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ac=/-(\w)/g,Ge=$r(e=>e.replace(Ac,(t,n)=>n?n.toUpperCase():"")),Oc=/\B([A-Z])/g,en=$r(e=>e.replace(Oc,"-$1").toLowerCase()),jr=$r(e=>e.charAt(0).toUpperCase()+e.slice(1)),zr=$r(e=>e?`on${jr(e)}`:""),It=(e,t)=>!Object.is(e,t),Ln=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ms=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Mc=e=>{const t=parseFloat(e);return isNaN(t)?e:t},il=e=>{const t=he(e)?Number(e):NaN;return isNaN(t)?e:t};let No;const Fr=()=>No||(No=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function to(e){if(J(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=he(r)?Nc(r):to(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(he(e)||ue(e))return e}const Lc=/;(?![^(]*\))/g,Ic=/:([^]+)/,Hc=/\/\*[^]*?\*\//g;function Nc(e){const t={};return e.replace(Hc,"").split(Lc).forEach(n=>{if(n){const r=n.split(Ic);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function no(e){let t="";if(he(e))t=e;else if(J(e))for(let n=0;n<e.length;n++){const r=no(e[n]);r&&(t+=r+" ")}else if(ue(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const $c="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",jc=Ys($c);function ll(e){return!!e||e===""}const al=e=>!!(e&&e.__v_isRef===!0),An=e=>he(e)?e:e==null?"":J(e)||ue(e)&&(e.toString===sl||!Y(e.toString))?al(e)?An(e.value):JSON.stringify(e,cl,2):String(e),cl=(e,t)=>al(t)?cl(e,t.value):fn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[Jr(r,o)+" =>"]=s,n),{})}:rl(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Jr(n))}:bt(t)?Jr(t):ue(t)&&!J(t)&&!ol(t)?String(t):t,Jr=(e,t="")=>{var n;return bt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ke;class ul{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ke,!t&&ke&&(this.index=(ke.scopes||(ke.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ke;try{return ke=this,t()}finally{ke=n}}}on(){++this._on===1&&(this.prevScope=ke,ke=this)}off(){this._on>0&&--this._on===0&&(ke=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function Fc(e){return new ul(e)}function fl(){return ke}function Fm(e,t=!1){ke&&ke.cleanups.push(e)}let ae;const Yr=new WeakSet;class dl{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ke&&ke.active&&ke.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Yr.has(this)&&(Yr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||pl(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,$o(this),gl(this);const t=ae,n=Ye;ae=this,Ye=!0;try{return this.fn()}finally{ml(this),ae=t,Ye=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)oo(t);this.deps=this.depsTail=void 0,$o(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Yr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ys(this)&&this.run()}get dirty(){return ys(this)}}let hl=0,In,Hn;function pl(e,t=!1){if(e.flags|=8,t){e.next=Hn,Hn=e;return}e.next=In,In=e}function ro(){hl++}function so(){if(--hl>0)return;if(Hn){let t=Hn;for(Hn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;In;){let t=In;for(In=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function gl(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ml(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),oo(r),Dc(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function ys(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(yl(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function yl(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Un)||(e.globalVersion=Un,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!ys(e))))return;e.flags|=2;const t=e.dep,n=ae,r=Ye;ae=e,Ye=!0;try{gl(e);const s=e.fn(e._value);(t.version===0||It(s,e._value))&&(e.flags|=128,e._value=s,t.version++)}catch(s){throw t.version++,s}finally{ae=n,Ye=r,ml(e),e.flags&=-3}}function oo(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)oo(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Dc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ye=!0;const _l=[];function mt(){_l.push(Ye),Ye=!1}function yt(){const e=_l.pop();Ye=e===void 0?!0:e}function $o(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ae;ae=void 0;try{t()}finally{ae=n}}}let Un=0;class Bc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class io{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!ae||!Ye||ae===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ae)n=this.activeLink=new Bc(ae,this),ae.deps?(n.prevDep=ae.depsTail,ae.depsTail.nextDep=n,ae.depsTail=n):ae.deps=ae.depsTail=n,vl(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=ae.depsTail,n.nextDep=void 0,ae.depsTail.nextDep=n,ae.depsTail=n,ae.deps===n&&(ae.deps=r)}return n}trigger(t){this.version++,Un++,this.notify(t)}notify(t){ro();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{so()}}}function vl(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)vl(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const br=new WeakMap,Wt=Symbol(""),_s=Symbol(""),Kn=Symbol("");function Pe(e,t,n){if(Ye&&ae){let r=br.get(e);r||br.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new io),s.map=r,s.key=n),s.track()}}function pt(e,t,n,r,s,o){const i=br.get(e);if(!i){Un++;return}const l=a=>{a&&a.trigger()};if(ro(),t==="clear")i.forEach(l);else{const a=J(e),f=a&&eo(n);if(a&&n==="length"){const c=Number(r);i.forEach((u,d)=>{(d==="length"||d===Kn||!bt(d)&&d>=c)&&l(u)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),f&&l(i.get(Kn)),t){case"add":a?f&&l(i.get("length")):(l(i.get(Wt)),fn(e)&&l(i.get(_s)));break;case"delete":a||(l(i.get(Wt)),fn(e)&&l(i.get(_s)));break;case"set":fn(e)&&l(i.get(Wt));break}}so()}function Uc(e,t){const n=br.get(e);return n&&n.get(t)}function sn(e){const t=te(e);return t===e?t:(Pe(t,"iterate",Kn),qe(e)?t:t.map(Te))}function Dr(e){return Pe(e=te(e),"iterate",Kn),e}const Kc={__proto__:null,[Symbol.iterator](){return Xr(this,Symbol.iterator,Te)},concat(...e){return sn(this).concat(...e.map(t=>J(t)?sn(t):t))},entries(){return Xr(this,"entries",e=>(e[1]=Te(e[1]),e))},every(e,t){return ct(this,"every",e,t,void 0,arguments)},filter(e,t){return ct(this,"filter",e,t,n=>n.map(Te),arguments)},find(e,t){return ct(this,"find",e,t,Te,arguments)},findIndex(e,t){return ct(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ct(this,"findLast",e,t,Te,arguments)},findLastIndex(e,t){return ct(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ct(this,"forEach",e,t,void 0,arguments)},includes(...e){return Qr(this,"includes",e)},indexOf(...e){return Qr(this,"indexOf",e)},join(e){return sn(this).join(e)},lastIndexOf(...e){return Qr(this,"lastIndexOf",e)},map(e,t){return ct(this,"map",e,t,void 0,arguments)},pop(){return Rn(this,"pop")},push(...e){return Rn(this,"push",e)},reduce(e,...t){return jo(this,"reduce",e,t)},reduceRight(e,...t){return jo(this,"reduceRight",e,t)},shift(){return Rn(this,"shift")},some(e,t){return ct(this,"some",e,t,void 0,arguments)},splice(...e){return Rn(this,"splice",e)},toReversed(){return sn(this).toReversed()},toSorted(e){return sn(this).toSorted(e)},toSpliced(...e){return sn(this).toSpliced(...e)},unshift(...e){return Rn(this,"unshift",e)},values(){return Xr(this,"values",Te)}};function Xr(e,t,n){const r=Dr(e),s=r[t]();return r!==e&&!qe(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const Vc=Array.prototype;function ct(e,t,n,r,s,o){const i=Dr(e),l=i!==e&&!qe(e),a=i[t];if(a!==Vc[t]){const u=a.apply(e,o);return l?Te(u):u}let f=n;i!==e&&(l?f=function(u,d){return n.call(this,Te(u),d,e)}:n.length>2&&(f=function(u,d){return n.call(this,u,d,e)}));const c=a.call(i,f,r);return l&&s?s(c):c}function jo(e,t,n,r){const s=Dr(e);let o=n;return s!==e&&(qe(e)?n.length>3&&(o=function(i,l,a){return n.call(this,i,l,a,e)}):o=function(i,l,a){return n.call(this,i,Te(l),a,e)}),s[t](o,...r)}function Qr(e,t,n){const r=te(e);Pe(r,"iterate",Kn);const s=r[t](...n);return(s===-1||s===!1)&&co(n[0])?(n[0]=te(n[0]),r[t](...n)):s}function Rn(e,t,n=[]){mt(),ro();const r=te(e)[t].apply(e,n);return so(),yt(),r}const Wc=Ys("__proto__,__v_isRef,__isVue"),bl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(bt));function qc(e){bt(e)||(e=String(e));const t=te(this);return Pe(t,"has",e),t.hasOwnProperty(e)}class wl{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?nu:Cl:o?Tl:Sl).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=J(t);if(!s){let a;if(i&&(a=Kc[n]))return a;if(n==="hasOwnProperty")return qc}const l=Reflect.get(t,n,ve(t)?t:r);return(bt(n)?bl.has(n):Wc(n))||(s||Pe(t,"get",n),o)?l:ve(l)?i&&eo(n)?l:l.value:ue(l)?s?Rl(l):wt(l):l}}class El extends wl{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const a=_t(o);if(!qe(r)&&!_t(r)&&(o=te(o),r=te(r)),!J(t)&&ve(o)&&!ve(r))return a?!1:(o.value=r,!0)}const i=J(t)&&eo(n)?Number(n)<t.length:oe(t,n),l=Reflect.set(t,n,r,ve(t)?t:s);return t===te(s)&&(i?It(r,o)&&pt(t,"set",n,r):pt(t,"add",n,r)),l}deleteProperty(t,n){const r=oe(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&pt(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!bt(n)||!bl.has(n))&&Pe(t,"has",n),r}ownKeys(t){return Pe(t,"iterate",J(t)?"length":Wt),Reflect.ownKeys(t)}}class Gc extends wl{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const zc=new El,Jc=new Gc,Yc=new El(!0);const vs=e=>e,sr=e=>Reflect.getPrototypeOf(e);function Xc(e,t,n){return function(...r){const s=this.__v_raw,o=te(s),i=fn(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,f=s[e](...r),c=n?vs:t?wr:Te;return!t&&Pe(o,"iterate",a?_s:Wt),{next(){const{value:u,done:d}=f.next();return d?{value:u,done:d}:{value:l?[c(u[0]),c(u[1])]:c(u),done:d}},[Symbol.iterator](){return this}}}}function or(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Qc(e,t){const n={get(s){const o=this.__v_raw,i=te(o),l=te(s);e||(It(s,l)&&Pe(i,"get",s),Pe(i,"get",l));const{has:a}=sr(i),f=t?vs:e?wr:Te;if(a.call(i,s))return f(o.get(s));if(a.call(i,l))return f(o.get(l));o!==i&&o.get(s)},get size(){const s=this.__v_raw;return!e&&Pe(te(s),"iterate",Wt),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,i=te(o),l=te(s);return e||(It(s,l)&&Pe(i,"has",s),Pe(i,"has",l)),s===l?o.has(s):o.has(s)||o.has(l)},forEach(s,o){const i=this,l=i.__v_raw,a=te(l),f=t?vs:e?wr:Te;return!e&&Pe(a,"iterate",Wt),l.forEach((c,u)=>s.call(o,f(c),f(u),i))}};return Ee(n,e?{add:or("add"),set:or("set"),delete:or("delete"),clear:or("clear")}:{add(s){!t&&!qe(s)&&!_t(s)&&(s=te(s));const o=te(this);return sr(o).has.call(o,s)||(o.add(s),pt(o,"add",s,s)),this},set(s,o){!t&&!qe(o)&&!_t(o)&&(o=te(o));const i=te(this),{has:l,get:a}=sr(i);let f=l.call(i,s);f||(s=te(s),f=l.call(i,s));const c=a.call(i,s);return i.set(s,o),f?It(o,c)&&pt(i,"set",s,o):pt(i,"add",s,o),this},delete(s){const o=te(this),{has:i,get:l}=sr(o);let a=i.call(o,s);a||(s=te(s),a=i.call(o,s)),l&&l.call(o,s);const f=o.delete(s);return a&&pt(o,"delete",s,void 0),f},clear(){const s=te(this),o=s.size!==0,i=s.clear();return o&&pt(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=Xc(s,e,t)}),n}function lo(e,t){const n=Qc(e,t);return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(oe(n,s)&&s in r?n:r,s,o)}const Zc={get:lo(!1,!1)},eu={get:lo(!1,!0)},tu={get:lo(!0,!1)};const Sl=new WeakMap,Tl=new WeakMap,Cl=new WeakMap,nu=new WeakMap;function ru(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function su(e){return e.__v_skip||!Object.isExtensible(e)?0:ru(Pc(e))}function wt(e){return _t(e)?e:ao(e,!1,zc,Zc,Sl)}function ot(e){return ao(e,!1,Yc,eu,Tl)}function Rl(e){return ao(e,!0,Jc,tu,Cl)}function ao(e,t,n,r,s){if(!ue(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=su(e);if(o===0)return e;const i=s.get(e);if(i)return i;const l=new Proxy(e,o===2?r:n);return s.set(e,l),l}function qt(e){return _t(e)?qt(e.__v_raw):!!(e&&e.__v_isReactive)}function _t(e){return!!(e&&e.__v_isReadonly)}function qe(e){return!!(e&&e.__v_isShallow)}function co(e){return e?!!e.__v_raw:!1}function te(e){const t=e&&e.__v_raw;return t?te(t):e}function ou(e){return!oe(e,"__v_skip")&&Object.isExtensible(e)&&ms(e,"__v_skip",!0),e}const Te=e=>ue(e)?wt(e):e,wr=e=>ue(e)?Rl(e):e;function ve(e){return e?e.__v_isRef===!0:!1}function lt(e){return xl(e,!1)}function Yt(e){return xl(e,!0)}function xl(e,t){return ve(e)?e:new iu(e,t)}class iu{constructor(t,n){this.dep=new io,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:te(t),this._value=n?t:Te(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||qe(t)||_t(t);t=r?t:te(t),It(t,n)&&(this._rawValue=t,this._value=r?t:Te(t),this.dep.trigger())}}function me(e){return ve(e)?e.value:e}function lu(e){return Y(e)?e():me(e)}const au={get:(e,t,n)=>t==="__v_raw"?e:me(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return ve(s)&&!ve(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function kl(e){return qt(e)?e:new Proxy(e,au)}class cu{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Uc(te(this._object),this._key)}}class uu{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Pl(e,t,n){return ve(e)?e:Y(e)?new uu(e):ue(e)&&arguments.length>1?fu(e,t,n):lt(e)}function fu(e,t,n){const r=e[t];return ve(r)?r:new cu(e,t,n)}class du{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new io(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Un-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ae!==this)return pl(this,!0),!0}get value(){const t=this.dep.track();return yl(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function hu(e,t,n=!1){let r,s;return Y(e)?r=e:(r=e.get,s=e.set),new du(r,s,n)}const ir={},Er=new WeakMap;let Kt;function pu(e,t=!1,n=Kt){if(n){let r=Er.get(n);r||Er.set(n,r=[]),r.push(e)}}function gu(e,t,n=ce){const{immediate:r,deep:s,once:o,scheduler:i,augmentJob:l,call:a}=n,f=v=>s?v:qe(v)||s===!1||s===0?Ot(v,1):Ot(v);let c,u,d,g,_=!1,y=!1;if(ve(e)?(u=()=>e.value,_=qe(e)):qt(e)?(u=()=>f(e),_=!0):J(e)?(y=!0,_=e.some(v=>qt(v)||qe(v)),u=()=>e.map(v=>{if(ve(v))return v.value;if(qt(v))return f(v);if(Y(v))return a?a(v,2):v()})):Y(e)?t?u=a?()=>a(e,2):e:u=()=>{if(d){mt();try{d()}finally{yt()}}const v=Kt;Kt=c;try{return a?a(e,3,[g]):e(g)}finally{Kt=v}}:u=it,t&&s){const v=u,b=s===!0?1/0:s;u=()=>Ot(v(),b)}const S=fl(),E=()=>{c.stop(),S&&S.active&&Qs(S.effects,c)};if(o&&t){const v=t;t=(...b)=>{v(...b),E()}}let w=y?new Array(e.length).fill(ir):ir;const p=v=>{if(!(!(c.flags&1)||!c.dirty&&!v))if(t){const b=c.run();if(s||_||(y?b.some((C,k)=>It(C,w[k])):It(b,w))){d&&d();const C=Kt;Kt=c;try{const k=[b,w===ir?void 0:y&&w[0]===ir?[]:w,g];w=b,a?a(t,3,k):t(...k)}finally{Kt=C}}}else c.run()};return l&&l(p),c=new dl(u),c.scheduler=i?()=>i(p,!1):p,g=v=>pu(v,!1,c),d=c.onStop=()=>{const v=Er.get(c);if(v){if(a)a(v,4);else for(const b of v)b();Er.delete(c)}},t?r?p(!0):w=c.run():i?i(p.bind(null,!0),!0):c.run(),E.pause=c.pause.bind(c),E.resume=c.resume.bind(c),E.stop=E,E}function Ot(e,t=1/0,n){if(t<=0||!ue(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ve(e))Ot(e.value,t,n);else if(J(e))for(let r=0;r<e.length;r++)Ot(e[r],t,n);else if(rl(e)||fn(e))e.forEach(r=>{Ot(r,t,n)});else if(ol(e)){for(const r in e)Ot(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Ot(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Zn(e,t,n,r){try{return r?e(...r):e()}catch(s){Sn(s,t,n)}}function Xe(e,t,n,r){if(Y(e)){const s=Zn(e,t,n,r);return s&&Zs(s)&&s.catch(o=>{Sn(o,t,n)}),s}if(J(e)){const s=[];for(let o=0;o<e.length;o++)s.push(Xe(e[o],t,n,r));return s}}function Sn(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ce;if(t){let l=t.parent;const a=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let u=0;u<c.length;u++)if(c[u](e,a,f)===!1)return}l=l.parent}if(o){mt(),Zn(o,null,10,[e,a,f]),yt();return}}mu(e,n,s,r,i)}function mu(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const Me=[];let rt=-1;const hn=[];let Rt=null,ln=0;const Al=Promise.resolve();let Sr=null;function yn(e){const t=Sr||Al;return e?t.then(this?e.bind(this):e):t}function yu(e){let t=rt+1,n=Me.length;for(;t<n;){const r=t+n>>>1,s=Me[r],o=Vn(s);o<e||o===e&&s.flags&2?t=r+1:n=r}return t}function uo(e){if(!(e.flags&1)){const t=Vn(e),n=Me[Me.length-1];!n||!(e.flags&2)&&t>=Vn(n)?Me.push(e):Me.splice(yu(t),0,e),e.flags|=1,Ol()}}function Ol(){Sr||(Sr=Al.then(Ml))}function bs(e){J(e)?hn.push(...e):Rt&&e.id===-1?Rt.splice(ln+1,0,e):e.flags&1||(hn.push(e),e.flags|=1),Ol()}function Fo(e,t,n=rt+1){for(;n<Me.length;n++){const r=Me[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Me.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Tr(e){if(hn.length){const t=[...new Set(hn)].sort((n,r)=>Vn(n)-Vn(r));if(hn.length=0,Rt){Rt.push(...t);return}for(Rt=t,ln=0;ln<Rt.length;ln++){const n=Rt[ln];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Rt=null,ln=0}}const Vn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ml(e){try{for(rt=0;rt<Me.length;rt++){const t=Me[rt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Zn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;rt<Me.length;rt++){const t=Me[rt];t&&(t.flags&=-2)}rt=-1,Me.length=0,Tr(),Sr=null,(Me.length||hn.length)&&Ml()}}let Ce=null,Ll=null;function Cr(e){const t=Ce;return Ce=e,Ll=e&&e.type.__scopeId||null,t}function Br(e,t=Ce,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&Qo(-1);const o=Cr(t);let i;try{i=e(...s)}finally{Cr(o),r._d&&Qo(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function st(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const l=s[i];o&&(l.oldValue=o[i].value);let a=l.dir[r];a&&(mt(),Xe(a,n,8,[e.el,l,e,t]),yt())}}const _u=Symbol("_vte"),Il=e=>e.__isTeleport,xt=Symbol("_leaveCb"),lr=Symbol("_enterCb");function vu(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Tn(()=>{e.isMounted=!0}),tn(()=>{e.isUnmounting=!0}),e}const Ue=[Function,Array],Hl={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ue,onEnter:Ue,onAfterEnter:Ue,onEnterCancelled:Ue,onBeforeLeave:Ue,onLeave:Ue,onAfterLeave:Ue,onLeaveCancelled:Ue,onBeforeAppear:Ue,onAppear:Ue,onAfterAppear:Ue,onAppearCancelled:Ue},Nl=e=>{const t=e.subTree;return t.component?Nl(t.component):t},bu={name:"BaseTransition",props:Hl,setup(e,{slots:t}){const n=tr(),r=vu();return()=>{const s=t.default&&Fl(t.default(),!0);if(!s||!s.length)return;const o=$l(s),i=te(e),{mode:l}=i;if(r.isLeaving)return Zr(o);const a=Do(o);if(!a)return Zr(o);let f=ws(a,i,r,n,u=>f=u);a.type!==_e&&_n(a,f);let c=n.subTree&&Do(n.subTree);if(c&&c.type!==_e&&!Je(a,c)&&Nl(n).type!==_e){let u=ws(c,i,r,n);if(_n(c,u),l==="out-in"&&a.type!==_e)return r.isLeaving=!0,u.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete u.afterLeave,c=void 0},Zr(o);l==="in-out"&&a.type!==_e?u.delayLeave=(d,g,_)=>{const y=jl(r,c);y[String(c.key)]=c,d[xt]=()=>{g(),d[xt]=void 0,delete f.delayedLeave,c=void 0},f.delayedLeave=()=>{_(),delete f.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return o}}};function $l(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==_e){t=n;break}}return t}const wu=bu;function jl(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function ws(e,t,n,r,s){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:a,onEnter:f,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:g,onAfterLeave:_,onLeaveCancelled:y,onBeforeAppear:S,onAppear:E,onAfterAppear:w,onAppearCancelled:p}=t,v=String(e.key),b=jl(n,e),C=(I,L)=>{I&&Xe(I,r,9,L)},k=(I,L)=>{const q=L[1];C(I,L),J(I)?I.every(M=>M.length<=1)&&q():I.length<=1&&q()},K={mode:i,persisted:l,beforeEnter(I){let L=a;if(!n.isMounted)if(o)L=S||a;else return;I[xt]&&I[xt](!0);const q=b[v];q&&Je(e,q)&&q.el[xt]&&q.el[xt](),C(L,[I])},enter(I){let L=f,q=c,M=u;if(!n.isMounted)if(o)L=E||f,q=w||c,M=p||u;else return;let G=!1;const Z=I[lr]=ne=>{G||(G=!0,ne?C(M,[I]):C(q,[I]),K.delayedLeave&&K.delayedLeave(),I[lr]=void 0)};L?k(L,[I,Z]):Z()},leave(I,L){const q=String(e.key);if(I[lr]&&I[lr](!0),n.isUnmounting)return L();C(d,[I]);let M=!1;const G=I[xt]=Z=>{M||(M=!0,L(),Z?C(y,[I]):C(_,[I]),I[xt]=void 0,b[q]===e&&delete b[q])};b[q]=e,g?k(g,[I,G]):G()},clone(I){const L=ws(I,t,n,r,s);return s&&s(L),L}};return K}function Zr(e){if(er(e))return e=vt(e),e.children=null,e}function Do(e){if(!er(e))return Il(e.type)&&e.children?$l(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&Y(n.default))return n.default()}}function _n(e,t){e.shapeFlag&6&&e.component?(e.transition=t,_n(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Fl(e,t=!1,n){let r=[],s=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===Ae?(i.patchFlag&128&&s++,r=r.concat(Fl(i.children,t,l))):(t||i.type!==_e)&&r.push(l!=null?vt(i,{key:l}):i)}if(s>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Ze(e,t){return Y(e)?Ee({name:e.name},t,{setup:e}):e}function fo(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function pn(e,t,n,r,s=!1){if(J(e)){e.forEach((_,y)=>pn(_,t&&(J(t)?t[y]:t),n,r,s));return}if(Ht(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&pn(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?bo(r.component):r.el,i=s?null:o,{i:l,r:a}=e,f=t&&t.r,c=l.refs===ce?l.refs={}:l.refs,u=l.setupState,d=te(u),g=u===ce?()=>!1:_=>oe(d,_);if(f!=null&&f!==a&&(he(f)?(c[f]=null,g(f)&&(u[f]=null)):ve(f)&&(f.value=null)),Y(a))Zn(a,l,12,[i,c]);else{const _=he(a),y=ve(a);if(_||y){const S=()=>{if(e.f){const E=_?g(a)?u[a]:c[a]:a.value;s?J(E)&&Qs(E,o):J(E)?E.includes(o)||E.push(o):_?(c[a]=[o],g(a)&&(u[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else _?(c[a]=i,g(a)&&(u[a]=i)):y&&(a.value=i,e.k&&(c[e.k]=i))};i?(S.id=-1,Se(S,n)):S()}}}let Bo=!1;const on=()=>{Bo||(console.error("Hydration completed but contains mismatches."),Bo=!0)},Eu=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",Su=e=>e.namespaceURI.includes("MathML"),ar=e=>{if(e.nodeType===1){if(Eu(e))return"svg";if(Su(e))return"mathml"}},cn=e=>e.nodeType===8;function Tu(e){const{mt:t,p:n,o:{patchProp:r,createText:s,nextSibling:o,parentNode:i,remove:l,insert:a,createComment:f}}=e,c=(p,v)=>{if(!v.hasChildNodes()){n(null,p,v),Tr(),v._vnode=p;return}u(v.firstChild,p,null,null,null),Tr(),v._vnode=p},u=(p,v,b,C,k,K=!1)=>{K=K||!!v.dynamicChildren;const I=cn(p)&&p.data==="[",L=()=>y(p,v,b,C,k,I),{type:q,ref:M,shapeFlag:G,patchFlag:Z}=v;let ne=p.nodeType;v.el=p,Z===-2&&(K=!1,v.dynamicChildren=null);let B=null;switch(q){case zt:ne!==3?v.children===""?(a(v.el=s(""),i(p),p),B=p):B=L():(p.data!==v.children&&(on(),p.data=v.children),B=o(p));break;case _e:w(p)?(B=o(p),E(v.el=p.content.firstChild,p,b)):ne!==8||I?B=L():B=o(p);break;case gr:if(I&&(p=o(p),ne=p.nodeType),ne===1||ne===3){B=p;const Q=!v.children.length;for(let U=0;U<v.staticCount;U++)Q&&(v.children+=B.nodeType===1?B.outerHTML:B.data),U===v.staticCount-1&&(v.anchor=B),B=o(B);return I?o(B):B}else L();break;case Ae:I?B=_(p,v,b,C,k,K):B=L();break;default:if(G&1)(ne!==1||v.type.toLowerCase()!==p.tagName.toLowerCase())&&!w(p)?B=L():B=d(p,v,b,C,k,K);else if(G&6){v.slotScopeIds=k;const Q=i(p);if(I?B=S(p):cn(p)&&p.data==="teleport start"?B=S(p,p.data,"teleport end"):B=o(p),t(v,Q,null,b,C,ar(Q),K),Ht(v)&&!v.type.__asyncResolved){let U;I?(U=de(Ae),U.anchor=B?B.previousSibling:Q.lastChild):U=p.nodeType===3?_o(""):de("div"),U.el=p,v.component.subTree=U}}else G&64?ne!==8?B=L():B=v.type.hydrate(p,v,b,C,k,K,e,g):G&128&&(B=v.type.hydrate(p,v,b,C,ar(i(p)),k,K,e,u))}return M!=null&&pn(M,null,C,v),B},d=(p,v,b,C,k,K)=>{K=K||!!v.dynamicChildren;const{type:I,props:L,patchFlag:q,shapeFlag:M,dirs:G,transition:Z}=v,ne=I==="input"||I==="option";if(ne||q!==-1){G&&st(v,null,b,"created");let B=!1;if(w(p)){B=aa(null,Z)&&b&&b.vnode.props&&b.vnode.props.appear;const U=p.content.firstChild;if(B){const pe=U.getAttribute("class");pe&&(U.$cls=pe),Z.beforeEnter(U)}E(U,p,b),v.el=p=U}if(M&16&&!(L&&(L.innerHTML||L.textContent))){let U=g(p.firstChild,v,p,b,C,k,K);for(;U;){cr(p,1)||on();const pe=U;U=U.nextSibling,l(pe)}}else if(M&8){let U=v.children;U[0]===`
`&&(p.tagName==="PRE"||p.tagName==="TEXTAREA")&&(U=U.slice(1)),p.textContent!==U&&(cr(p,0)||on(),p.textContent=v.children)}if(L){if(ne||!K||q&48){const U=p.tagName.includes("-");for(const pe in L)(ne&&(pe.endsWith("value")||pe==="indeterminate")||Xn(pe)&&!dn(pe)||pe[0]==="."||U)&&r(p,pe,null,L[pe],void 0,b)}else if(L.onClick)r(p,"onClick",null,L.onClick,void 0,b);else if(q&4&&qt(L.style))for(const U in L.style)L.style[U]}let Q;(Q=L&&L.onVnodeBeforeMount)&&Ne(Q,b,v),G&&st(v,null,b,"beforeMount"),((Q=L&&L.onVnodeMounted)||G||B)&&pa(()=>{Q&&Ne(Q,b,v),B&&Z.enter(p),G&&st(v,null,b,"mounted")},C)}return p.nextSibling},g=(p,v,b,C,k,K,I)=>{I=I||!!v.dynamicChildren;const L=v.children,q=L.length;for(let M=0;M<q;M++){const G=I?L[M]:L[M]=De(L[M]),Z=G.type===zt;p?(Z&&!I&&M+1<q&&De(L[M+1]).type===zt&&(a(s(p.data.slice(G.children.length)),b,o(p)),p.data=G.children),p=u(p,G,C,k,K,I)):Z&&!G.children?a(G.el=s(""),b):(cr(b,1)||on(),n(null,G,b,null,C,k,ar(b),K))}return p},_=(p,v,b,C,k,K)=>{const{slotScopeIds:I}=v;I&&(k=k?k.concat(I):I);const L=i(p),q=g(o(p),v,L,b,C,k,K);return q&&cn(q)&&q.data==="]"?o(v.anchor=q):(on(),a(v.anchor=f("]"),L,q),q)},y=(p,v,b,C,k,K)=>{if(cr(p.parentElement,1)||on(),v.el=null,K){const q=S(p);for(;;){const M=o(p);if(M&&M!==q)l(M);else break}}const I=o(p),L=i(p);return l(p),n(null,v,L,I,b,C,ar(L),k),b&&(b.vnode.el=v.el,Wr(b,v.el)),I},S=(p,v="[",b="]")=>{let C=0;for(;p;)if(p=o(p),p&&cn(p)&&(p.data===v&&C++,p.data===b)){if(C===0)return o(p);C--}return p},E=(p,v,b)=>{const C=v.parentNode;C&&C.replaceChild(p,v);let k=b;for(;k;)k.vnode.el===v&&(k.vnode.el=k.subTree.el=p),k=k.parent},w=p=>p.nodeType===1&&p.tagName==="TEMPLATE";return[c,u]}const Uo="data-allow-mismatch",Cu={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function cr(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(Uo);)e=e.parentElement;const n=e&&e.getAttribute(Uo);if(n==null)return!1;if(n==="")return!0;{const r=n.split(",");return t===0&&r.includes("children")?!0:r.includes(Cu[t])}}Fr().requestIdleCallback;Fr().cancelIdleCallback;function Ru(e,t){if(cn(e)&&e.data==="["){let n=1,r=e.nextSibling;for(;r;){if(r.nodeType===1){if(t(r)===!1)break}else if(cn(r))if(r.data==="]"){if(--n===0)break}else r.data==="["&&n++;r=r.nextSibling}}else t(e)}const Ht=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function xu(e){Y(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:r,delay:s=200,hydrate:o,timeout:i,suspensible:l=!0,onError:a}=e;let f=null,c,u=0;const d=()=>(u++,f=null,g()),g=()=>{let _;return f||(_=f=t().catch(y=>{if(y=y instanceof Error?y:new Error(String(y)),a)return new Promise((S,E)=>{a(y,()=>S(d()),()=>E(y),u+1)});throw y}).then(y=>_!==f&&f?f:(y&&(y.__esModule||y[Symbol.toStringTag]==="Module")&&(y=y.default),c=y,y)))};return Ze({name:"AsyncComponentWrapper",__asyncLoader:g,__asyncHydrate(_,y,S){const E=o?()=>{const p=o(()=>{S()},v=>Ru(_,v));p&&(y.bum||(y.bum=[])).push(p),(y.u||(y.u=[])).push(()=>!0)}:S;c?E():g().then(()=>!y.isUnmounted&&E())},get __asyncResolved(){return c},setup(){const _=be;if(fo(_),c)return()=>es(c,_);const y=p=>{f=null,Sn(p,_,13,!r)};if(l&&_.suspense||bn)return g().then(p=>()=>es(p,_)).catch(p=>(y(p),()=>r?de(r,{error:p}):null));const S=lt(!1),E=lt(),w=lt(!!s);return s&&setTimeout(()=>{w.value=!1},s),i!=null&&setTimeout(()=>{if(!S.value&&!E.value){const p=new Error(`Async component timed out after ${i}ms.`);y(p),E.value=p}},i),g().then(()=>{S.value=!0,_.parent&&er(_.parent.vnode)&&_.parent.update()}).catch(p=>{y(p),E.value=p}),()=>{if(S.value&&c)return es(c,_);if(E.value&&r)return de(r,{error:E.value});if(n&&!w.value)return de(n)}}})}function es(e,t){const{ref:n,props:r,children:s,ce:o}=t.vnode,i=de(e,r,s);return i.ref=n,i.ce=o,delete t.vnode.ce,i}const er=e=>e.type.__isKeepAlive,ku={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=tr(),r=n.ctx;if(!r.renderer)return()=>{const w=t.default&&t.default();return w&&w.length===1?w[0]:w};const s=new Map,o=new Set;let i=null;const l=n.suspense,{renderer:{p:a,m:f,um:c,o:{createElement:u}}}=r,d=u("div");r.activate=(w,p,v,b,C)=>{const k=w.component;f(w,p,v,0,l),a(k.vnode,w,p,v,k,l,b,w.slotScopeIds,C),Se(()=>{k.isDeactivated=!1,k.a&&Ln(k.a);const K=w.props&&w.props.onVnodeMounted;K&&Ne(K,k.parent,w)},l)},r.deactivate=w=>{const p=w.component;xr(p.m),xr(p.a),f(w,d,null,1,l),Se(()=>{p.da&&Ln(p.da);const v=w.props&&w.props.onVnodeUnmounted;v&&Ne(v,p.parent,w),p.isDeactivated=!0},l)};function g(w){ts(w),c(w,n,l,!0)}function _(w){s.forEach((p,v)=>{const b=As(p.type);b&&!w(b)&&y(v)})}function y(w){const p=s.get(w);p&&(!i||!Je(p,i))?g(p):i&&ts(i),s.delete(w),o.delete(w)}$t(()=>[e.include,e.exclude],([w,p])=>{w&&_(v=>On(w,v)),p&&_(v=>!On(p,v))},{flush:"post",deep:!0});let S=null;const E=()=>{S!=null&&(kr(n.subTree.type)?Se(()=>{s.set(S,ur(n.subTree))},n.subTree.suspense):s.set(S,ur(n.subTree)))};return Tn(E),Kl(E),tn(()=>{s.forEach(w=>{const{subTree:p,suspense:v}=n,b=ur(p);if(w.type===b.type&&w.key===b.key){ts(b);const C=b.component.da;C&&Se(C,v);return}g(w)})}),()=>{if(S=null,!t.default)return i=null;const w=t.default(),p=w[0];if(w.length>1)return i=null,w;if(!Xt(p)||!(p.shapeFlag&4)&&!(p.shapeFlag&128))return i=null,p;let v=ur(p);if(v.type===_e)return i=null,v;const b=v.type,C=As(Ht(v)?v.type.__asyncResolved||{}:b),{include:k,exclude:K,max:I}=e;if(k&&(!C||!On(k,C))||K&&C&&On(K,C))return v.shapeFlag&=-257,i=v,p;const L=v.key==null?b:v.key,q=s.get(L);return v.el&&(v=vt(v),p.shapeFlag&128&&(p.ssContent=v)),S=L,q?(v.el=q.el,v.component=q.component,v.transition&&_n(v,v.transition),v.shapeFlag|=512,o.delete(L),o.add(L)):(o.add(L),I&&o.size>parseInt(I,10)&&y(o.values().next().value)),v.shapeFlag|=256,i=v,kr(p.type)?p:v}}},Pu=ku;function On(e,t){return J(e)?e.some(n=>On(n,t)):he(e)?e.split(",").includes(t):kc(e)?(e.lastIndex=0,e.test(t)):!1}function Dl(e,t){Ul(e,"a",t)}function Bl(e,t){Ul(e,"da",t)}function Ul(e,t,n=be){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Ur(t,r,n),n){let s=n.parent;for(;s&&s.parent;)er(s.parent.vnode)&&Au(r,t,n,s),s=s.parent}}function Au(e,t,n,r){const s=Ur(t,e,r,!0);Vl(()=>{Qs(r[t],s)},n)}function ts(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function ur(e){return e.shapeFlag&128?e.ssContent:e}function Ur(e,t,n=be,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{mt();const l=Qt(n),a=Xe(t,n,e,i);return l(),yt(),a});return r?s.unshift(o):s.push(o),o}}const Et=e=>(t,n=be)=>{(!bn||e==="sp")&&Ur(e,(...r)=>t(...r),n)},Ou=Et("bm"),Tn=Et("m"),Mu=Et("bu"),Kl=Et("u"),tn=Et("bum"),Vl=Et("um"),Lu=Et("sp"),Iu=Et("rtg"),Hu=Et("rtc");function Wl(e,t=be){Ur("ec",e,t)}const ql="components";function Ko(e,t){return zl(ql,e,!0,t)||e}const Gl=Symbol.for("v-ndc");function Nu(e){return he(e)?zl(ql,e,!1)||e:e||Gl}function zl(e,t,n=!0,r=!1){const s=Ce||be;if(s){const o=s.type;{const l=As(o,!1);if(l&&(l===t||l===Ge(t)||l===jr(Ge(t))))return o}const i=Vo(s[e]||o[e],t)||Vo(s.appContext[e],t);return!i&&r?o:i}}function Vo(e,t){return e&&(e[t]||e[Ge(t)]||e[jr(Ge(t))])}function Dm(e,t,n,r){let s;const o=n,i=J(e);if(i||he(e)){const l=i&&qt(e);let a=!1,f=!1;l&&(a=!qe(e),f=_t(e),e=Dr(e)),s=new Array(e.length);for(let c=0,u=e.length;c<u;c++)s[c]=t(a?f?wr(Te(e[c])):Te(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){s=new Array(e);for(let l=0;l<e;l++)s[l]=t(l+1,l,void 0,o)}else if(ue(e))if(e[Symbol.iterator])s=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);s=new Array(l.length);for(let a=0,f=l.length;a<f;a++){const c=l[a];s[a]=t(e[c],c,a,o)}}else s=[];return s}function Bm(e,t,n={},r,s){if(Ce.ce||Ce.parent&&Ht(Ce.parent)&&Ce.parent.ce)return We(),Mt(Ae,null,[de("slot",n,r)],64);let o=e[t];o&&o._c&&(o._d=!1),We();const i=o&&Jl(o(n)),l=n.key||i&&i.key,a=Mt(Ae,{key:(l&&!bt(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),a}function Jl(e){return e.some(t=>Xt(t)?!(t.type===_e||t.type===Ae&&!Jl(t.children)):!0)?e:null}const Es=e=>e?va(e)?bo(e):Es(e.parent):null,Nn=Ee(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Es(e.parent),$root:e=>Es(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Xl(e),$forceUpdate:e=>e.f||(e.f=()=>{uo(e.update)}),$nextTick:e=>e.n||(e.n=yn.bind(e.proxy)),$watch:e=>rf.bind(e)}),ns=(e,t)=>e!==ce&&!e.__isScriptSetup&&oe(e,t),$u={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:l,appContext:a}=e;let f;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(ns(r,t))return i[t]=1,r[t];if(s!==ce&&oe(s,t))return i[t]=2,s[t];if((f=e.propsOptions[0])&&oe(f,t))return i[t]=3,o[t];if(n!==ce&&oe(n,t))return i[t]=4,n[t];Ss&&(i[t]=0)}}const c=Nn[t];let u,d;if(c)return t==="$attrs"&&Pe(e.attrs,"get",""),c(e);if((u=l.__cssModules)&&(u=u[t]))return u;if(n!==ce&&oe(n,t))return i[t]=4,n[t];if(d=a.config.globalProperties,oe(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return ns(s,t)?(s[t]=n,!0):r!==ce&&oe(r,t)?(r[t]=n,!0):oe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let l;return!!n[i]||e!==ce&&oe(e,i)||ns(t,i)||(l=o[0])&&oe(l,i)||oe(r,i)||oe(Nn,i)||oe(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:oe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Wo(e){return J(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function Um(e){const t=tr();let n=e();return ks(),Zs(n)&&(n=n.catch(r=>{throw Qt(t),r})),[n,()=>Qt(t)]}let Ss=!0;function ju(e){const t=Xl(e),n=e.proxy,r=e.ctx;Ss=!1,t.beforeCreate&&qo(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:l,provide:a,inject:f,created:c,beforeMount:u,mounted:d,beforeUpdate:g,updated:_,activated:y,deactivated:S,beforeDestroy:E,beforeUnmount:w,destroyed:p,unmounted:v,render:b,renderTracked:C,renderTriggered:k,errorCaptured:K,serverPrefetch:I,expose:L,inheritAttrs:q,components:M,directives:G,filters:Z}=t;if(f&&Fu(f,r,null),i)for(const Q in i){const U=i[Q];Y(U)&&(r[Q]=U.bind(n))}if(s){const Q=s.call(n,n);ue(Q)&&(e.data=wt(Q))}if(Ss=!0,o)for(const Q in o){const U=o[Q],pe=Y(U)?U.bind(n,n):Y(U.get)?U.get.bind(n,n):it,St=!Y(U)&&Y(U.set)?U.set.bind(n):it,et=ye({get:pe,set:St});Object.defineProperty(r,Q,{enumerable:!0,configurable:!0,get:()=>et.value,set:Ie=>et.value=Ie})}if(l)for(const Q in l)Yl(l[Q],r,n,Q);if(a){const Q=Y(a)?a.call(n):a;Reflect.ownKeys(Q).forEach(U=>{Nt(U,Q[U])})}c&&qo(c,e,"c");function B(Q,U){J(U)?U.forEach(pe=>Q(pe.bind(n))):U&&Q(U.bind(n))}if(B(Ou,u),B(Tn,d),B(Mu,g),B(Kl,_),B(Dl,y),B(Bl,S),B(Wl,K),B(Hu,C),B(Iu,k),B(tn,w),B(Vl,v),B(Lu,I),J(L))if(L.length){const Q=e.exposed||(e.exposed={});L.forEach(U=>{Object.defineProperty(Q,U,{get:()=>n[U],set:pe=>n[U]=pe})})}else e.exposed||(e.exposed={});b&&e.render===it&&(e.render=b),q!=null&&(e.inheritAttrs=q),M&&(e.components=M),G&&(e.directives=G),I&&fo(e)}function Fu(e,t,n=it){J(e)&&(e=Ts(e));for(const r in e){const s=e[r];let o;ue(s)?"default"in s?o=Re(s.from||r,s.default,!0):o=Re(s.from||r):o=Re(s),ve(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function qo(e,t,n){Xe(J(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Yl(e,t,n,r){let s=r.includes(".")?fa(n,r):()=>n[r];if(he(e)){const o=t[e];Y(o)&&$t(s,o)}else if(Y(e))$t(s,e.bind(n));else if(ue(e))if(J(e))e.forEach(o=>Yl(o,t,n,r));else{const o=Y(e.handler)?e.handler.bind(n):t[e.handler];Y(o)&&$t(s,o,e)}}function Xl(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!s.length&&!n&&!r?a=t:(a={},s.length&&s.forEach(f=>Rr(a,f,i,!0)),Rr(a,t,i)),ue(t)&&o.set(t,a),a}function Rr(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&Rr(e,o,n,!0),s&&s.forEach(i=>Rr(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const l=Du[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Du={data:Go,props:zo,emits:zo,methods:Mn,computed:Mn,beforeCreate:Oe,created:Oe,beforeMount:Oe,mounted:Oe,beforeUpdate:Oe,updated:Oe,beforeDestroy:Oe,beforeUnmount:Oe,destroyed:Oe,unmounted:Oe,activated:Oe,deactivated:Oe,errorCaptured:Oe,serverPrefetch:Oe,components:Mn,directives:Mn,watch:Uu,provide:Go,inject:Bu};function Go(e,t){return t?e?function(){return Ee(Y(e)?e.call(this,this):e,Y(t)?t.call(this,this):t)}:t:e}function Bu(e,t){return Mn(Ts(e),Ts(t))}function Ts(e){if(J(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Oe(e,t){return e?[...new Set([].concat(e,t))]:t}function Mn(e,t){return e?Ee(Object.create(null),e,t):t}function zo(e,t){return e?J(e)&&J(t)?[...new Set([...e,...t])]:Ee(Object.create(null),Wo(e),Wo(t??{})):t}function Uu(e,t){if(!e)return t;if(!t)return e;const n=Ee(Object.create(null),e);for(const r in t)n[r]=Oe(e[r],t[r]);return n}function Ql(){return{app:null,config:{isNativeTag:Rc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ku=0;function Vu(e,t){return function(r,s=null){Y(r)||(r=Ee({},r)),s!=null&&!ue(s)&&(s=null);const o=Ql(),i=new WeakSet,l=[];let a=!1;const f=o.app={_uid:Ku++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:kf,get config(){return o.config},set config(c){},use(c,...u){return i.has(c)||(c&&Y(c.install)?(i.add(c),c.install(f,...u)):Y(c)&&(i.add(c),c(f,...u))),f},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),f},component(c,u){return u?(o.components[c]=u,f):o.components[c]},directive(c,u){return u?(o.directives[c]=u,f):o.directives[c]},mount(c,u,d){if(!a){const g=f._ceVNode||de(r,s);return g.appContext=o,d===!0?d="svg":d===!1&&(d=void 0),u&&t?t(g,c):e(g,c,d),a=!0,f._container=c,c.__vue_app__=f,bo(g.component)}},onUnmount(c){l.push(c)},unmount(){a&&(Xe(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(c,u){return o.provides[c]=u,f},runWithContext(c){const u=Gt;Gt=f;try{return c()}finally{Gt=u}}};return f}}let Gt=null;function Nt(e,t){if(be){let n=be.provides;const r=be.parent&&be.parent.provides;r===n&&(n=be.provides=Object.create(r)),n[e]=t}}function Re(e,t,n=!1){const r=be||Ce;if(r||Gt){let s=Gt?Gt._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&Y(t)?t.call(r&&r.proxy):t}}function Kr(){return!!(be||Ce||Gt)}const Zl={},ea=()=>Object.create(Zl),ta=e=>Object.getPrototypeOf(e)===Zl;function Wu(e,t,n,r=!1){const s={},o=ea();e.propsDefaults=Object.create(null),na(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:ot(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function qu(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,l=te(s),[a]=e.propsOptions;let f=!1;if((r||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let u=0;u<c.length;u++){let d=c[u];if(Vr(e.emitsOptions,d))continue;const g=t[d];if(a)if(oe(o,d))g!==o[d]&&(o[d]=g,f=!0);else{const _=Ge(d);s[_]=Cs(a,l,_,g,e,!1)}else g!==o[d]&&(o[d]=g,f=!0)}}}else{na(e,t,s,o)&&(f=!0);let c;for(const u in l)(!t||!oe(t,u)&&((c=en(u))===u||!oe(t,c)))&&(a?n&&(n[u]!==void 0||n[c]!==void 0)&&(s[u]=Cs(a,l,u,void 0,e,!0)):delete s[u]);if(o!==l)for(const u in o)(!t||!oe(t,u))&&(delete o[u],f=!0)}f&&pt(e.attrs,"set","")}function na(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(dn(a))continue;const f=t[a];let c;s&&oe(s,c=Ge(a))?!o||!o.includes(c)?n[c]=f:(l||(l={}))[c]=f:Vr(e.emitsOptions,a)||(!(a in r)||f!==r[a])&&(r[a]=f,i=!0)}if(o){const a=te(n),f=l||ce;for(let c=0;c<o.length;c++){const u=o[c];n[u]=Cs(s,a,u,f[u],e,!oe(f,u))}}return i}function Cs(e,t,n,r,s,o){const i=e[n];if(i!=null){const l=oe(i,"default");if(l&&r===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&Y(a)){const{propsDefaults:f}=s;if(n in f)r=f[n];else{const c=Qt(s);r=f[n]=a.call(null,t),c()}}else r=a;s.ce&&s.ce._setProp(n,r)}i[0]&&(o&&!l?r=!1:i[1]&&(r===""||r===en(n))&&(r=!0))}return r}const Gu=new WeakMap;function ra(e,t,n=!1){const r=n?Gu:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},l=[];let a=!1;if(!Y(e)){const c=u=>{a=!0;const[d,g]=ra(u,t,!0);Ee(i,d),g&&l.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return ue(e)&&r.set(e,un),un;if(J(o))for(let c=0;c<o.length;c++){const u=Ge(o[c]);Jo(u)&&(i[u]=ce)}else if(o)for(const c in o){const u=Ge(c);if(Jo(u)){const d=o[c],g=i[u]=J(d)||Y(d)?{type:d}:Ee({},d),_=g.type;let y=!1,S=!0;if(J(_))for(let E=0;E<_.length;++E){const w=_[E],p=Y(w)&&w.name;if(p==="Boolean"){y=!0;break}else p==="String"&&(S=!1)}else y=Y(_)&&_.name==="Boolean";g[0]=y,g[1]=S,(y||oe(g,"default"))&&l.push(u)}}const f=[i,l];return ue(e)&&r.set(e,f),f}function Jo(e){return e[0]!=="$"&&!dn(e)}const ho=e=>e[0]==="_"||e==="$stable",po=e=>J(e)?e.map(De):[De(e)],zu=(e,t,n)=>{if(t._n)return t;const r=Br((...s)=>po(t(...s)),n);return r._c=!1,r},sa=(e,t,n)=>{const r=e._ctx;for(const s in e){if(ho(s))continue;const o=e[s];if(Y(o))t[s]=zu(s,o,r);else if(o!=null){const i=po(o);t[s]=()=>i}}},oa=(e,t)=>{const n=po(t);e.slots.default=()=>n},ia=(e,t,n)=>{for(const r in t)(n||!ho(r))&&(e[r]=t[r])},Ju=(e,t,n)=>{const r=e.slots=ea();if(e.vnode.shapeFlag&32){const s=t.__;s&&ms(r,"__",s,!0);const o=t._;o?(ia(r,t,n),n&&ms(r,"_",o,!0)):sa(t,r)}else t&&oa(e,t)},Yu=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=ce;if(r.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:ia(s,t,n):(o=!t.$stable,sa(t,s)),i=t}else t&&(oa(e,t),i={default:1});if(o)for(const l in s)!ho(l)&&i[l]==null&&delete s[l]},Se=pa;function Xu(e){return la(e)}function Qu(e){return la(e,Tu)}function la(e,t){const n=Fr();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:l,createComment:a,setText:f,setElementText:c,parentNode:u,nextSibling:d,setScopeId:g=it,insertStaticContent:_}=e,y=(h,m,T,P=null,R=null,A=null,$=void 0,N=null,H=!!m.dynamicChildren)=>{if(h===m)return;h&&!Je(h,m)&&(P=x(h),Ie(h,R,A,!0),h=null),m.patchFlag===-2&&(H=!1,m.dynamicChildren=null);const{type:O,ref:z,shapeFlag:F}=m;switch(O){case zt:S(h,m,T,P);break;case _e:E(h,m,T,P);break;case gr:h==null&&w(m,T,P,$);break;case Ae:M(h,m,T,P,R,A,$,N,H);break;default:F&1?b(h,m,T,P,R,A,$,N,H):F&6?G(h,m,T,P,R,A,$,N,H):(F&64||F&128)&&O.process(h,m,T,P,R,A,$,N,H,V)}z!=null&&R?pn(z,h&&h.ref,A,m||h,!m):z==null&&h&&h.ref!=null&&pn(h.ref,null,A,h,!0)},S=(h,m,T,P)=>{if(h==null)r(m.el=l(m.children),T,P);else{const R=m.el=h.el;m.children!==h.children&&f(R,m.children)}},E=(h,m,T,P)=>{h==null?r(m.el=a(m.children||""),T,P):m.el=h.el},w=(h,m,T,P)=>{[h.el,h.anchor]=_(h.children,m,T,P,h.el,h.anchor)},p=({el:h,anchor:m},T,P)=>{let R;for(;h&&h!==m;)R=d(h),r(h,T,P),h=R;r(m,T,P)},v=({el:h,anchor:m})=>{let T;for(;h&&h!==m;)T=d(h),s(h),h=T;s(m)},b=(h,m,T,P,R,A,$,N,H)=>{m.type==="svg"?$="svg":m.type==="math"&&($="mathml"),h==null?C(m,T,P,R,A,$,N,H):I(h,m,R,A,$,N,H)},C=(h,m,T,P,R,A,$,N)=>{let H,O;const{props:z,shapeFlag:F,transition:W,dirs:X}=h;if(H=h.el=i(h.type,A,z&&z.is,z),F&8?c(H,h.children):F&16&&K(h.children,H,null,P,R,rs(h,A),$,N),X&&st(h,null,P,"created"),k(H,h,h.scopeId,$,P),z){for(const le in z)le!=="value"&&!dn(le)&&o(H,le,null,z[le],A,P);"value"in z&&o(H,"value",null,z.value,A),(O=z.onVnodeBeforeMount)&&Ne(O,P,h)}X&&st(h,null,P,"beforeMount");const ee=aa(R,W);ee&&W.beforeEnter(H),r(H,m,T),((O=z&&z.onVnodeMounted)||ee||X)&&Se(()=>{O&&Ne(O,P,h),ee&&W.enter(H),X&&st(h,null,P,"mounted")},R)},k=(h,m,T,P,R)=>{if(T&&g(h,T),P)for(let A=0;A<P.length;A++)g(h,P[A]);if(R){let A=R.subTree;if(m===A||kr(A.type)&&(A.ssContent===m||A.ssFallback===m)){const $=R.vnode;k(h,$,$.scopeId,$.slotScopeIds,R.parent)}}},K=(h,m,T,P,R,A,$,N,H=0)=>{for(let O=H;O<h.length;O++){const z=h[O]=N?kt(h[O]):De(h[O]);y(null,z,m,T,P,R,A,$,N)}},I=(h,m,T,P,R,A,$)=>{const N=m.el=h.el;let{patchFlag:H,dynamicChildren:O,dirs:z}=m;H|=h.patchFlag&16;const F=h.props||ce,W=m.props||ce;let X;if(T&&Ft(T,!1),(X=W.onVnodeBeforeUpdate)&&Ne(X,T,m,h),z&&st(m,h,T,"beforeUpdate"),T&&Ft(T,!0),(F.innerHTML&&W.innerHTML==null||F.textContent&&W.textContent==null)&&c(N,""),O?L(h.dynamicChildren,O,N,T,P,rs(m,R),A):$||U(h,m,N,null,T,P,rs(m,R),A,!1),H>0){if(H&16)q(N,F,W,T,R);else if(H&2&&F.class!==W.class&&o(N,"class",null,W.class,R),H&4&&o(N,"style",F.style,W.style,R),H&8){const ee=m.dynamicProps;for(let le=0;le<ee.length;le++){const ie=ee[le],He=F[ie],xe=W[ie];(xe!==He||ie==="value")&&o(N,ie,He,xe,R,T)}}H&1&&h.children!==m.children&&c(N,m.children)}else!$&&O==null&&q(N,F,W,T,R);((X=W.onVnodeUpdated)||z)&&Se(()=>{X&&Ne(X,T,m,h),z&&st(m,h,T,"updated")},P)},L=(h,m,T,P,R,A,$)=>{for(let N=0;N<m.length;N++){const H=h[N],O=m[N],z=H.el&&(H.type===Ae||!Je(H,O)||H.shapeFlag&198)?u(H.el):T;y(H,O,z,null,P,R,A,$,!0)}},q=(h,m,T,P,R)=>{if(m!==T){if(m!==ce)for(const A in m)!dn(A)&&!(A in T)&&o(h,A,m[A],null,R,P);for(const A in T){if(dn(A))continue;const $=T[A],N=m[A];$!==N&&A!=="value"&&o(h,A,N,$,R,P)}"value"in T&&o(h,"value",m.value,T.value,R)}},M=(h,m,T,P,R,A,$,N,H)=>{const O=m.el=h?h.el:l(""),z=m.anchor=h?h.anchor:l("");let{patchFlag:F,dynamicChildren:W,slotScopeIds:X}=m;X&&(N=N?N.concat(X):X),h==null?(r(O,T,P),r(z,T,P),K(m.children||[],T,z,R,A,$,N,H)):F>0&&F&64&&W&&h.dynamicChildren?(L(h.dynamicChildren,W,T,R,A,$,N),(m.key!=null||R&&m===R.subTree)&&ca(h,m,!0)):U(h,m,T,z,R,A,$,N,H)},G=(h,m,T,P,R,A,$,N,H)=>{m.slotScopeIds=N,h==null?m.shapeFlag&512?R.ctx.activate(m,T,P,$,H):Z(m,T,P,R,A,$,H):ne(h,m,H)},Z=(h,m,T,P,R,A,$)=>{const N=h.component=Ef(h,P,R);if(er(h)&&(N.ctx.renderer=V),Sf(N,!1,$),N.asyncDep){if(R&&R.registerDep(N,B,$),!h.el){const H=N.subTree=de(_e);E(null,H,m,T)}}else B(N,h,m,T,R,A,$)},ne=(h,m,T)=>{const P=m.component=h.component;if(uf(h,m,T))if(P.asyncDep&&!P.asyncResolved){Q(P,m,T);return}else P.next=m,P.update();else m.el=h.el,P.vnode=m},B=(h,m,T,P,R,A,$)=>{const N=()=>{if(h.isMounted){let{next:F,bu:W,u:X,parent:ee,vnode:le}=h;{const je=ua(h);if(je){F&&(F.el=le.el,Q(h,F,$)),je.asyncDep.then(()=>{h.isUnmounted||N()});return}}let ie=F,He;Ft(h,!1),F?(F.el=le.el,Q(h,F,$)):F=le,W&&Ln(W),(He=F.props&&F.props.onVnodeBeforeUpdate)&&Ne(He,ee,F,le),Ft(h,!0);const xe=ss(h),ze=h.subTree;h.subTree=xe,y(ze,xe,u(ze.el),x(ze),h,R,A),F.el=xe.el,ie===null&&Wr(h,xe.el),X&&Se(X,R),(He=F.props&&F.props.onVnodeUpdated)&&Se(()=>Ne(He,ee,F,le),R)}else{let F;const{el:W,props:X}=m,{bm:ee,m:le,parent:ie,root:He,type:xe}=h,ze=Ht(m);if(Ft(h,!1),ee&&Ln(ee),!ze&&(F=X&&X.onVnodeBeforeMount)&&Ne(F,ie,m),Ft(h,!0),W&&fe){const je=()=>{h.subTree=ss(h),fe(W,h.subTree,h,R,null)};ze&&xe.__asyncHydrate?xe.__asyncHydrate(W,h,je):je()}else{He.ce&&He.ce._def.shadowRoot!==!1&&He.ce._injectChildStyle(xe);const je=h.subTree=ss(h);y(null,je,T,P,h,R,A),m.el=je.el}if(le&&Se(le,R),!ze&&(F=X&&X.onVnodeMounted)){const je=m;Se(()=>Ne(F,ie,je),R)}(m.shapeFlag&256||ie&&Ht(ie.vnode)&&ie.vnode.shapeFlag&256)&&h.a&&Se(h.a,R),h.isMounted=!0,m=T=P=null}};h.scope.on();const H=h.effect=new dl(N);h.scope.off();const O=h.update=H.run.bind(H),z=h.job=H.runIfDirty.bind(H);z.i=h,z.id=h.uid,H.scheduler=()=>uo(z),Ft(h,!0),O()},Q=(h,m,T)=>{m.component=h;const P=h.vnode.props;h.vnode=m,h.next=null,qu(h,m.props,P,T),Yu(h,m.children,T),mt(),Fo(h),yt()},U=(h,m,T,P,R,A,$,N,H=!1)=>{const O=h&&h.children,z=h?h.shapeFlag:0,F=m.children,{patchFlag:W,shapeFlag:X}=m;if(W>0){if(W&128){St(O,F,T,P,R,A,$,N,H);return}else if(W&256){pe(O,F,T,P,R,A,$,N,H);return}}X&8?(z&16&&Be(O,R,A),F!==O&&c(T,F)):z&16?X&16?St(O,F,T,P,R,A,$,N,H):Be(O,R,A,!0):(z&8&&c(T,""),X&16&&K(F,T,P,R,A,$,N,H))},pe=(h,m,T,P,R,A,$,N,H)=>{h=h||un,m=m||un;const O=h.length,z=m.length,F=Math.min(O,z);let W;for(W=0;W<F;W++){const X=m[W]=H?kt(m[W]):De(m[W]);y(h[W],X,T,null,R,A,$,N,H)}O>z?Be(h,R,A,!0,!1,F):K(m,T,P,R,A,$,N,H,F)},St=(h,m,T,P,R,A,$,N,H)=>{let O=0;const z=m.length;let F=h.length-1,W=z-1;for(;O<=F&&O<=W;){const X=h[O],ee=m[O]=H?kt(m[O]):De(m[O]);if(Je(X,ee))y(X,ee,T,null,R,A,$,N,H);else break;O++}for(;O<=F&&O<=W;){const X=h[F],ee=m[W]=H?kt(m[W]):De(m[W]);if(Je(X,ee))y(X,ee,T,null,R,A,$,N,H);else break;F--,W--}if(O>F){if(O<=W){const X=W+1,ee=X<z?m[X].el:P;for(;O<=W;)y(null,m[O]=H?kt(m[O]):De(m[O]),T,ee,R,A,$,N,H),O++}}else if(O>W)for(;O<=F;)Ie(h[O],R,A,!0),O++;else{const X=O,ee=O,le=new Map;for(O=ee;O<=W;O++){const Fe=m[O]=H?kt(m[O]):De(m[O]);Fe.key!=null&&le.set(Fe.key,O)}let ie,He=0;const xe=W-ee+1;let ze=!1,je=0;const Cn=new Array(xe);for(O=0;O<xe;O++)Cn[O]=0;for(O=X;O<=F;O++){const Fe=h[O];if(He>=xe){Ie(Fe,R,A,!0);continue}let tt;if(Fe.key!=null)tt=le.get(Fe.key);else for(ie=ee;ie<=W;ie++)if(Cn[ie-ee]===0&&Je(Fe,m[ie])){tt=ie;break}tt===void 0?Ie(Fe,R,A,!0):(Cn[tt-ee]=O+1,tt>=je?je=tt:ze=!0,y(Fe,m[tt],T,null,R,A,$,N,H),He++)}const Io=ze?Zu(Cn):un;for(ie=Io.length-1,O=xe-1;O>=0;O--){const Fe=ee+O,tt=m[Fe],Ho=Fe+1<z?m[Fe+1].el:P;Cn[O]===0?y(null,tt,T,Ho,R,A,$,N,H):ze&&(ie<0||O!==Io[ie]?et(tt,T,Ho,2):ie--)}}},et=(h,m,T,P,R=null)=>{const{el:A,type:$,transition:N,children:H,shapeFlag:O}=h;if(O&6){et(h.component.subTree,m,T,P);return}if(O&128){h.suspense.move(m,T,P);return}if(O&64){$.move(h,m,T,V);return}if($===Ae){r(A,m,T);for(let F=0;F<H.length;F++)et(H[F],m,T,P);r(h.anchor,m,T);return}if($===gr){p(h,m,T);return}if(P!==2&&O&1&&N)if(P===0)N.beforeEnter(A),r(A,m,T),Se(()=>N.enter(A),R);else{const{leave:F,delayLeave:W,afterLeave:X}=N,ee=()=>{h.ctx.isUnmounted?s(A):r(A,m,T)},le=()=>{F(A,()=>{ee(),X&&X()})};W?W(A,ee,le):le()}else r(A,m,T)},Ie=(h,m,T,P=!1,R=!1)=>{const{type:A,props:$,ref:N,children:H,dynamicChildren:O,shapeFlag:z,patchFlag:F,dirs:W,cacheIndex:X}=h;if(F===-2&&(R=!1),N!=null&&(mt(),pn(N,null,T,h,!0),yt()),X!=null&&(m.renderCache[X]=void 0),z&256){m.ctx.deactivate(h);return}const ee=z&1&&W,le=!Ht(h);let ie;if(le&&(ie=$&&$.onVnodeBeforeUnmount)&&Ne(ie,m,h),z&6)rr(h.component,T,P);else{if(z&128){h.suspense.unmount(T,P);return}ee&&st(h,null,m,"beforeUnmount"),z&64?h.type.remove(h,m,T,V,P):O&&!O.hasOnce&&(A!==Ae||F>0&&F&64)?Be(O,m,T,!1,!0):(A===Ae&&F&384||!R&&z&16)&&Be(H,m,T),P&&nn(h)}(le&&(ie=$&&$.onVnodeUnmounted)||ee)&&Se(()=>{ie&&Ne(ie,m,h),ee&&st(h,null,m,"unmounted")},T)},nn=h=>{const{type:m,el:T,anchor:P,transition:R}=h;if(m===Ae){rn(T,P);return}if(m===gr){v(h);return}const A=()=>{s(T),R&&!R.persisted&&R.afterLeave&&R.afterLeave()};if(h.shapeFlag&1&&R&&!R.persisted){const{leave:$,delayLeave:N}=R,H=()=>$(T,A);N?N(h.el,A,H):H()}else A()},rn=(h,m)=>{let T;for(;h!==m;)T=d(h),s(h),h=T;s(m)},rr=(h,m,T)=>{const{bum:P,scope:R,job:A,subTree:$,um:N,m:H,a:O,parent:z,slots:{__:F}}=h;xr(H),xr(O),P&&Ln(P),z&&J(F)&&F.forEach(W=>{z.renderCache[W]=void 0}),R.stop(),A&&(A.flags|=8,Ie($,h,m,T)),N&&Se(N,m),Se(()=>{h.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},Be=(h,m,T,P=!1,R=!1,A=0)=>{for(let $=A;$<h.length;$++)Ie(h[$],m,T,P,R)},x=h=>{if(h.shapeFlag&6)return x(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const m=d(h.anchor||h.el),T=m&&m[_u];return T?d(T):m};let D=!1;const j=(h,m,T)=>{h==null?m._vnode&&Ie(m._vnode,null,null,!0):y(m._vnode||null,h,m,null,null,null,T),m._vnode=h,D||(D=!0,Fo(),Tr(),D=!1)},V={p:y,um:Ie,m:et,r:nn,mt:Z,mc:K,pc:U,pbc:L,n:x,o:e};let re,fe;return t&&([re,fe]=t(V)),{render:j,hydrate:re,createApp:Vu(j,re)}}function rs({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ft({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function aa(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ca(e,t,n=!1){const r=e.children,s=t.children;if(J(r)&&J(s))for(let o=0;o<r.length;o++){const i=r[o];let l=s[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[o]=kt(s[o]),l.el=i.el),!n&&l.patchFlag!==-2&&ca(i,l)),l.type===zt&&(l.el=i.el),l.type===_e&&!l.el&&(l.el=i.el)}}function Zu(e){const t=e.slice(),n=[0];let r,s,o,i,l;const a=e.length;for(r=0;r<a;r++){const f=e[r];if(f!==0){if(s=n[n.length-1],e[s]<f){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<f?o=l+1:i=l;f<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function ua(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ua(t)}function xr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ef=Symbol.for("v-scx"),tf=()=>Re(ef);function nf(e,t){return go(e,null,t)}function $t(e,t,n){return go(e,t,n)}function go(e,t,n=ce){const{immediate:r,deep:s,flush:o,once:i}=n,l=Ee({},n),a=t&&r||!t&&o!=="post";let f;if(bn){if(o==="sync"){const g=tf();f=g.__watcherHandles||(g.__watcherHandles=[])}else if(!a){const g=()=>{};return g.stop=it,g.resume=it,g.pause=it,g}}const c=be;l.call=(g,_,y)=>Xe(g,c,_,y);let u=!1;o==="post"?l.scheduler=g=>{Se(g,c&&c.suspense)}:o!=="sync"&&(u=!0,l.scheduler=(g,_)=>{_?g():uo(g)}),l.augmentJob=g=>{t&&(g.flags|=4),u&&(g.flags|=2,c&&(g.id=c.uid,g.i=c))};const d=gu(e,t,l);return bn&&(f?f.push(d):a&&d()),d}function rf(e,t,n){const r=this.proxy,s=he(e)?e.includes(".")?fa(r,e):()=>r[e]:e.bind(r,r);let o;Y(t)?o=t:(o=t.handler,n=t);const i=Qt(this),l=go(s,o.bind(r),n);return i(),l}function fa(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const sf=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ge(t)}Modifiers`]||e[`${en(t)}Modifiers`];function of(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||ce;let s=n;const o=t.startsWith("update:"),i=o&&sf(r,t.slice(7));i&&(i.trim&&(s=n.map(c=>he(c)?c.trim():c)),i.number&&(s=n.map(Mc)));let l,a=r[l=zr(t)]||r[l=zr(Ge(t))];!a&&o&&(a=r[l=zr(en(t))]),a&&Xe(a,e,6,s);const f=r[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Xe(f,e,6,s)}}function da(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},l=!1;if(!Y(e)){const a=f=>{const c=da(f,t,!0);c&&(l=!0,Ee(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(ue(e)&&r.set(e,null),null):(J(o)?o.forEach(a=>i[a]=null):Ee(i,o),ue(e)&&r.set(e,i),i)}function Vr(e,t){return!e||!Xn(t)?!1:(t=t.slice(2).replace(/Once$/,""),oe(e,t[0].toLowerCase()+t.slice(1))||oe(e,en(t))||oe(e,t))}function ss(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:l,emit:a,render:f,renderCache:c,props:u,data:d,setupState:g,ctx:_,inheritAttrs:y}=e,S=Cr(e);let E,w;try{if(n.shapeFlag&4){const v=s||r,b=v;E=De(f.call(b,v,c,u,g,d,_)),w=l}else{const v=t;E=De(v.length>1?v(u,{attrs:l,slots:i,emit:a}):v(u,null)),w=t.props?l:af(l)}}catch(v){$n.length=0,Sn(v,e,1),E=de(_e)}let p=E;if(w&&y!==!1){const v=Object.keys(w),{shapeFlag:b}=p;v.length&&b&7&&(o&&v.some(Xs)&&(w=cf(w,o)),p=vt(p,w,!1,!0))}return n.dirs&&(p=vt(p,null,!1,!0),p.dirs=p.dirs?p.dirs.concat(n.dirs):n.dirs),n.transition&&_n(p,n.transition),E=p,Cr(S),E}function lf(e,t=!0){let n;for(let r=0;r<e.length;r++){const s=e[r];if(Xt(s)){if(s.type!==_e||s.children==="v-if"){if(n)return;n=s}}else return}return n}const af=e=>{let t;for(const n in e)(n==="class"||n==="style"||Xn(n))&&((t||(t={}))[n]=e[n]);return t},cf=(e,t)=>{const n={};for(const r in e)(!Xs(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function uf(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:l,patchFlag:a}=t,f=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return r?Yo(r,i,f):!!i;if(a&8){const c=t.dynamicProps;for(let u=0;u<c.length;u++){const d=c[u];if(i[d]!==r[d]&&!Vr(f,d))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:r===i?!1:r?i?Yo(r,i,f):!0:!!i;return!1}function Yo(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!Vr(n,o))return!0}return!1}function Wr({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const kr=e=>e.__isSuspense;let Rs=0;const ff={name:"Suspense",__isSuspense:!0,process(e,t,n,r,s,o,i,l,a,f){if(e==null)df(t,n,r,s,o,i,l,a,f);else{if(o&&o.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}hf(e,t,n,r,s,i,l,a,f)}},hydrate:pf,normalize:gf},mo=ff;function Wn(e,t){const n=e.props&&e.props[t];Y(n)&&n()}function df(e,t,n,r,s,o,i,l,a){const{p:f,o:{createElement:c}}=a,u=c("div"),d=e.suspense=ha(e,s,r,t,u,n,o,i,l,a);f(null,d.pendingBranch=e.ssContent,u,null,r,d,o,i),d.deps>0?(Wn(e,"onPending"),Wn(e,"onFallback"),f(null,e.ssFallback,t,n,r,null,o,i),gn(d,e.ssFallback)):d.resolve(!1,!0)}function hf(e,t,n,r,s,o,i,l,{p:a,um:f,o:{createElement:c}}){const u=t.suspense=e.suspense;u.vnode=t,t.el=e.el;const d=t.ssContent,g=t.ssFallback,{activeBranch:_,pendingBranch:y,isInFallback:S,isHydrating:E}=u;if(y)u.pendingBranch=d,Je(d,y)?(a(y,d,u.hiddenContainer,null,s,u,o,i,l),u.deps<=0?u.resolve():S&&(E||(a(_,g,n,r,s,null,o,i,l),gn(u,g)))):(u.pendingId=Rs++,E?(u.isHydrating=!1,u.activeBranch=y):f(y,s,u),u.deps=0,u.effects.length=0,u.hiddenContainer=c("div"),S?(a(null,d,u.hiddenContainer,null,s,u,o,i,l),u.deps<=0?u.resolve():(a(_,g,n,r,s,null,o,i,l),gn(u,g))):_&&Je(d,_)?(a(_,d,n,r,s,u,o,i,l),u.resolve(!0)):(a(null,d,u.hiddenContainer,null,s,u,o,i,l),u.deps<=0&&u.resolve()));else if(_&&Je(d,_))a(_,d,n,r,s,u,o,i,l),gn(u,d);else if(Wn(t,"onPending"),u.pendingBranch=d,d.shapeFlag&512?u.pendingId=d.component.suspenseId:u.pendingId=Rs++,a(null,d,u.hiddenContainer,null,s,u,o,i,l),u.deps<=0)u.resolve();else{const{timeout:w,pendingId:p}=u;w>0?setTimeout(()=>{u.pendingId===p&&u.fallback(g)},w):w===0&&u.fallback(g)}}function ha(e,t,n,r,s,o,i,l,a,f,c=!1){const{p:u,m:d,um:g,n:_,o:{parentNode:y,remove:S}}=f;let E;const w=mf(e);w&&t&&t.pendingBranch&&(E=t.pendingId,t.deps++);const p=e.props?il(e.props.timeout):void 0,v=o,b={vnode:e,parent:t,parentComponent:n,namespace:i,container:r,hiddenContainer:s,deps:0,pendingId:Rs++,timeout:typeof p=="number"?p:-1,activeBranch:null,pendingBranch:null,isInFallback:!c,isHydrating:c,isUnmounted:!1,effects:[],resolve(C=!1,k=!1){const{vnode:K,activeBranch:I,pendingBranch:L,pendingId:q,effects:M,parentComponent:G,container:Z}=b;let ne=!1;b.isHydrating?b.isHydrating=!1:C||(ne=I&&L.transition&&L.transition.mode==="out-in",ne&&(I.transition.afterLeave=()=>{q===b.pendingId&&(d(L,Z,o===v?_(I):o,0),bs(M))}),I&&(y(I.el)===Z&&(o=_(I)),g(I,G,b,!0)),ne||d(L,Z,o,0)),gn(b,L),b.pendingBranch=null,b.isInFallback=!1;let B=b.parent,Q=!1;for(;B;){if(B.pendingBranch){B.effects.push(...M),Q=!0;break}B=B.parent}!Q&&!ne&&bs(M),b.effects=[],w&&t&&t.pendingBranch&&E===t.pendingId&&(t.deps--,t.deps===0&&!k&&t.resolve()),Wn(K,"onResolve")},fallback(C){if(!b.pendingBranch)return;const{vnode:k,activeBranch:K,parentComponent:I,container:L,namespace:q}=b;Wn(k,"onFallback");const M=_(K),G=()=>{b.isInFallback&&(u(null,C,L,M,I,null,q,l,a),gn(b,C))},Z=C.transition&&C.transition.mode==="out-in";Z&&(K.transition.afterLeave=G),b.isInFallback=!0,g(K,I,null,!0),Z||G()},move(C,k,K){b.activeBranch&&d(b.activeBranch,C,k,K),b.container=C},next(){return b.activeBranch&&_(b.activeBranch)},registerDep(C,k,K){const I=!!b.pendingBranch;I&&b.deps++;const L=C.vnode.el;C.asyncDep.catch(q=>{Sn(q,C,0)}).then(q=>{if(C.isUnmounted||b.isUnmounted||b.pendingId!==C.suspenseId)return;C.asyncResolved=!0;const{vnode:M}=C;Ps(C,q),L&&(M.el=L);const G=!L&&C.subTree.el;k(C,M,y(L||C.subTree.el),L?null:_(C.subTree),b,i,K),G&&S(G),Wr(C,M.el),I&&--b.deps===0&&b.resolve()})},unmount(C,k){b.isUnmounted=!0,b.activeBranch&&g(b.activeBranch,n,C,k),b.pendingBranch&&g(b.pendingBranch,n,C,k)}};return b}function pf(e,t,n,r,s,o,i,l,a){const f=t.suspense=ha(t,r,n,e.parentNode,document.createElement("div"),null,s,o,i,l,!0),c=a(e,f.pendingBranch=t.ssContent,n,f,o,i);return f.deps===0&&f.resolve(!1,!0),c}function gf(e){const{shapeFlag:t,children:n}=e,r=t&32;e.ssContent=Xo(r?n.default:n),e.ssFallback=r?Xo(n.fallback):de(_e)}function Xo(e){let t;if(Y(e)){const n=vn&&e._c;n&&(e._d=!1,We()),e=e(),n&&(e._d=!0,t=$e,ga())}return J(e)&&(e=lf(e)),e=De(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(n=>n!==e)),e}function pa(e,t){t&&t.pendingBranch?J(e)?t.effects.push(...e):t.effects.push(e):bs(e)}function gn(e,t){e.activeBranch=t;const{vnode:n,parentComponent:r}=e;let s=t.el;for(;!s&&t.component;)t=t.component.subTree,s=t.el;n.el=s,r&&r.subTree===n&&(r.vnode.el=s,Wr(r,s))}function mf(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const Ae=Symbol.for("v-fgt"),zt=Symbol.for("v-txt"),_e=Symbol.for("v-cmt"),gr=Symbol.for("v-stc"),$n=[];let $e=null;function We(e=!1){$n.push($e=e?null:[])}function ga(){$n.pop(),$e=$n[$n.length-1]||null}let vn=1;function Qo(e,t=!1){vn+=e,e<0&&$e&&t&&($e.hasOnce=!0)}function ma(e){return e.dynamicChildren=vn>0?$e||un:null,ga(),vn>0&&$e&&$e.push(e),e}function yo(e,t,n,r,s,o){return ma(Ke(e,t,n,r,s,o,!0))}function Mt(e,t,n,r,s){return ma(de(e,t,n,r,s,!0))}function Xt(e){return e?e.__v_isVNode===!0:!1}function Je(e,t){return e.type===t.type&&e.key===t.key}const ya=({key:e})=>e??null,mr=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?he(e)||ve(e)||Y(e)?{i:Ce,r:e,k:t,f:!!n}:e:null);function Ke(e,t=null,n=null,r=0,s=null,o=e===Ae?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ya(t),ref:t&&mr(t),scopeId:Ll,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:Ce};return l?(vo(a,n),o&128&&e.normalize(a)):n&&(a.shapeFlag|=he(n)?8:16),vn>0&&!i&&$e&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&$e.push(a),a}const de=yf;function yf(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===Gl)&&(e=_e),Xt(e)){const l=vt(e,t,!0);return n&&vo(l,n),vn>0&&!o&&$e&&(l.shapeFlag&6?$e[$e.indexOf(e)]=l:$e.push(l)),l.patchFlag=-2,l}if(xf(e)&&(e=e.__vccOpts),t){t=_f(t);let{class:l,style:a}=t;l&&!he(l)&&(t.class=no(l)),ue(a)&&(co(a)&&!J(a)&&(a=Ee({},a)),t.style=to(a))}const i=he(e)?1:kr(e)?128:Il(e)?64:ue(e)?4:Y(e)?2:0;return Ke(e,t,n,r,s,i,o,!0)}function _f(e){return e?co(e)||ta(e)?Ee({},e):e:null}function vt(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:l,transition:a}=e,f=t?_a(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&ya(f),ref:t&&t.ref?n&&o?J(o)?o.concat(mr(t)):[o,mr(t)]:mr(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ae?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&vt(e.ssContent),ssFallback:e.ssFallback&&vt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&r&&_n(c,a.clone(c)),c}function _o(e=" ",t=0){return de(zt,null,e,t)}function vf(e="",t=!1){return t?(We(),Mt(_e,null,e)):de(_e,null,e)}function De(e){return e==null||typeof e=="boolean"?de(_e):J(e)?de(Ae,null,e.slice()):Xt(e)?kt(e):de(zt,null,String(e))}function kt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:vt(e)}function vo(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(J(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),vo(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!ta(t)?t._ctx=Ce:s===3&&Ce&&(Ce.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Y(t)?(t={default:t,_ctx:Ce},n=32):(t=String(t),r&64?(n=16,t=[_o(t)]):n=8);e.children=t,e.shapeFlag|=n}function _a(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=no([t.class,r.class]));else if(s==="style")t.style=to([t.style,r.style]);else if(Xn(s)){const o=t[s],i=r[s];i&&o!==i&&!(J(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function Ne(e,t,n,r=null){Xe(e,t,7,[n,r])}const bf=Ql();let wf=0;function Ef(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||bf,o={uid:wf++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ul(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ra(r,s),emitsOptions:da(r,s),emit:null,emitted:null,propsDefaults:ce,inheritAttrs:r.inheritAttrs,ctx:ce,data:ce,props:ce,attrs:ce,slots:ce,refs:ce,setupState:ce,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=of.bind(null,o),e.ce&&e.ce(o),o}let be=null;const tr=()=>be||Ce;let Pr,xs;{const e=Fr(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};Pr=t("__VUE_INSTANCE_SETTERS__",n=>be=n),xs=t("__VUE_SSR_SETTERS__",n=>bn=n)}const Qt=e=>{const t=be;return Pr(e),e.scope.on(),()=>{e.scope.off(),Pr(t)}},ks=()=>{be&&be.scope.off(),Pr(null)};function va(e){return e.vnode.shapeFlag&4}let bn=!1;function Sf(e,t=!1,n=!1){t&&xs(t);const{props:r,children:s}=e.vnode,o=va(e);Wu(e,r,o,t),Ju(e,s,n||t);const i=o?Tf(e,t):void 0;return t&&xs(!1),i}function Tf(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,$u);const{setup:r}=n;if(r){mt();const s=e.setupContext=r.length>1?Rf(e):null,o=Qt(e),i=Zn(r,e,0,[e.props,s]),l=Zs(i);if(yt(),o(),(l||e.sp)&&!Ht(e)&&fo(e),l){if(i.then(ks,ks),t)return i.then(a=>{Ps(e,a)}).catch(a=>{Sn(a,e,0)});e.asyncDep=i}else Ps(e,i)}else ba(e)}function Ps(e,t,n){Y(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ue(t)&&(e.setupState=kl(t)),ba(e)}function ba(e,t,n){const r=e.type;e.render||(e.render=r.render||it);{const s=Qt(e);mt();try{ju(e)}finally{yt(),s()}}}const Cf={get(e,t){return Pe(e,"get",""),e[t]}};function Rf(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Cf),slots:e.slots,emit:e.emit,expose:t}}function bo(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(kl(ou(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Nn)return Nn[n](e)},has(t,n){return n in t||n in Nn}})):e.proxy}function As(e,t=!0){return Y(e)?e.displayName||e.name:e.name||t&&e.__name}function xf(e){return Y(e)&&"__vccOpts"in e}const ye=(e,t)=>hu(e,t,bn);function we(e,t,n){const r=arguments.length;return r===2?ue(t)&&!J(t)?Xt(t)?de(e,null,[t]):de(e,t):de(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&Xt(n)&&(n=[n]),de(e,t,n))}const kf="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Os;const Zo=typeof window<"u"&&window.trustedTypes;if(Zo)try{Os=Zo.createPolicy("vue",{createHTML:e=>e})}catch{}const wa=Os?e=>Os.createHTML(e):e=>e,Pf="http://www.w3.org/2000/svg",Af="http://www.w3.org/1998/Math/MathML",ht=typeof document<"u"?document:null,ei=ht&&ht.createElement("template"),Of={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?ht.createElementNS(Pf,e):t==="mathml"?ht.createElementNS(Af,e):n?ht.createElement(e,{is:n}):ht.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>ht.createTextNode(e),createComment:e=>ht.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ht.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{ei.innerHTML=wa(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=ei.content;if(r==="svg"||r==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ct="transition",xn="animation",qn=Symbol("_vtc"),Ea={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Mf=Ee({},Hl,Ea),Lf=e=>(e.displayName="Transition",e.props=Mf,e),If=Lf((e,{slots:t})=>we(wu,Hf(e),t)),Dt=(e,t=[])=>{J(e)?e.forEach(n=>n(...t)):e&&e(...t)},ti=e=>e?J(e)?e.some(t=>t.length>1):e.length>1:!1;function Hf(e){const t={};for(const M in e)M in Ea||(t[M]=e[M]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=o,appearActiveClass:f=i,appearToClass:c=l,leaveFromClass:u=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,_=Nf(s),y=_&&_[0],S=_&&_[1],{onBeforeEnter:E,onEnter:w,onEnterCancelled:p,onLeave:v,onLeaveCancelled:b,onBeforeAppear:C=E,onAppear:k=w,onAppearCancelled:K=p}=t,I=(M,G,Z,ne)=>{M._enterCancelled=ne,Bt(M,G?c:l),Bt(M,G?f:i),Z&&Z()},L=(M,G)=>{M._isLeaving=!1,Bt(M,u),Bt(M,g),Bt(M,d),G&&G()},q=M=>(G,Z)=>{const ne=M?k:w,B=()=>I(G,M,Z);Dt(ne,[G,B]),ni(()=>{Bt(G,M?a:o),ut(G,M?c:l),ti(ne)||ri(G,r,y,B)})};return Ee(t,{onBeforeEnter(M){Dt(E,[M]),ut(M,o),ut(M,i)},onBeforeAppear(M){Dt(C,[M]),ut(M,a),ut(M,f)},onEnter:q(!1),onAppear:q(!0),onLeave(M,G){M._isLeaving=!0;const Z=()=>L(M,G);ut(M,u),M._enterCancelled?(ut(M,d),ii()):(ii(),ut(M,d)),ni(()=>{M._isLeaving&&(Bt(M,u),ut(M,g),ti(v)||ri(M,r,S,Z))}),Dt(v,[M,Z])},onEnterCancelled(M){I(M,!1,void 0,!0),Dt(p,[M])},onAppearCancelled(M){I(M,!0,void 0,!0),Dt(K,[M])},onLeaveCancelled(M){L(M),Dt(b,[M])}})}function Nf(e){if(e==null)return null;if(ue(e))return[os(e.enter),os(e.leave)];{const t=os(e);return[t,t]}}function os(e){return il(e)}function ut(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[qn]||(e[qn]=new Set)).add(t)}function Bt(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[qn];n&&(n.delete(t),n.size||(e[qn]=void 0))}function ni(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let $f=0;function ri(e,t,n,r){const s=e._endId=++$f,o=()=>{s===e._endId&&r()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:l,propCount:a}=jf(e,t);if(!i)return r();const f=i+"end";let c=0;const u=()=>{e.removeEventListener(f,d),o()},d=g=>{g.target===e&&++c>=a&&u()};setTimeout(()=>{c<a&&u()},l+1),e.addEventListener(f,d)}function jf(e,t){const n=window.getComputedStyle(e),r=_=>(n[_]||"").split(", "),s=r(`${Ct}Delay`),o=r(`${Ct}Duration`),i=si(s,o),l=r(`${xn}Delay`),a=r(`${xn}Duration`),f=si(l,a);let c=null,u=0,d=0;t===Ct?i>0&&(c=Ct,u=i,d=o.length):t===xn?f>0&&(c=xn,u=f,d=a.length):(u=Math.max(i,f),c=u>0?i>f?Ct:xn:null,d=c?c===Ct?o.length:a.length:0);const g=c===Ct&&/\b(transform|all)(,|$)/.test(r(`${Ct}Property`).toString());return{type:c,timeout:u,propCount:d,hasTransform:g}}function si(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>oi(n)+oi(e[r])))}function oi(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function ii(){return document.body.offsetHeight}function Ff(e,t,n){const r=e[qn];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const li=Symbol("_vod"),Df=Symbol("_vsh"),Bf=Symbol(""),Uf=/(^|;)\s*display\s*:/;function Kf(e,t,n){const r=e.style,s=he(n);let o=!1;if(n&&!s){if(t)if(he(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&yr(r,l,"")}else for(const i in t)n[i]==null&&yr(r,i,"");for(const i in n)i==="display"&&(o=!0),yr(r,i,n[i])}else if(s){if(t!==n){const i=r[Bf];i&&(n+=";"+i),r.cssText=n,o=Uf.test(n)}}else t&&e.removeAttribute("style");li in e&&(e[li]=o?r.display:"",e[Df]&&(r.display="none"))}const ai=/\s*!important$/;function yr(e,t,n){if(J(n))n.forEach(r=>yr(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Vf(e,t);ai.test(n)?e.setProperty(en(r),n.replace(ai,""),"important"):e[r]=n}}const ci=["Webkit","Moz","ms"],is={};function Vf(e,t){const n=is[t];if(n)return n;let r=Ge(t);if(r!=="filter"&&r in e)return is[t]=r;r=jr(r);for(let s=0;s<ci.length;s++){const o=ci[s]+r;if(o in e)return is[t]=o}return t}const ui="http://www.w3.org/1999/xlink";function fi(e,t,n,r,s,o=jc(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ui,t.slice(6,t.length)):e.setAttributeNS(ui,t,n):n==null||o&&!ll(n)?e.removeAttribute(t):e.setAttribute(t,o?"":bt(n)?String(n):n)}function di(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?wa(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=ll(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function Wf(e,t,n,r){e.addEventListener(t,n,r)}function qf(e,t,n,r){e.removeEventListener(t,n,r)}const hi=Symbol("_vei");function Gf(e,t,n,r,s=null){const o=e[hi]||(e[hi]={}),i=o[t];if(r&&i)i.value=r;else{const[l,a]=zf(t);if(r){const f=o[t]=Xf(r,s);Wf(e,l,f,a)}else i&&(qf(e,l,i,a),o[t]=void 0)}}const pi=/(?:Once|Passive|Capture)$/;function zf(e){let t;if(pi.test(e)){t={};let r;for(;r=e.match(pi);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):en(e.slice(2)),t]}let ls=0;const Jf=Promise.resolve(),Yf=()=>ls||(Jf.then(()=>ls=0),ls=Date.now());function Xf(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Xe(Qf(r,n.value),t,5,[r])};return n.value=e,n.attached=Yf(),n}function Qf(e,t){if(J(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const gi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Zf=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?Ff(e,r,i):t==="style"?Kf(e,n,r):Xn(t)?Xs(t)||Gf(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ed(e,t,r,i))?(di(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&fi(e,t,r,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!he(r))?di(e,Ge(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),fi(e,t,r,i))};function ed(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&gi(t)&&Y(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return gi(t)&&he(n)?!1:t in e}const Sa=Ee({patchProp:Zf},Of);let jn,mi=!1;function td(){return jn||(jn=Xu(Sa))}function nd(){return jn=mi?jn:Qu(Sa),mi=!0,jn}const rd=(...e)=>{const t=td().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=Ca(r);if(!s)return;const o=t._component;!Y(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,Ta(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t},sd=(...e)=>{const t=nd().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=Ca(r);if(s)return n(s,!0,Ta(s))},t};function Ta(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Ca(e){return he(e)?document.querySelector(e):e}const od=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,id=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,ld=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function ad(e,t){if(e==="__proto__"||e==="constructor"&&t&&typeof t=="object"&&"prototype"in t){cd(e);return}return t}function cd(e){console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)}function Ar(e,t={}){if(typeof e!="string")return e;if(e[0]==='"'&&e[e.length-1]==='"'&&e.indexOf("\\")===-1)return e.slice(1,-1);const n=e.trim();if(n.length<=9)switch(n.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!ld.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(od.test(e)||id.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,ad)}return JSON.parse(e)}catch(r){if(t.strict)throw r;return e}}const ud=/#/g,fd=/&/g,dd=/\//g,hd=/=/g,wo=/\+/g,pd=/%5e/gi,gd=/%60/gi,md=/%7c/gi,yd=/%20/gi;function _d(e){return encodeURI(""+e).replace(md,"|")}function Ms(e){return _d(typeof e=="string"?e:JSON.stringify(e)).replace(wo,"%2B").replace(yd,"+").replace(ud,"%23").replace(fd,"%26").replace(gd,"`").replace(pd,"^").replace(dd,"%2F")}function as(e){return Ms(e).replace(hd,"%3D")}function Or(e=""){try{return decodeURIComponent(""+e)}catch{return""+e}}function vd(e){return Or(e.replace(wo," "))}function bd(e){return Or(e.replace(wo," "))}function Eo(e=""){const t=Object.create(null);e[0]==="?"&&(e=e.slice(1));for(const n of e.split("&")){const r=n.match(/([^=]+)=?(.*)/)||[];if(r.length<2)continue;const s=vd(r[1]);if(s==="__proto__"||s==="constructor")continue;const o=bd(r[2]||"");t[s]===void 0?t[s]=o:Array.isArray(t[s])?t[s].push(o):t[s]=[t[s],o]}return t}function wd(e,t){return(typeof t=="number"||typeof t=="boolean")&&(t=String(t)),t?Array.isArray(t)?t.map(n=>`${as(e)}=${Ms(n)}`).join("&"):`${as(e)}=${Ms(t)}`:as(e)}function Ed(e){return Object.keys(e).filter(t=>e[t]!==void 0).map(t=>wd(t,e[t])).filter(Boolean).join("&")}const Sd=/^[\s\w\0+.-]{2,}:([/\\]{1,2})/,Td=/^[\s\w\0+.-]{2,}:([/\\]{2})?/,Cd=/^([/\\]\s*){2,}[^/\\]/,Rd=/^[\s\0]*(blob|data|javascript|vbscript):$/i,xd=/\/$|\/\?|\/#/,kd=/^\.?\//;function jt(e,t={}){return typeof t=="boolean"&&(t={acceptRelative:t}),t.strict?Sd.test(e):Td.test(e)||(t.acceptRelative?Cd.test(e):!1)}function Pd(e){return!!e&&Rd.test(e)}function Ls(e="",t){return t?xd.test(e):e.endsWith("/")}function Gn(e="",t){if(!t)return(Ls(e)?e.slice(0,-1):e)||"/";if(!Ls(e,!0))return e||"/";let n=e,r="";const s=e.indexOf("#");s!==-1&&(n=e.slice(0,s),r=e.slice(s));const[o,...i]=n.split("?");return((o.endsWith("/")?o.slice(0,-1):o)||"/")+(i.length>0?`?${i.join("?")}`:"")+r}function Ra(e="",t){if(!t)return e.endsWith("/")?e:e+"/";if(Ls(e,!0))return e||"/";let n=e,r="";const s=e.indexOf("#");if(s!==-1&&(n=e.slice(0,s),r=e.slice(s),!n))return r;const[o,...i]=n.split("?");return o+"/"+(i.length>0?`?${i.join("?")}`:"")+r}function Ad(e,t){if(ka(t)||jt(e))return e;const n=Gn(t);return e.startsWith(n)?e:So(n,e)}function yi(e,t){if(ka(t))return e;const n=Gn(t);if(!e.startsWith(n))return e;const r=e.slice(n.length);return r[0]==="/"?r:"/"+r}function xa(e,t){const n=Oa(e),r={...Eo(n.search),...t};return n.search=Ed(r),Ld(n)}function ka(e){return!e||e==="/"}function Od(e){return e&&e!=="/"}function So(e,...t){let n=e||"";for(const r of t.filter(s=>Od(s)))if(n){const s=r.replace(kd,"");n=Ra(n)+s}else n=r;return n}function Pa(...e){var i,l,a,f;const t=/\/(?!\/)/,n=e.filter(Boolean),r=[];let s=0;for(const c of n)if(!(!c||c==="/")){for(const[u,d]of c.split(t).entries())if(!(!d||d===".")){if(d===".."){if(r.length===1&&jt(r[0]))continue;r.pop(),s--;continue}if(u===1&&((i=r[r.length-1])!=null&&i.endsWith(":/"))){r[r.length-1]+="/"+d;continue}r.push(d),s++}}let o=r.join("/");return s>=0?(l=n[0])!=null&&l.startsWith("/")&&!o.startsWith("/")?o="/"+o:(a=n[0])!=null&&a.startsWith("./")&&!o.startsWith("./")&&(o="./"+o):o="../".repeat(-1*s)+o,(f=n[n.length-1])!=null&&f.endsWith("/")&&!o.endsWith("/")&&(o+="/"),o}function Md(e,t){return Or(Gn(e))===Or(Gn(t))}const Aa=Symbol.for("ufo:protocolRelative");function Oa(e="",t){const n=e.match(/^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(n){const[,u,d=""]=n;return{protocol:u.toLowerCase(),pathname:d,href:u+d,auth:"",host:"",search:"",hash:""}}if(!jt(e,{acceptRelative:!0}))return _i(e);const[,r="",s,o=""]=e.replace(/\\/g,"/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/)||[];let[,i="",l=""]=o.match(/([^#/?]*)(.*)?/)||[];r==="file:"&&(l=l.replace(/\/(?=[A-Za-z]:)/,""));const{pathname:a,search:f,hash:c}=_i(l);return{protocol:r.toLowerCase(),auth:s?s.slice(0,Math.max(0,s.length-1)):"",host:i,pathname:a,search:f,hash:c,[Aa]:!r}}function _i(e=""){const[t="",n="",r=""]=(e.match(/([^#?]*)(\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:n,hash:r}}function Ld(e){const t=e.pathname||"",n=e.search?(e.search.startsWith("?")?"":"?")+e.search:"",r=e.hash||"",s=e.auth?e.auth+"@":"",o=e.host||"";return(e.protocol||e[Aa]?(e.protocol||"")+"//":"")+s+o+t+n+r}class Id extends Error{constructor(t,n){super(t,n),this.name="FetchError",n!=null&&n.cause&&!this.cause&&(this.cause=n.cause)}}function Hd(e){var a,f,c,u,d;const t=((a=e.error)==null?void 0:a.message)||((f=e.error)==null?void 0:f.toString())||"",n=((c=e.request)==null?void 0:c.method)||((u=e.options)==null?void 0:u.method)||"GET",r=((d=e.request)==null?void 0:d.url)||String(e.request)||"/",s=`[${n}] ${JSON.stringify(r)}`,o=e.response?`${e.response.status} ${e.response.statusText}`:"<no response>",i=`${s}: ${o}${t?` ${t}`:""}`,l=new Id(i,e.error?{cause:e.error}:void 0);for(const g of["request","options","response"])Object.defineProperty(l,g,{get(){return e[g]}});for(const[g,_]of[["data","_data"],["status","status"],["statusCode","status"],["statusText","statusText"],["statusMessage","statusText"]])Object.defineProperty(l,g,{get(){return e.response&&e.response[_]}});return l}const Nd=new Set(Object.freeze(["PATCH","POST","PUT","DELETE"]));function vi(e="GET"){return Nd.has(e.toUpperCase())}function $d(e){if(e===void 0)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"||t===null?!0:t!=="object"?!1:Array.isArray(e)?!0:e.buffer?!1:e.constructor&&e.constructor.name==="Object"||typeof e.toJSON=="function"}const jd=new Set(["image/svg","application/xml","application/xhtml","application/html"]),Fd=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function Dd(e=""){if(!e)return"json";const t=e.split(";").shift()||"";return Fd.test(t)?"json":jd.has(t)||t.startsWith("text/")?"text":"blob"}function Bd(e,t,n,r){const s=Ud((t==null?void 0:t.headers)??(e==null?void 0:e.headers),n==null?void 0:n.headers,r);let o;return(n!=null&&n.query||n!=null&&n.params||t!=null&&t.params||t!=null&&t.query)&&(o={...n==null?void 0:n.params,...n==null?void 0:n.query,...t==null?void 0:t.params,...t==null?void 0:t.query}),{...n,...t,query:o,params:o,headers:s}}function Ud(e,t,n){if(!t)return new n(e);const r=new n(t);if(e)for(const[s,o]of Symbol.iterator in e||Array.isArray(e)?e:new n(e))r.set(s,o);return r}async function fr(e,t){if(t)if(Array.isArray(t))for(const n of t)await n(e);else await t(e)}const Kd=new Set([408,409,425,429,500,502,503,504]),Vd=new Set([101,204,205,304]);function Ma(e={}){const{fetch:t=globalThis.fetch,Headers:n=globalThis.Headers,AbortController:r=globalThis.AbortController}=e;async function s(l){const a=l.error&&l.error.name==="AbortError"&&!l.options.timeout||!1;if(l.options.retry!==!1&&!a){let c;typeof l.options.retry=="number"?c=l.options.retry:c=vi(l.options.method)?0:1;const u=l.response&&l.response.status||500;if(c>0&&(Array.isArray(l.options.retryStatusCodes)?l.options.retryStatusCodes.includes(u):Kd.has(u))){const d=typeof l.options.retryDelay=="function"?l.options.retryDelay(l):l.options.retryDelay||0;return d>0&&await new Promise(g=>setTimeout(g,d)),o(l.request,{...l.options,retry:c-1})}}const f=Hd(l);throw Error.captureStackTrace&&Error.captureStackTrace(f,o),f}const o=async function(a,f={}){const c={request:a,options:Bd(a,f,e.defaults,n),response:void 0,error:void 0};c.options.method&&(c.options.method=c.options.method.toUpperCase()),c.options.onRequest&&await fr(c,c.options.onRequest),typeof c.request=="string"&&(c.options.baseURL&&(c.request=Ad(c.request,c.options.baseURL)),c.options.query&&(c.request=xa(c.request,c.options.query),delete c.options.query),"query"in c.options&&delete c.options.query,"params"in c.options&&delete c.options.params),c.options.body&&vi(c.options.method)&&($d(c.options.body)?(c.options.body=typeof c.options.body=="string"?c.options.body:JSON.stringify(c.options.body),c.options.headers=new n(c.options.headers||{}),c.options.headers.has("content-type")||c.options.headers.set("content-type","application/json"),c.options.headers.has("accept")||c.options.headers.set("accept","application/json")):("pipeTo"in c.options.body&&typeof c.options.body.pipeTo=="function"||typeof c.options.body.pipe=="function")&&("duplex"in c.options||(c.options.duplex="half")));let u;if(!c.options.signal&&c.options.timeout){const g=new r;u=setTimeout(()=>{const _=new Error("[TimeoutError]: The operation was aborted due to timeout");_.name="TimeoutError",_.code=23,g.abort(_)},c.options.timeout),c.options.signal=g.signal}try{c.response=await t(c.request,c.options)}catch(g){return c.error=g,c.options.onRequestError&&await fr(c,c.options.onRequestError),await s(c)}finally{u&&clearTimeout(u)}if((c.response.body||c.response._bodyInit)&&!Vd.has(c.response.status)&&c.options.method!=="HEAD"){const g=(c.options.parseResponse?"json":c.options.responseType)||Dd(c.response.headers.get("content-type")||"");switch(g){case"json":{const _=await c.response.text(),y=c.options.parseResponse||Ar;c.response._data=y(_);break}case"stream":{c.response._data=c.response.body||c.response._bodyInit;break}default:c.response._data=await c.response[g]()}}return c.options.onResponse&&await fr(c,c.options.onResponse),!c.options.ignoreResponseError&&c.response.status>=400&&c.response.status<600?(c.options.onResponseError&&await fr(c,c.options.onResponseError),await s(c)):c.response},i=async function(a,f){return(await o(a,f))._data};return i.raw=o,i.native=(...l)=>t(...l),i.create=(l={},a={})=>Ma({...e,...a,defaults:{...e.defaults,...a.defaults,...l}}),i}const Mr=function(){if(typeof globalThis<"u")return globalThis;if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")}(),Wd=Mr.fetch?(...e)=>Mr.fetch(...e):()=>Promise.reject(new Error("[ofetch] global.fetch is not supported!")),qd=Mr.Headers,Gd=Mr.AbortController,zd=Ma({fetch:Wd,Headers:qd,AbortController:Gd}),Jd=zd,Yd=()=>{var e;return((e=window==null?void 0:window.__NUXT__)==null?void 0:e.config)||{}},Lr=Yd().app,Xd=()=>Lr.baseURL,Qd=()=>Lr.buildAssetsDir,To=(...e)=>Pa(La(),Qd(),...e),La=(...e)=>{const t=Lr.cdnURL||Lr.baseURL;return e.length?Pa(t,...e):t};globalThis.__buildAssetsURL=To,globalThis.__publicAssetsURL=La;globalThis.$fetch||(globalThis.$fetch=Jd.create({baseURL:Xd()}));"global"in globalThis||(globalThis.global=globalThis);function Is(e,t={},n){for(const r in e){const s=e[r],o=n?`${n}:${r}`:r;typeof s=="object"&&s!==null?Is(s,t,o):typeof s=="function"&&(t[o]=s)}return t}const Zd={run:e=>e()},eh=()=>Zd,Ia=typeof console.createTask<"u"?console.createTask:eh;function th(e,t){const n=t.shift(),r=Ia(n);return e.reduce((s,o)=>s.then(()=>r.run(()=>o(...t))),Promise.resolve())}function nh(e,t){const n=t.shift(),r=Ia(n);return Promise.all(e.map(s=>r.run(()=>s(...t))))}function cs(e,t){for(const n of[...e])n(t)}class rh{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(t,n,r={}){if(!t||typeof n!="function")return()=>{};const s=t;let o;for(;this._deprecatedHooks[t];)o=this._deprecatedHooks[t],t=o.to;if(o&&!r.allowDeprecated){let i=o.message;i||(i=`${s} hook has been deprecated`+(o.to?`, please use ${o.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(i)||(console.warn(i),this._deprecatedMessages.add(i))}if(!n.name)try{Object.defineProperty(n,"name",{get:()=>"_"+t.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[t]=this._hooks[t]||[],this._hooks[t].push(n),()=>{n&&(this.removeHook(t,n),n=void 0)}}hookOnce(t,n){let r,s=(...o)=>(typeof r=="function"&&r(),r=void 0,s=void 0,n(...o));return r=this.hook(t,s),r}removeHook(t,n){if(this._hooks[t]){const r=this._hooks[t].indexOf(n);r!==-1&&this._hooks[t].splice(r,1),this._hooks[t].length===0&&delete this._hooks[t]}}deprecateHook(t,n){this._deprecatedHooks[t]=typeof n=="string"?{to:n}:n;const r=this._hooks[t]||[];delete this._hooks[t];for(const s of r)this.hook(t,s)}deprecateHooks(t){Object.assign(this._deprecatedHooks,t);for(const n in t)this.deprecateHook(n,t[n])}addHooks(t){const n=Is(t),r=Object.keys(n).map(s=>this.hook(s,n[s]));return()=>{for(const s of r.splice(0,r.length))s()}}removeHooks(t){const n=Is(t);for(const r in n)this.removeHook(r,n[r])}removeAllHooks(){for(const t in this._hooks)delete this._hooks[t]}callHook(t,...n){return n.unshift(t),this.callHookWith(th,t,...n)}callHookParallel(t,...n){return n.unshift(t),this.callHookWith(nh,t,...n)}callHookWith(t,n,...r){const s=this._before||this._after?{name:n,args:r,context:{}}:void 0;this._before&&cs(this._before,s);const o=t(n in this._hooks?[...this._hooks[n]]:[],r);return o instanceof Promise?o.finally(()=>{this._after&&s&&cs(this._after,s)}):(this._after&&s&&cs(this._after,s),o)}beforeEach(t){return this._before=this._before||[],this._before.push(t),()=>{if(this._before!==void 0){const n=this._before.indexOf(t);n!==-1&&this._before.splice(n,1)}}}afterEach(t){return this._after=this._after||[],this._after.push(t),()=>{if(this._after!==void 0){const n=this._after.indexOf(t);n!==-1&&this._after.splice(n,1)}}}}function Ha(){return new rh}function sh(e={}){let t,n=!1;const r=i=>{if(t&&t!==i)throw new Error("Context conflict")};let s;if(e.asyncContext){const i=e.AsyncLocalStorage||globalThis.AsyncLocalStorage;i?s=new i:console.warn("[unctx] `AsyncLocalStorage` is not provided.")}const o=()=>{if(s){const i=s.getStore();if(i!==void 0)return i}return t};return{use:()=>{const i=o();if(i===void 0)throw new Error("Context is not available");return i},tryUse:()=>o(),set:(i,l)=>{l||r(i),t=i,n=!0},unset:()=>{t=void 0,n=!1},call:(i,l)=>{r(i),t=i;try{return s?s.run(i,l):l()}finally{n||(t=void 0)}},async callAsync(i,l){t=i;const a=()=>{t=i},f=()=>t===i?a:void 0;Hs.add(f);try{const c=s?s.run(i,l):l();return n||(t=void 0),await c}finally{Hs.delete(f)}}}}function oh(e={}){const t={};return{get(n,r={}){return t[n]||(t[n]=sh({...e,...r})),t[n]}}}const Ir=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof global<"u"?global:typeof window<"u"?window:{},bi="__unctx__",ih=Ir[bi]||(Ir[bi]=oh()),lh=(e,t={})=>ih.get(e,t),wi="__unctx_async_handlers__",Hs=Ir[wi]||(Ir[wi]=new Set);function mn(e){const t=[];for(const s of Hs){const o=s();o&&t.push(o)}const n=()=>{for(const s of t)s()};let r=e();return r&&typeof r=="object"&&"catch"in r&&(r=r.catch(s=>{throw n(),s})),[r,n]}const ah=!1,Ei=!1,ch=!1,uh={componentName:"NuxtLink",prefetch:!0,prefetchOn:{visibility:!0}},Km={value:null,errorValue:null,deep:!0},fh=null,Vm={},dh="#__nuxt",Na="nuxt-app",Si=36e5,hh="vite:preloadError";function $a(e=Na){return lh(e,{asyncContext:!1})}const ph="__nuxt_plugin";function gh(e){var s;let t=0;const n={_id:e.id||Na||"nuxt-app",_scope:Fc(),provide:void 0,globalName:"nuxt",versions:{get nuxt(){return"3.17.5"},get vue(){return n.vueApp.version}},payload:ot({...((s=e.ssrContext)==null?void 0:s.payload)||{},data:ot({}),state:wt({}),once:new Set,_errors:ot({})}),static:{data:{}},runWithContext(o){return n._scope.active&&!fl()?n._scope.run(()=>Ti(n,o)):Ti(n,o)},isHydrating:!0,deferHydration(){if(!n.isHydrating)return()=>{};t++;let o=!1;return()=>{if(!o&&(o=!0,t--,t===0))return n.isHydrating=!1,n.callHook("app:suspense:resolve")}},_asyncDataPromises:{},_asyncData:ot({}),_payloadRevivers:{},...e};{const o=window.__NUXT__;if(o)for(const i in o)switch(i){case"data":case"state":case"_errors":Object.assign(n.payload[i],o[i]);break;default:n.payload[i]=o[i]}}n.hooks=Ha(),n.hook=n.hooks.hook,n.callHook=n.hooks.callHook,n.provide=(o,i)=>{const l="$"+o;dr(n,l,i),dr(n.vueApp.config.globalProperties,l,i)},dr(n.vueApp,"$nuxt",n),dr(n.vueApp.config.globalProperties,"$nuxt",n);{window.addEventListener(hh,i=>{n.callHook("app:chunkError",{error:i.payload}),i.payload.message.includes("Unable to preload CSS")&&i.preventDefault()}),window.useNuxtApp||(window.useNuxtApp=ge);const o=n.hook("app:error",(...i)=>{console.error("[nuxt] error caught during app initialization",...i)});n.hook("app:mounted",o)}const r=n.payload.config;return n.provide("config",r),n}function mh(e,t){t.hooks&&e.hooks.addHooks(t.hooks)}async function yh(e,t){if(typeof t=="function"){const{provide:n}=await e.runWithContext(()=>t(e))||{};if(n&&typeof n=="object")for(const r in n)e.provide(r,n[r])}}async function _h(e,t){const n=new Set,r=[],s=[],o=[];let i=0;async function l(a){var c;const f=((c=a.dependsOn)==null?void 0:c.filter(u=>t.some(d=>d._name===u)&&!n.has(u)))??[];if(f.length>0)r.push([new Set(f),a]);else{const u=yh(e,a).then(async()=>{a._name&&(n.add(a._name),await Promise.all(r.map(async([d,g])=>{d.has(a._name)&&(d.delete(a._name),d.size===0&&(i++,await l(g)))})))});a.parallel?s.push(u.catch(d=>o.push(d))):await u}}for(const a of t)mh(e,a);for(const a of t)await l(a);if(await Promise.all(s),i)for(let a=0;a<i;a++)await Promise.all(s);if(o.length)throw o[0]}function at(e){if(typeof e=="function")return e;const t=e._name||e.name;return delete e.name,Object.assign(e.setup||(()=>{}),e,{[ph]:!0,_name:t})}function Ti(e,t,n){const r=()=>t();return $a(e._id).set(e),e.vueApp.runWithContext(r)}function ja(e){var n;let t;return Kr()&&(t=(n=tr())==null?void 0:n.appContext.app.$nuxt),t||(t=$a(e).tryUse()),t||null}function ge(e){const t=ja(e);if(!t)throw new Error("[nuxt] instance unavailable");return t}function nr(e){return ge().$config}function dr(e,t,n){Object.defineProperty(e,t,{get:()=>n})}function vh(e,t){return{ctx:{table:e},matchAll:n=>Da(n,e)}}function Fa(e){const t={};for(const n in e)t[n]=n==="dynamic"?new Map(Object.entries(e[n]).map(([r,s])=>[r,Fa(s)])):new Map(Object.entries(e[n]));return t}function bh(e){return vh(Fa(e))}function Da(e,t,n){e.endsWith("/")&&(e=e.slice(0,-1)||"/");const r=[];for(const[o,i]of Ci(t.wildcard))(e===o||e.startsWith(o+"/"))&&r.push(i);for(const[o,i]of Ci(t.dynamic))if(e.startsWith(o+"/")){const l="/"+e.slice(o.length).split("/").splice(2).join("/");r.push(...Da(l,i))}const s=t.static.get(e);return s&&r.push(s),r.filter(Boolean)}function Ci(e){return[...e.entries()].sort((t,n)=>t[0].length-n[0].length)}function us(e){if(e===null||typeof e!="object")return!1;const t=Object.getPrototypeOf(e);return t!==null&&t!==Object.prototype&&Object.getPrototypeOf(t)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function Ns(e,t,n=".",r){if(!us(t))return Ns(e,{},n,r);const s=Object.assign({},t);for(const o in e){if(o==="__proto__"||o==="constructor")continue;const i=e[o];i!=null&&(r&&r(s,o,i,n)||(Array.isArray(i)&&Array.isArray(s[o])?s[o]=[...i,...s[o]]:us(i)&&us(s[o])?s[o]=Ns(i,s[o],(n?`${n}.`:"")+o.toString(),r):s[o]=i))}return s}function wh(e){return(...t)=>t.reduce((n,r)=>Ns(n,r,"",e),{})}const Ba=wh();function Eh(e,t){try{return t in e}catch{return!1}}class $s extends Error{constructor(n,r={}){super(n,r);Tt(this,"statusCode",500);Tt(this,"fatal",!1);Tt(this,"unhandled",!1);Tt(this,"statusMessage");Tt(this,"data");Tt(this,"cause");r.cause&&!this.cause&&(this.cause=r.cause)}toJSON(){const n={message:this.message,statusCode:js(this.statusCode,500)};return this.statusMessage&&(n.statusMessage=Ua(this.statusMessage)),this.data!==void 0&&(n.data=this.data),n}}Tt($s,"__h3_error__",!0);function Sh(e){if(typeof e=="string")return new $s(e);if(Th(e))return e;const t=new $s(e.message??e.statusMessage??"",{cause:e.cause||e});if(Eh(e,"stack"))try{Object.defineProperty(t,"stack",{get(){return e.stack}})}catch{try{t.stack=e.stack}catch{}}if(e.data&&(t.data=e.data),e.statusCode?t.statusCode=js(e.statusCode,t.statusCode):e.status&&(t.statusCode=js(e.status,t.statusCode)),e.statusMessage?t.statusMessage=e.statusMessage:e.statusText&&(t.statusMessage=e.statusText),t.statusMessage){const n=t.statusMessage;Ua(t.statusMessage)!==n&&console.warn("[h3] Please prefer using `message` for longer error messages instead of `statusMessage`. In the future, `statusMessage` will be sanitized by default.")}return e.fatal!==void 0&&(t.fatal=e.fatal),e.unhandled!==void 0&&(t.unhandled=e.unhandled),t}function Th(e){var t;return((t=e==null?void 0:e.constructor)==null?void 0:t.__h3_error__)===!0}const Ch=/[^\u0009\u0020-\u007E]/g;function Ua(e=""){return e.replace(Ch,"")}function js(e,t=200){return!e||(typeof e=="string"&&(e=Number.parseInt(e,10)),e<100||e>999)?t:e}const Ka=Symbol("layout-meta"),Zt=Symbol("route"),Le=()=>{var e;return(e=ge())==null?void 0:e.$router},Co=()=>Kr()?Re(Zt,ge()._route):ge()._route;const Rh=()=>{try{if(ge()._processingMiddleware)return!0}catch{return!1}return!1},Va=(e,t)=>{e||(e="/");const n=typeof e=="string"?e:"path"in e?Fs(e):Le().resolve(e).href;if(t!=null&&t.open){const{target:a="_blank",windowFeatures:f={}}=t.open,c=Object.entries(f).filter(([u,d])=>d!==void 0).map(([u,d])=>`${u.toLowerCase()}=${d}`).join(", ");return open(n,a,c),Promise.resolve()}const r=jt(n,{acceptRelative:!0}),s=(t==null?void 0:t.external)||r;if(s){if(!(t!=null&&t.external))throw new Error("Navigating to an external URL is not allowed by default. Use `navigateTo(url, { external: true })`.");const{protocol:a}=new URL(n,window.location.href);if(a&&Pd(a))throw new Error(`Cannot navigate to a URL with '${a}' protocol.`)}const o=Rh();if(!s&&o){if(t!=null&&t.replace){if(typeof e=="string"){const{pathname:a,search:f,hash:c}=Oa(e);return{path:a,...f&&{query:Eo(f)},...c&&{hash:c},replace:!0}}return{...e,replace:!0}}return e}const i=Le(),l=ge();return s?(l._scope.stop(),t!=null&&t.replace?location.replace(n):location.href=n,o?l.isHydrating?new Promise(()=>{}):!1:Promise.resolve()):t!=null&&t.replace?i.replace(e):i.push(e)};function Fs(e){return xa(e.path||"",e.query||{})+(e.hash||"")}const Wa="__nuxt_error",qr=()=>Pl(ge().payload,"error"),Vt=e=>{const t=Jt(e);try{const n=ge(),r=qr();n.hooks.callHook("app:error",t),r.value||(r.value=t)}catch{throw t}return t},qa=async(e={})=>{const t=ge(),n=qr();t.callHook("app:error:cleared",e),e.redirect&&await Le().replace(e.redirect),n.value=fh},Ga=e=>!!e&&typeof e=="object"&&Wa in e,Jt=e=>{const t=Sh(e);return Object.defineProperty(t,Wa,{value:!0,configurable:!1,writable:!1}),t};function Ri(e){const t=kh(e),n=new ArrayBuffer(t.length),r=new DataView(n);for(let s=0;s<n.byteLength;s++)r.setUint8(s,t.charCodeAt(s));return n}const xh="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function kh(e){e.length%4===0&&(e=e.replace(/==?$/,""));let t="",n=0,r=0;for(let s=0;s<e.length;s++)n<<=6,n|=xh.indexOf(e[s]),r+=6,r===24&&(t+=String.fromCharCode((n&16711680)>>16),t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255),n=r=0);return r===12?(n>>=4,t+=String.fromCharCode(n)):r===18&&(n>>=2,t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255)),t}const Ph=-1,Ah=-2,Oh=-3,Mh=-4,Lh=-5,Ih=-6;function Hh(e,t){return Nh(JSON.parse(e),t)}function Nh(e,t){if(typeof e=="number")return s(e,!0);if(!Array.isArray(e)||e.length===0)throw new Error("Invalid input");const n=e,r=Array(n.length);function s(o,i=!1){if(o===Ph)return;if(o===Oh)return NaN;if(o===Mh)return 1/0;if(o===Lh)return-1/0;if(o===Ih)return-0;if(i)throw new Error("Invalid input");if(o in r)return r[o];const l=n[o];if(!l||typeof l!="object")r[o]=l;else if(Array.isArray(l))if(typeof l[0]=="string"){const a=l[0],f=t==null?void 0:t[a];if(f)return r[o]=f(s(l[1]));switch(a){case"Date":r[o]=new Date(l[1]);break;case"Set":const c=new Set;r[o]=c;for(let g=1;g<l.length;g+=1)c.add(s(l[g]));break;case"Map":const u=new Map;r[o]=u;for(let g=1;g<l.length;g+=2)u.set(s(l[g]),s(l[g+1]));break;case"RegExp":r[o]=new RegExp(l[1],l[2]);break;case"Object":r[o]=Object(l[1]);break;case"BigInt":r[o]=BigInt(l[1]);break;case"null":const d=Object.create(null);r[o]=d;for(let g=1;g<l.length;g+=2)d[l[g]]=s(l[g+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const g=globalThis[a],_=l[1],y=Ri(_),S=new g(y);r[o]=S;break}case"ArrayBuffer":{const g=l[1],_=Ri(g);r[o]=_;break}default:throw new Error(`Unknown type ${a}`)}}else{const a=new Array(l.length);r[o]=a;for(let f=0;f<l.length;f+=1){const c=l[f];c!==Ah&&(a[f]=s(c))}}else{const a={};r[o]=a;for(const f in l){const c=l[f];a[f]=s(c)}}return r[o]}return s(0)}const $h=new Set(["link","style","script","noscript"]),jh=new Set(["title","titleTemplate","script","style","noscript"]),xi=new Set(["base","meta","link","style","script","noscript"]),Fh=new Set(["title","base","htmlAttrs","bodyAttrs","meta","link","style","script","noscript"]),Dh=new Set(["base","title","titleTemplate","bodyAttrs","htmlAttrs","templateParams"]),Bh=new Set(["key","tagPosition","tagPriority","tagDuplicateStrategy","innerHTML","textContent","processTemplateParams"]),Uh=new Set(["templateParams","htmlAttrs","bodyAttrs"]),Ro=new Set(["theme-color","google-site-verification","og","article","book","profile","twitter","author"]),zn={META:new Set(["twitter"]),OG:new Set(["og","book","article","profile","fb"]),MEDIA:new Set(["ogImage","ogVideo","ogAudio","twitterImage"]),HTTP_EQUIV:new Set(["contentType","defaultStyle","xUaCompatible"])},Kh={articleExpirationTime:"article:expiration_time",articleModifiedTime:"article:modified_time",articlePublishedTime:"article:published_time",bookReleaseDate:"book:release_date",fbAppId:"fb:app_id",ogAudioSecureUrl:"og:audio:secure_url",ogAudioUrl:"og:audio",ogImageSecureUrl:"og:image:secure_url",ogImageUrl:"og:image",ogSiteName:"og:site_name",ogVideoSecureUrl:"og:video:secure_url",ogVideoUrl:"og:video",profileFirstName:"profile:first_name",profileLastName:"profile:last_name",profileUsername:"profile:username",msapplicationConfig:"msapplication-Config",msapplicationTileColor:"msapplication-TileColor",msapplicationTileImage:"msapplication-TileImage"},za={appleItunesApp:{unpack:{entrySeparator:", ",resolve:({key:e,value:t})=>`${gt(e)}=${t}`}},refresh:{metaKey:"http-equiv",unpack:{entrySeparator:";",resolve:({key:e,value:t})=>e==="seconds"?`${t}`:void 0}},robots:{unpack:{entrySeparator:", ",resolve:({key:e,value:t})=>typeof t=="boolean"?gt(e):`${gt(e)}:${t}`}},contentSecurityPolicy:{metaKey:"http-equiv",unpack:{entrySeparator:"; ",resolve:({key:e,value:t})=>`${gt(e)} ${t}`}},charset:{}};function gt(e){const t=e.replace(/([A-Z])/g,"-$1").toLowerCase(),n=t.indexOf("-");return n===-1?t:zn.META.has(t.slice(0,n))||zn.OG.has(t.slice(0,n))?e.replace(/([A-Z])/g,":$1").toLowerCase():t}function Ja(e){return Object.fromEntries(Object.entries(e).filter(([t,n])=>String(n)!=="false"&&t))}function Ds(e){return Array.isArray(e)?e.map(Ds):!e||typeof e!="object"?e:Object.fromEntries(Object.entries(e).map(([t,n])=>[gt(t),Ds(n)]))}function Ya(e,t={}){const{entrySeparator:n="",keyValueSeparator:r="",wrapValue:s,resolve:o}=t;return Object.entries(e).map(([i,l])=>{if(o){const f=o({key:i,value:l});if(f!==void 0)return f}const a=typeof l=="object"?Ya(l,t):typeof l=="number"?l.toString():typeof l=="string"&&s?`${s}${l.replace(new RegExp(s,"g"),`\\${s}`)}${s}`:l;return`${i}${r}${a}`}).join(n)}function ki(e,t){const n=Ja(t),r=gt(e),s=Xa(r);if(!Ro.has(r))return[{[s]:r,...n}];const o=Object.fromEntries(Object.entries(n).map(([i,l])=>[`${e}${i==="url"?"":`${i[0].toUpperCase()}${i.slice(1)}`}`,l]));return Hr(o||{}).sort((i,l)=>{var a,f;return(((a=i[s])==null?void 0:a.length)||0)-(((f=l[s])==null?void 0:f.length)||0)})}function Xa(e){var r;if(((r=za[e])==null?void 0:r.metaKey)==="http-equiv"||zn.HTTP_EQUIV.has(e))return"http-equiv";const t=gt(e),n=t.indexOf(":");return n===-1?"name":zn.OG.has(t.slice(0,n))?"property":"name"}function Vh(e){return Kh[e]||gt(e)}function Wh(e,t){var n;return t==="refresh"?`${e.seconds};url=${e.url}`:Ya(Ds(e),{keyValueSeparator:"=",entrySeparator:", ",resolve:({value:r,key:s})=>r===null?"":typeof r=="boolean"?s:void 0,...(n=za[t])==null?void 0:n.unpack})}function Hr(e){const t=[],n={};for(const[s,o]of Object.entries(e)){if(Array.isArray(o)){if(s==="themeColor"){o.forEach(i=>{typeof i=="object"&&i!==null&&t.push({name:"theme-color",...i})});continue}for(const i of o)if(typeof i=="object"&&i!==null){const l=[],a=[];for(const[f,c]of Object.entries(i)){const u=`${s}${f==="url"?"":`:${f}`}`,d=Hr({[u]:c});(f==="url"?l:a).push(...d)}t.push(...l,...a)}else t.push(...typeof i=="string"?Hr({[s]:i}):ki(s,i));continue}if(typeof o=="object"&&o)if(zn.MEDIA.has(s)){const i=s.startsWith("twitter")?"twitter":"og",l=s.replace(/^(og|twitter)/,"").toLowerCase(),a=i==="twitter"?"name":"property";o.url&&t.push({[a]:`${i}:${l}`,content:o.url}),o.secureUrl&&t.push({[a]:`${i}:${l}:secure_url`,content:o.secureUrl});for(const[f,c]of Object.entries(o))f!=="url"&&f!=="secureUrl"&&t.push({[a]:`${i}:${l}:${f}`,content:c})}else Ro.has(gt(s))?t.push(...ki(s,o)):n[s]=Ja(o);else n[s]=o}const r=Object.entries(n).map(([s,o])=>{if(s==="charset")return{charset:o===null?"_null":o};const i=Xa(s),l=Vh(s),a=o===null?"_null":typeof o=="object"?Wh(o,s):typeof o=="number"?o.toString():o;return i==="http-equiv"?{"http-equiv":l,content:a}:{[i]:l,content:a}});return[...t,...r].map(s=>"content"in s&&s.content==="_null"?{...s,content:null}:s)}const qh={key:"flatMeta",hooks:{"entries:normalize":e=>{const t=[];e.tags=e.tags.map(n=>n.tag!=="_flatMeta"?n:(t.push(Hr(n.props).map(r=>({...n,tag:"meta",props:r}))),!1)).filter(Boolean).concat(...t)}}},Gh=["name","property","http-equiv"],zh=new Set(["viewport","description","keywords","robots"]);function Qa(e){const t=e.split(":");return t.length?Ro.has(t[1]):!1}function Bs(e){const{props:t,tag:n}=e;if(Dh.has(n))return n;if(n==="link"&&t.rel==="canonical")return"canonical";if(t.charset)return"charset";if(e.tag==="meta"){for(const r of Gh)if(t[r]!==void 0){const s=t[r],o=s.includes(":"),i=zh.has(s),a=!(o||i)&&e.key?`:key:${e.key}`:"";return`${n}:${s}${a}`}}if(e.key)return`${n}:key:${e.key}`;if(t.id)return`${n}:id:${t.id}`;if(jh.has(n)){const r=e.textContent||e.innerHTML;if(r)return`${n}:content:${r}`}}function Pi(e){const t=e._h||e._d;if(t)return t;const n=e.textContent||e.innerHTML;return n||`${e.tag}:${Object.entries(e.props).map(([r,s])=>`${r}:${String(s)}`).join(",")}`}function Nr(e,t,n){typeof e==="function"&&(!n||n!=="titleTemplate"&&!(n[0]==="o"&&n[1]==="n"))&&(e=e());let s;if(t&&(s=t(n,e)),Array.isArray(s))return s.map(o=>Nr(o,t));if((s==null?void 0:s.constructor)===Object){const o={};for(const i of Object.keys(s))o[i]=Nr(s[i],t,i);return o}return s}function Jh(e,t){const n=e==="style"?new Map:new Set;function r(s){const o=s.trim();if(o)if(e==="style"){const[i,...l]=o.split(":").map(a=>a.trim());i&&l.length&&n.set(i,l.join(":"))}else o.split(" ").filter(Boolean).forEach(i=>n.add(i))}return typeof t=="string"?e==="style"?t.split(";").forEach(r):r(t):Array.isArray(t)?t.forEach(s=>r(s)):t&&typeof t=="object"&&Object.entries(t).forEach(([s,o])=>{o&&o!=="false"&&(e==="style"?n.set(s.trim(),o):r(s))}),n}function Za(e,t){return e.props=e.props||{},t&&Object.entries(t).forEach(([n,r])=>{if(r===null){e.props[n]=null;return}if(n==="class"||n==="style"){e.props[n]=Jh(n,r);return}if(Bh.has(n)){if(["textContent","innerHTML"].includes(n)&&typeof r=="object"){let i=t.type;if(t.type||(i="application/json"),!(i!=null&&i.endsWith("json"))&&i!=="speculationrules")return;t.type=i,e.props.type=i,e[n]=JSON.stringify(r)}else e[n]=r;return}const s=String(r),o=n.startsWith("data-");s==="true"||s===""?e.props[n]=o?s:!0:!r&&o&&s==="false"?e.props[n]="false":r!==void 0&&(e.props[n]=r)}),e}function Yh(e,t){const n=typeof t=="object"&&typeof t!="function"?t:{[e==="script"||e==="noscript"||e==="style"?"innerHTML":"textContent"]:t},r=Za({tag:e,props:{}},n);return r.key&&$h.has(r.tag)&&(r.props["data-hid"]=r._h=r.key),r.tag==="script"&&typeof r.innerHTML=="object"&&(r.innerHTML=JSON.stringify(r.innerHTML),r.props.type=r.props.type||"application/json"),Array.isArray(r.props.content)?r.props.content.map(s=>({...r,props:{...r.props,content:s}})):r}function Xh(e,t){if(!e)return[];typeof e=="function"&&(e=e());const n=(s,o)=>{for(let i=0;i<t.length;i++)o=t[i](s,o);return o};e=n(void 0,e);const r=[];return e=Nr(e,n),Object.entries(e||{}).forEach(([s,o])=>{if(o!==void 0)for(const i of Array.isArray(o)?o:[o])r.push(Yh(s,i))}),r.flat()}const Us=(e,t)=>e._w===t._w?e._p-t._p:e._w-t._w,Ai={base:-10,title:10},Qh={critical:-8,high:-1,low:2},Oi={meta:{"content-security-policy":-30,charset:-20,viewport:-15},link:{preconnect:20,stylesheet:60,preload:70,modulepreload:70,prefetch:90,"dns-prefetch":90,prerender:90},script:{async:30,defer:80,sync:50},style:{imported:40,sync:60}},Zh=/@import/,kn=e=>e===""||e===!0;function ep(e,t){var o;if(typeof t.tagPriority=="number")return t.tagPriority;let n=100;const r=Qh[t.tagPriority]||0,s=e.resolvedOptions.disableCapoSorting?{link:{},script:{},style:{}}:Oi;if(t.tag in Ai)n=Ai[t.tag];else if(t.tag==="meta"){const i=t.props["http-equiv"]==="content-security-policy"?"content-security-policy":t.props.charset?"charset":t.props.name==="viewport"?"viewport":null;i&&(n=Oi.meta[i])}else t.tag==="link"&&t.props.rel?n=s.link[t.props.rel]:t.tag==="script"?kn(t.props.async)?n=s.script.async:t.props.src&&!kn(t.props.defer)&&!kn(t.props.async)&&t.props.type!=="module"&&!((o=t.props.type)!=null&&o.endsWith("json"))?n=s.script.sync:kn(t.props.defer)&&t.props.src&&!kn(t.props.async)&&(n=s.script.defer):t.tag==="style"&&(n=t.innerHTML&&Zh.test(t.innerHTML)?s.style.imported:s.style.sync);return(n||100)+r}function Mi(e,t){const n=typeof t=="function"?t(e):t,r=n.key||String(e.plugins.size+1);e.plugins.get(r)||(e.plugins.set(r,n),e.hooks.addHooks(n.hooks||{}))}function tp(e={}){var l;const t=Ha();t.addHooks(e.hooks||{});const n=!e.document,r=new Map,s=new Map,o=[],i={_entryCount:1,plugins:s,dirty:!1,resolvedOptions:e,hooks:t,ssr:n,entries:r,headEntries(){return[...r.values()]},use:a=>Mi(i,a),push(a,f){const c={...f||{}};delete c.head;const u=c._index??i._entryCount++,d={_i:u,input:a,options:c},g={_poll(_=!1){i.dirty=!0,!_&&o.push(u),t.callHook("entries:updated",i)},dispose(){r.delete(u)&&g._poll(!0)},patch(_){(!c.mode||c.mode==="server"&&n||c.mode==="client"&&!n)&&(d.input=_,r.set(u,d),g._poll())}};return g.patch(a),g},async resolveTags(){var g;const a={tagMap:new Map,tags:[],entries:[...i.entries.values()]};for(await t.callHook("entries:resolve",a);o.length;){const _=o.shift(),y=r.get(_);if(y){const S={tags:Xh(y.input,e.propResolvers||[]).map(E=>Object.assign(E,y.options)),entry:y};await t.callHook("entries:normalize",S),y._tags=S.tags.map((E,w)=>(E._w=ep(i,E),E._p=(y._i<<10)+w,E._d=Bs(E),E))}}let f=!1;a.entries.flatMap(_=>(_._tags||[]).map(y=>({...y,props:{...y.props}}))).sort(Us).reduce((_,y)=>{const S=String(y._d||y._p);if(!_.has(S))return _.set(S,y);const E=_.get(S);if(((y==null?void 0:y.tagDuplicateStrategy)||(Uh.has(y.tag)?"merge":null)||(y.key&&y.key===E.key?"merge":null))==="merge"){const p={...E.props};Object.entries(y.props).forEach(([v,b])=>p[v]=v==="style"?new Map([...E.props.style||new Map,...b]):v==="class"?new Set([...E.props.class||new Set,...b]):b),_.set(S,{...y,props:p})}else y._p>>10===E._p>>10&&y.tag==="meta"&&Qa(S)?(_.set(S,Object.assign([...Array.isArray(E)?E:[E],y],y)),f=!0):(y._w===E._w?y._p>E._p:(y==null?void 0:y._w)<(E==null?void 0:E._w))&&_.set(S,y);return _},a.tagMap);const c=a.tagMap.get("title"),u=a.tagMap.get("titleTemplate");if(i._title=c==null?void 0:c.textContent,u){const _=u==null?void 0:u.textContent;if(i._titleTemplate=_,_){let y=typeof _=="function"?_(c==null?void 0:c.textContent):_;typeof y=="string"&&!i.plugins.has("template-params")&&(y=y.replace("%s",(c==null?void 0:c.textContent)||"")),c?y===null?a.tagMap.delete("title"):a.tagMap.set("title",{...c,textContent:y}):(u.tag="title",u.textContent=y)}}a.tags=Array.from(a.tagMap.values()),f&&(a.tags=a.tags.flat().sort(Us)),await t.callHook("tags:beforeResolve",a),await t.callHook("tags:resolve",a),await t.callHook("tags:afterResolve",a);const d=[];for(const _ of a.tags){const{innerHTML:y,tag:S,props:E}=_;if(Fh.has(S)&&!(Object.keys(E).length===0&&!_.innerHTML&&!_.textContent)&&!(S==="meta"&&!E.content&&!E["http-equiv"]&&!E.charset)){if(S==="script"&&y){if((g=E.type)!=null&&g.endsWith("json")){const w=typeof y=="string"?y:JSON.stringify(y);_.innerHTML=w.replace(/</g,"\\u003C")}else typeof y=="string"&&(_.innerHTML=y.replace(new RegExp(`</${S}`,"g"),`<\\/${S}`));_._d=Bs(_)}d.push(_)}}return d}};return((e==null?void 0:e.plugins)||[]).forEach(a=>Mi(i,a)),i.hooks.callHook("init",i),(l=e.init)==null||l.forEach(a=>a&&i.push(a)),i}const Pt="%separator",np=new RegExp(`${Pt}(?:\\s*${Pt})*`,"g");function rp(e,t,n=!1){var s;let r;if(t==="s"||t==="pageTitle")r=e.pageTitle;else if(t.includes(".")){const o=t.indexOf(".");r=(s=e[t.substring(0,o)])==null?void 0:s[t.substring(o+1)]}else r=e[t];if(r!==void 0)return n?(r||"").replace(/\\/g,"\\\\").replace(/</g,"\\u003C").replace(/"/g,'\\"'):r||""}function hr(e,t,n,r=!1){if(typeof e!="string"||!e.includes("%"))return e;let s=e;try{s=decodeURI(e)}catch{}const o=s.match(/%\w+(?:\.\w+)?/g);if(!o)return e;const i=e.includes(Pt);return e=e.replace(/%\w+(?:\.\w+)?/g,l=>{if(l===Pt||!o.includes(l))return l;const a=rp(t,l.slice(1),r);return a!==void 0?a:l}).trim(),i&&(e.endsWith(Pt)&&(e=e.slice(0,-Pt.length)),e.startsWith(Pt)&&(e=e.slice(Pt.length)),e=e.replace(np,n||"").trim()),e}const Li=e=>e.includes(":key")?e:e.split(":").join(":key:"),sp={key:"aliasSorting",hooks:{"tags:resolve":e=>{let t=!1;for(const n of e.tags){const r=n.tagPriority;if(!r)continue;const s=String(r);if(s.startsWith("before:")){const o=Li(s.slice(7)),i=e.tagMap.get(o);i&&(typeof i.tagPriority=="number"&&(n.tagPriority=i.tagPriority),n._p=i._p-1,t=!0)}else if(s.startsWith("after:")){const o=Li(s.slice(6)),i=e.tagMap.get(o);i&&(typeof i.tagPriority=="number"&&(n.tagPriority=i.tagPriority),n._p=i._p+1,t=!0)}}t&&(e.tags=e.tags.sort(Us))}}},op={key:"deprecations",hooks:{"entries:normalize":({tags:e})=>{for(const t of e)t.props.children&&(t.innerHTML=t.props.children,delete t.props.children),t.props.hid&&(t.key=t.props.hid,delete t.props.hid),t.props.vmid&&(t.key=t.props.vmid,delete t.props.vmid),t.props.body&&(t.tagPosition="bodyClose",delete t.props.body)}}};async function Ks(e){if(typeof e==="function")return e;if(e instanceof Promise)return await e;if(Array.isArray(e))return await Promise.all(e.map(n=>Ks(n)));if((e==null?void 0:e.constructor)===Object){const n={};for(const r of Object.keys(e))n[r]=await Ks(e[r]);return n}return e}const ip={key:"promises",hooks:{"entries:resolve":async e=>{const t=[];for(const n in e.entries)e.entries[n]._promisesProcessed||t.push(Ks(e.entries[n].input).then(r=>{e.entries[n].input=r,e.entries[n]._promisesProcessed=!0}));await Promise.all(t)}}},lp={meta:"content",link:"href",htmlAttrs:"lang"},ap=["innerHTML","textContent"],cp=e=>({key:"template-params",hooks:{"entries:normalize":t=>{var r,s,o;const n=((s=(r=t.tags.filter(i=>i.tag==="templateParams"&&i.mode==="server"))==null?void 0:r[0])==null?void 0:s.props)||{};Object.keys(n).length&&(e._ssrPayload={templateParams:{...((o=e._ssrPayload)==null?void 0:o.templateParams)||{},...n}})},"tags:resolve":({tagMap:t,tags:n})=>{var o;const r=((o=t.get("templateParams"))==null?void 0:o.props)||{},s=r.separator||"|";delete r.separator,r.pageTitle=hr(r.pageTitle||e._title||"",r,s);for(const i of n){if(i.processTemplateParams===!1)continue;const l=lp[i.tag];if(l&&typeof i.props[l]=="string")i.props[l]=hr(i.props[l],r,s);else if(i.processTemplateParams||i.tag==="titleTemplate"||i.tag==="title")for(const a of ap)typeof i[a]=="string"&&(i[a]=hr(i[a],r,s,i.tag==="script"&&i.props.type.endsWith("json")))}e._templateParams=r,e._separator=s},"tags:afterResolve":({tagMap:t})=>{const n=t.get("title");n!=null&&n.textContent&&n.processTemplateParams!==!1&&(n.textContent=hr(n.textContent,e._templateParams,e._separator))}}}),up=(e,t)=>ve(t)?lu(t):t,xo="usehead";function fp(e){return{install(n){n.config.globalProperties.$unhead=e,n.config.globalProperties.$head=e,n.provide(xo,e)}}.install}function ec(){if(Kr()){const e=Re(xo);if(!e)throw new Error("useHead() was called without provide context, ensure you call it through the setup() function.");return e}throw new Error("useHead() was called without provide context, ensure you call it through the setup() function.")}function tc(e,t={}){const n=t.head||ec();return n.ssr?n.push(e||{},t):dp(n,e,t)}function dp(e,t,n={}){const r=lt(!1);let s;return nf(()=>{const i=r.value?{}:Nr(t,up);s?s.patch(i):s=e.push(i,n)}),tr()&&(tn(()=>{s.dispose()}),Bl(()=>{r.value=!0}),Dl(()=>{r.value=!1})),s}function hp(e={},t={}){(t.head||ec()).use(qh);const{title:r,titleTemplate:s,...o}=e;return tc({title:r,titleTemplate:s,_flatMeta:o},t)}function nc(e){var n;const t=e||ja();return((n=t==null?void 0:t.ssrContext)==null?void 0:n.head)||(t==null?void 0:t.runWithContext(()=>{if(Kr())return Re(xo)}))}function pp(e,t={}){const n=nc(t.nuxt);if(n)return tc(e,{head:n,...t})}function gp(e,t={}){const n=nc(t.nuxt);if(n)return hp(e,{head:n,...t})}const mp="modulepreload",yp=function(e,t){return new URL(e,t).href},Ii={},Vs=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){let i=function(c){return Promise.all(c.map(u=>Promise.resolve(u).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};const l=document.getElementsByTagName("link"),a=document.querySelector("meta[property=csp-nonce]"),f=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));s=i(n.map(c=>{if(c=yp(c,r),c in Ii)return;Ii[c]=!0;const u=c.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(!!r)for(let y=l.length-1;y>=0;y--){const S=l[y];if(S.href===c&&(!u||S.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${d}`))return;const _=document.createElement("link");if(_.rel=u?"stylesheet":mp,u||(_.as="script"),_.crossOrigin="",_.href=c,f&&_.setAttribute("nonce",f),document.head.appendChild(_),u)return new Promise((y,S)=>{_.addEventListener("load",y),_.addEventListener("error",()=>S(new Error(`Unable to preload CSS for ${c}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return s.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};let _r,vr;function _p(){return _r=$fetch(To(`builds/meta/${nr().app.buildId}.json`),{responseType:"json"}),_r.then(e=>{vr=bh(e.matcher)}).catch(e=>{console.error("[nuxt] Error fetching app manifest.",e)}),_r}function rc(){return _r||_p()}async function sc(e){const t=typeof e=="string"?e:e.path;if(await rc(),!vr)return console.error("[nuxt] Error creating app manifest matcher.",vr),{};try{return Ba({},...vr.matchAll(t).reverse())}catch(n){return console.error("[nuxt] Error matching route rules.",n),{}}}async function vp(e){return null}let Ut=null;async function bp(){var r;if(Ut)return Ut;const e=document.getElementById("__NUXT_DATA__");if(!e)return{};const t=await wp(e.textContent||""),n=e.dataset.src?await vp(e.dataset.src):void 0;return Ut={...t,...n,...window.__NUXT__},(r=Ut.config)!=null&&r.public&&(Ut.config.public=wt(Ut.config.public)),Ut}async function wp(e){return await Hh(e,ge()._payloadRevivers)}function Ep(e,t){ge()._payloadRevivers[e]=t}const Sp=[["NuxtError",e=>Jt(e)],["EmptyShallowRef",e=>Yt(e==="_"?void 0:e==="0n"?BigInt(0):Ar(e))],["EmptyRef",e=>lt(e==="_"?void 0:e==="0n"?BigInt(0):Ar(e))],["ShallowRef",e=>Yt(e)],["ShallowReactive",e=>ot(e)],["Ref",e=>lt(e)],["Reactive",e=>wt(e)]],Tp=at({name:"nuxt:revive-payload:client",order:-30,async setup(e){let t,n;for(const[r,s]of Sp)Ep(r,s);Object.assign(e.payload,([t,n]=mn(()=>e.runWithContext(bp)),t=await t,n(),t)),window.__NUXT__=e.payload}});async function ko(e,t={}){const n=t.document||e.resolvedOptions.document;if(!n||!e.dirty)return;const r={shouldRender:!0,tags:[]};if(await e.hooks.callHook("dom:beforeRender",r),!!r.shouldRender)return e._domUpdatePromise||(e._domUpdatePromise=new Promise(async s=>{var g;const o=new Map,i=new Promise(_=>{e.resolveTags().then(y=>{_(y.map(S=>{const E=o.get(S._d)||0,w={tag:S,id:(E?`${S._d}:${E}`:S._d)||Pi(S),shouldRender:!0};return S._d&&Qa(S._d)&&o.set(S._d,E+1),w}))})});let l=e._dom;if(!l){l={title:n.title,elMap:new Map().set("htmlAttrs",n.documentElement).set("bodyAttrs",n.body)};for(const _ of["body","head"]){const y=(g=n[_])==null?void 0:g.children;for(const S of y){const E=S.tagName.toLowerCase();if(!xi.has(E))continue;const w=Za({tag:E,props:{}},{innerHTML:S.innerHTML,...S.getAttributeNames().reduce((p,v)=>(p[v]=S.getAttribute(v),p),{})||{}});if(w.key=S.getAttribute("data-hid")||void 0,w._d=Bs(w)||Pi(w),l.elMap.has(w._d)){let p=1,v=w._d;for(;l.elMap.has(v);)v=`${w._d}:${p++}`;l.elMap.set(v,S)}else l.elMap.set(w._d,S)}}}l.pendingSideEffects={...l.sideEffects},l.sideEffects={};function a(_,y,S){const E=`${_}:${y}`;l.sideEffects[E]=S,delete l.pendingSideEffects[E]}function f({id:_,$el:y,tag:S}){const E=S.tag.endsWith("Attrs");l.elMap.set(_,y),E||(S.textContent&&S.textContent!==y.textContent&&(y.textContent=S.textContent),S.innerHTML&&S.innerHTML!==y.innerHTML&&(y.innerHTML=S.innerHTML),a(_,"el",()=>{y==null||y.remove(),l.elMap.delete(_)}));for(const w in S.props){if(!Object.prototype.hasOwnProperty.call(S.props,w))continue;const p=S.props[w];if(w.startsWith("on")&&typeof p=="function"){const b=y==null?void 0:y.dataset;if(b&&b[`${w}fired`]){const C=w.slice(0,-5);p.call(y,new Event(C.substring(2)))}y.getAttribute(`data-${w}`)!==""&&((S.tag==="bodyAttrs"?n.defaultView:y).addEventListener(w.substring(2),p.bind(y)),y.setAttribute(`data-${w}`,""));continue}const v=`attr:${w}`;if(w==="class"){if(!p)continue;for(const b of p)E&&a(_,`${v}:${b}`,()=>y.classList.remove(b)),!y.classList.contains(b)&&y.classList.add(b)}else if(w==="style"){if(!p)continue;for(const[b,C]of p)a(_,`${v}:${b}`,()=>{y.style.removeProperty(b)}),y.style.setProperty(b,C)}else p!==!1&&p!==null&&(y.getAttribute(w)!==p&&y.setAttribute(w,p===!0?"":String(p)),E&&a(_,v,()=>y.removeAttribute(w)))}}const c=[],u={bodyClose:void 0,bodyOpen:void 0,head:void 0},d=await i;for(const _ of d){const{tag:y,shouldRender:S,id:E}=_;if(S){if(y.tag==="title"){n.title=y.textContent,a("title","",()=>n.title=l.title);continue}_.$el=_.$el||l.elMap.get(E),_.$el?f(_):xi.has(y.tag)&&c.push(_)}}for(const _ of c){const y=_.tag.tagPosition||"head";_.$el=n.createElement(_.tag.tag),f(_),u[y]=u[y]||n.createDocumentFragment(),u[y].appendChild(_.$el)}for(const _ of d)await e.hooks.callHook("dom:renderTag",_,n,a);u.head&&n.head.appendChild(u.head),u.bodyOpen&&n.body.insertBefore(u.bodyOpen,n.body.firstChild),u.bodyClose&&n.body.appendChild(u.bodyClose);for(const _ in l.pendingSideEffects)l.pendingSideEffects[_]();e._dom=l,await e.hooks.callHook("dom:rendered",{renders:d}),s()}).finally(()=>{e._domUpdatePromise=void 0,e.dirty=!1})),e._domUpdatePromise}function Cp(e={}){var r,s,o;const t=((r=e.domOptions)==null?void 0:r.render)||ko;e.document=e.document||(typeof window<"u"?document:void 0);const n=((o=(s=e.document)==null?void 0:s.head.querySelector('script[id="unhead:payload"]'))==null?void 0:o.innerHTML)||!1;return tp({...e,plugins:[...e.plugins||[],{key:"client",hooks:{"entries:updated":t}}],init:[n?JSON.parse(n):!1,...e.init||[]]})}function Rp(e,t){let n=0;return()=>{const r=++n;t(()=>{n===r&&e()})}}function xp(e={}){const t=Cp({domOptions:{render:Rp(()=>ko(t),n=>setTimeout(n,0))},...e});return t.install=fp(t),t}const kp={disableDefaults:!0,disableCapoSorting:!1,plugins:[op,ip,cp,sp]},Pp=at({name:"nuxt:head",enforce:"pre",setup(e){const t=xp(kp);e.vueApp.use(t);{let n=!0;const r=async()=>{n=!1,await ko(t)};t.hooks.hook("dom:beforeRender",s=>{s.shouldRender=!n}),e.hooks.hook("page:start",()=>{n=!0}),e.hooks.hook("page:finish",()=>{e.isHydrating||r()}),e.hooks.hook("app:error",r),e.hooks.hook("app:suspense:resolve",r)}}});/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const an=typeof document<"u";function oc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Ap(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&oc(e.default)}const se=Object.assign;function fs(e,t){const n={};for(const r in t){const s=t[r];n[r]=Qe(s)?s.map(e):e(s)}return n}const Fn=()=>{},Qe=Array.isArray,ic=/#/g,Op=/&/g,Mp=/\//g,Lp=/=/g,Ip=/\?/g,lc=/\+/g,Hp=/%5B/g,Np=/%5D/g,ac=/%5E/g,$p=/%60/g,cc=/%7B/g,jp=/%7C/g,uc=/%7D/g,Fp=/%20/g;function Po(e){return encodeURI(""+e).replace(jp,"|").replace(Hp,"[").replace(Np,"]")}function Dp(e){return Po(e).replace(cc,"{").replace(uc,"}").replace(ac,"^")}function Ws(e){return Po(e).replace(lc,"%2B").replace(Fp,"+").replace(ic,"%23").replace(Op,"%26").replace($p,"`").replace(cc,"{").replace(uc,"}").replace(ac,"^")}function Bp(e){return Ws(e).replace(Lp,"%3D")}function Up(e){return Po(e).replace(ic,"%23").replace(Ip,"%3F")}function Kp(e){return e==null?"":Up(e).replace(Mp,"%2F")}function Jn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Vp=/\/$/,Wp=e=>e.replace(Vp,"");function ds(e,t,n="/"){let r,s={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(r=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),s=e(o)),l>-1&&(r=r||t.slice(0,l),i=t.slice(l,t.length)),r=Jp(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:Jn(i)}}function qp(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Hi(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Gp(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&wn(t.matched[r],n.matched[s])&&fc(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function wn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function fc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!zp(e[n],t[n]))return!1;return!0}function zp(e,t){return Qe(e)?Ni(e,t):Qe(t)?Ni(t,e):e===t}function Ni(e,t){return Qe(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Jp(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,l;for(i=0;i<r.length;i++)if(l=r[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const Ve={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Yn;(function(e){e.pop="pop",e.push="push"})(Yn||(Yn={}));var Dn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Dn||(Dn={}));function Yp(e){if(!e)if(an){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Wp(e)}const Xp=/^[^#]+#/;function Qp(e,t){return e.replace(Xp,"#")+t}function Zp(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Gr=()=>({left:window.scrollX,top:window.scrollY});function eg(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=Zp(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function $i(e,t){return(history.state?history.state.position-t:-1)+e}const qs=new Map;function tg(e,t){qs.set(e,t)}function ng(e){const t=qs.get(e);return qs.delete(e),t}let rg=()=>location.protocol+"//"+location.host;function dc(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let l=s.includes(e.slice(o))?e.slice(o).length:1,a=s.slice(l);return a[0]!=="/"&&(a="/"+a),Hi(a,"")}return Hi(n,e)+r+s}function sg(e,t,n,r){let s=[],o=[],i=null;const l=({state:d})=>{const g=dc(e,location),_=n.value,y=t.value;let S=0;if(d){if(n.value=g,t.value=d,i&&i===_){i=null;return}S=y?d.position-y.position:0}else r(g);s.forEach(E=>{E(n.value,_,{delta:S,type:Yn.pop,direction:S?S>0?Dn.forward:Dn.back:Dn.unknown})})};function a(){i=n.value}function f(d){s.push(d);const g=()=>{const _=s.indexOf(d);_>-1&&s.splice(_,1)};return o.push(g),g}function c(){const{history:d}=window;d.state&&d.replaceState(se({},d.state,{scroll:Gr()}),"")}function u(){for(const d of o)d();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:f,destroy:u}}function ji(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?Gr():null}}function og(e){const{history:t,location:n}=window,r={value:dc(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,f,c){const u=e.indexOf("#"),d=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+a:rg()+e+a;try{t[c?"replaceState":"pushState"](f,"",d),s.value=f}catch(g){console.error(g),n[c?"replace":"assign"](d)}}function i(a,f){const c=se({},t.state,ji(s.value.back,a,s.value.forward,!0),f,{position:s.value.position});o(a,c,!0),r.value=a}function l(a,f){const c=se({},s.value,t.state,{forward:a,scroll:Gr()});o(c.current,c,!0);const u=se({},ji(r.value,a,null),{position:c.position+1},f);o(a,u,!1),r.value=a}return{location:r,state:s,push:l,replace:i}}function ig(e){e=Yp(e);const t=og(e),n=sg(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=se({location:"",base:e,go:r,createHref:Qp.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function lg(e){return typeof e=="string"||e&&typeof e=="object"}function hc(e){return typeof e=="string"||typeof e=="symbol"}const pc=Symbol("");var Fi;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Fi||(Fi={}));function En(e,t){return se(new Error,{type:e,[pc]:!0},t)}function ft(e,t){return e instanceof Error&&pc in e&&(t==null||!!(e.type&t))}const Di="[^/]+?",ag={sensitive:!1,strict:!1,start:!0,end:!0},cg=/[.+*?^${}()[\]/\\]/g;function ug(e,t){const n=se({},ag,t),r=[];let s=n.start?"^":"";const o=[];for(const f of e){const c=f.length?[]:[90];n.strict&&!f.length&&(s+="/");for(let u=0;u<f.length;u++){const d=f[u];let g=40+(n.sensitive?.25:0);if(d.type===0)u||(s+="/"),s+=d.value.replace(cg,"\\$&"),g+=40;else if(d.type===1){const{value:_,repeatable:y,optional:S,regexp:E}=d;o.push({name:_,repeatable:y,optional:S});const w=E||Di;if(w!==Di){g+=10;try{new RegExp(`(${w})`)}catch(v){throw new Error(`Invalid custom RegExp for param "${_}" (${w}): `+v.message)}}let p=y?`((?:${w})(?:/(?:${w}))*)`:`(${w})`;u||(p=S&&f.length<2?`(?:/${p})`:"/"+p),S&&(p+="?"),s+=p,g+=20,S&&(g+=-8),y&&(g+=-20),w===".*"&&(g+=-50)}c.push(g)}r.push(c)}if(n.strict&&n.end){const f=r.length-1;r[f][r[f].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function l(f){const c=f.match(i),u={};if(!c)return null;for(let d=1;d<c.length;d++){const g=c[d]||"",_=o[d-1];u[_.name]=g&&_.repeatable?g.split("/"):g}return u}function a(f){let c="",u=!1;for(const d of e){(!u||!c.endsWith("/"))&&(c+="/"),u=!1;for(const g of d)if(g.type===0)c+=g.value;else if(g.type===1){const{value:_,repeatable:y,optional:S}=g,E=_ in f?f[_]:"";if(Qe(E)&&!y)throw new Error(`Provided param "${_}" is an array but it is not repeatable (* or + modifiers)`);const w=Qe(E)?E.join("/"):E;if(!w)if(S)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):u=!0);else throw new Error(`Missing required param "${_}"`);c+=w}}return c||"/"}return{re:i,score:r,keys:o,parse:l,stringify:a}}function fg(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function gc(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=fg(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(Bi(r))return 1;if(Bi(s))return-1}return s.length-r.length}function Bi(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const dg={type:0,value:""},hg=/[a-zA-Z0-9_]/;function pg(e){if(!e)return[[]];if(e==="/")return[[dg]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${f}": ${g}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let l=0,a,f="",c="";function u(){f&&(n===0?o.push({type:0,value:f}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),f="")}function d(){f+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:a==="/"?(f&&u(),i()):a===":"?(u(),n=1):d();break;case 4:d(),n=r;break;case 1:a==="("?n=2:hg.test(a)?d():(u(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:u(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),u(),i(),s}function gg(e,t,n){const r=ug(pg(e.path),n),s=se(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function mg(e,t){const n=[],r=new Map;t=Wi({strict:!1,end:!0,sensitive:!1},t);function s(u){return r.get(u)}function o(u,d,g){const _=!g,y=Ki(u);y.aliasOf=g&&g.record;const S=Wi(t,u),E=[y];if("alias"in u){const v=typeof u.alias=="string"?[u.alias]:u.alias;for(const b of v)E.push(Ki(se({},y,{components:g?g.record.components:y.components,path:b,aliasOf:g?g.record:y})))}let w,p;for(const v of E){const{path:b}=v;if(d&&b[0]!=="/"){const C=d.record.path,k=C[C.length-1]==="/"?"":"/";v.path=d.record.path+(b&&k+b)}if(w=gg(v,d,S),g?g.alias.push(w):(p=p||w,p!==w&&p.alias.push(w),_&&u.name&&!Vi(w)&&i(u.name)),mc(w)&&a(w),y.children){const C=y.children;for(let k=0;k<C.length;k++)o(C[k],w,g&&g.children[k])}g=g||w}return p?()=>{i(p)}:Fn}function i(u){if(hc(u)){const d=r.get(u);d&&(r.delete(u),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(u);d>-1&&(n.splice(d,1),u.record.name&&r.delete(u.record.name),u.children.forEach(i),u.alias.forEach(i))}}function l(){return n}function a(u){const d=vg(u,n);n.splice(d,0,u),u.record.name&&!Vi(u)&&r.set(u.record.name,u)}function f(u,d){let g,_={},y,S;if("name"in u&&u.name){if(g=r.get(u.name),!g)throw En(1,{location:u});S=g.record.name,_=se(Ui(d.params,g.keys.filter(p=>!p.optional).concat(g.parent?g.parent.keys.filter(p=>p.optional):[]).map(p=>p.name)),u.params&&Ui(u.params,g.keys.map(p=>p.name))),y=g.stringify(_)}else if(u.path!=null)y=u.path,g=n.find(p=>p.re.test(y)),g&&(_=g.parse(y),S=g.record.name);else{if(g=d.name?r.get(d.name):n.find(p=>p.re.test(d.path)),!g)throw En(1,{location:u,currentLocation:d});S=g.record.name,_=se({},d.params,u.params),y=g.stringify(_)}const E=[];let w=g;for(;w;)E.unshift(w.record),w=w.parent;return{name:S,path:y,params:_,matched:E,meta:_g(E)}}e.forEach(u=>o(u));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:f,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:s}}function Ui(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Ki(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:yg(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function yg(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Vi(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function _g(e){return e.reduce((t,n)=>se(t,n.meta),{})}function Wi(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function vg(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;gc(e,t[o])<0?r=o:n=o+1}const s=bg(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function bg(e){let t=e;for(;t=t.parent;)if(mc(t)&&gc(e,t)===0)return t}function mc({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function wg(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(lc," "),i=o.indexOf("="),l=Jn(i<0?o:o.slice(0,i)),a=i<0?null:Jn(o.slice(i+1));if(l in t){let f=t[l];Qe(f)||(f=t[l]=[f]),f.push(a)}else t[l]=a}return t}function qi(e){let t="";for(let n in e){const r=e[n];if(n=Bp(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Qe(r)?r.map(o=>o&&Ws(o)):[r&&Ws(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Eg(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Qe(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const Sg=Symbol(""),Gi=Symbol(""),Ao=Symbol(""),Oo=Symbol(""),Gs=Symbol("");function Pn(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function At(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((l,a)=>{const f=d=>{d===!1?a(En(4,{from:n,to:t})):d instanceof Error?a(d):lg(d)?a(En(2,{from:t,to:d})):(i&&r.enterCallbacks[s]===i&&typeof d=="function"&&i.push(d),l())},c=o(()=>e.call(r&&r.instances[s],t,n,f));let u=Promise.resolve(c);e.length<3&&(u=u.then(f)),u.catch(d=>a(d))})}function hs(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(oc(a)){const c=(a.__vccOpts||a)[t];c&&o.push(At(c,n,r,i,l,s))}else{let f=a();o.push(()=>f.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const u=Ap(c)?c.default:c;i.mods[l]=c,i.components[l]=u;const g=(u.__vccOpts||u)[t];return g&&At(g,n,r,i,l,s)()}))}}return o}function zi(e){const t=Re(Ao),n=Re(Oo),r=ye(()=>{const a=me(e.to);return t.resolve(a)}),s=ye(()=>{const{matched:a}=r.value,{length:f}=a,c=a[f-1],u=n.matched;if(!c||!u.length)return-1;const d=u.findIndex(wn.bind(null,c));if(d>-1)return d;const g=Ji(a[f-2]);return f>1&&Ji(c)===g&&u[u.length-1].path!==g?u.findIndex(wn.bind(null,a[f-2])):d}),o=ye(()=>s.value>-1&&kg(n.params,r.value.params)),i=ye(()=>s.value>-1&&s.value===n.matched.length-1&&fc(n.params,r.value.params));function l(a={}){if(xg(a)){const f=t[me(e.replace)?"replace":"push"](me(e.to)).catch(Fn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:r,href:ye(()=>r.value.href),isActive:o,isExactActive:i,navigate:l}}function Tg(e){return e.length===1?e[0]:e}const Cg=Ze({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:zi,setup(e,{slots:t}){const n=wt(zi(e)),{options:r}=Re(Ao),s=ye(()=>({[Yi(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Yi(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Tg(t.default(n));return e.custom?o:we("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),Rg=Cg;function xg(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function kg(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!Qe(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function Ji(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Yi=(e,t,n)=>e??t??n,Pg=Ze({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Re(Gs),s=ye(()=>e.route||r.value),o=Re(Gi,0),i=ye(()=>{let f=me(o);const{matched:c}=s.value;let u;for(;(u=c[f])&&!u.components;)f++;return f}),l=ye(()=>s.value.matched[i.value]);Nt(Gi,ye(()=>i.value+1)),Nt(Sg,l),Nt(Gs,s);const a=lt();return $t(()=>[a.value,l.value,e.name],([f,c,u],[d,g,_])=>{c&&(c.instances[u]=f,g&&g!==c&&f&&f===d&&(c.leaveGuards.size||(c.leaveGuards=g.leaveGuards),c.updateGuards.size||(c.updateGuards=g.updateGuards))),f&&c&&(!g||!wn(c,g)||!d)&&(c.enterCallbacks[u]||[]).forEach(y=>y(f))},{flush:"post"}),()=>{const f=s.value,c=e.name,u=l.value,d=u&&u.components[c];if(!d)return Xi(n.default,{Component:d,route:f});const g=u.props[c],_=g?g===!0?f.params:typeof g=="function"?g(f):g:null,S=we(d,se({},_,t,{onVnodeUnmounted:E=>{E.component.isUnmounted&&(u.instances[c]=null)},ref:a}));return Xi(n.default,{Component:S,route:f})||S}}});function Xi(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const yc=Pg;function Ag(e){const t=mg(e.routes,e),n=e.parseQuery||wg,r=e.stringifyQuery||qi,s=e.history,o=Pn(),i=Pn(),l=Pn(),a=Yt(Ve);let f=Ve;an&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=fs.bind(null,x=>""+x),u=fs.bind(null,Kp),d=fs.bind(null,Jn);function g(x,D){let j,V;return hc(x)?(j=t.getRecordMatcher(x),V=D):V=x,t.addRoute(V,j)}function _(x){const D=t.getRecordMatcher(x);D&&t.removeRoute(D)}function y(){return t.getRoutes().map(x=>x.record)}function S(x){return!!t.getRecordMatcher(x)}function E(x,D){if(D=se({},D||a.value),typeof x=="string"){const m=ds(n,x,D.path),T=t.resolve({path:m.path},D),P=s.createHref(m.fullPath);return se(m,T,{params:d(T.params),hash:Jn(m.hash),redirectedFrom:void 0,href:P})}let j;if(x.path!=null)j=se({},x,{path:ds(n,x.path,D.path).path});else{const m=se({},x.params);for(const T in m)m[T]==null&&delete m[T];j=se({},x,{params:u(m)}),D.params=u(D.params)}const V=t.resolve(j,D),re=x.hash||"";V.params=c(d(V.params));const fe=qp(r,se({},x,{hash:Dp(re),path:V.path})),h=s.createHref(fe);return se({fullPath:fe,hash:re,query:r===qi?Eg(x.query):x.query||{}},V,{redirectedFrom:void 0,href:h})}function w(x){return typeof x=="string"?ds(n,x,a.value.path):se({},x)}function p(x,D){if(f!==x)return En(8,{from:D,to:x})}function v(x){return k(x)}function b(x){return v(se(w(x),{replace:!0}))}function C(x){const D=x.matched[x.matched.length-1];if(D&&D.redirect){const{redirect:j}=D;let V=typeof j=="function"?j(x):j;return typeof V=="string"&&(V=V.includes("?")||V.includes("#")?V=w(V):{path:V},V.params={}),se({query:x.query,hash:x.hash,params:V.path!=null?{}:x.params},V)}}function k(x,D){const j=f=E(x),V=a.value,re=x.state,fe=x.force,h=x.replace===!0,m=C(j);if(m)return k(se(w(m),{state:typeof m=="object"?se({},re,m.state):re,force:fe,replace:h}),D||j);const T=j;T.redirectedFrom=D;let P;return!fe&&Gp(r,V,j)&&(P=En(16,{to:T,from:V}),et(V,V,!0,!1)),(P?Promise.resolve(P):L(T,V)).catch(R=>ft(R)?ft(R,2)?R:St(R):U(R,T,V)).then(R=>{if(R){if(ft(R,2))return k(se({replace:h},w(R.to),{state:typeof R.to=="object"?se({},re,R.to.state):re,force:fe}),D||T)}else R=M(T,V,!0,h,re);return q(T,V,R),R})}function K(x,D){const j=p(x,D);return j?Promise.reject(j):Promise.resolve()}function I(x){const D=rn.values().next().value;return D&&typeof D.runWithContext=="function"?D.runWithContext(x):x()}function L(x,D){let j;const[V,re,fe]=Og(x,D);j=hs(V.reverse(),"beforeRouteLeave",x,D);for(const m of V)m.leaveGuards.forEach(T=>{j.push(At(T,x,D))});const h=K.bind(null,x,D);return j.push(h),Be(j).then(()=>{j=[];for(const m of o.list())j.push(At(m,x,D));return j.push(h),Be(j)}).then(()=>{j=hs(re,"beforeRouteUpdate",x,D);for(const m of re)m.updateGuards.forEach(T=>{j.push(At(T,x,D))});return j.push(h),Be(j)}).then(()=>{j=[];for(const m of fe)if(m.beforeEnter)if(Qe(m.beforeEnter))for(const T of m.beforeEnter)j.push(At(T,x,D));else j.push(At(m.beforeEnter,x,D));return j.push(h),Be(j)}).then(()=>(x.matched.forEach(m=>m.enterCallbacks={}),j=hs(fe,"beforeRouteEnter",x,D,I),j.push(h),Be(j))).then(()=>{j=[];for(const m of i.list())j.push(At(m,x,D));return j.push(h),Be(j)}).catch(m=>ft(m,8)?m:Promise.reject(m))}function q(x,D,j){l.list().forEach(V=>I(()=>V(x,D,j)))}function M(x,D,j,V,re){const fe=p(x,D);if(fe)return fe;const h=D===Ve,m=an?history.state:{};j&&(V||h?s.replace(x.fullPath,se({scroll:h&&m&&m.scroll},re)):s.push(x.fullPath,re)),a.value=x,et(x,D,j,h),St()}let G;function Z(){G||(G=s.listen((x,D,j)=>{if(!rr.listening)return;const V=E(x),re=C(V);if(re){k(se(re,{replace:!0,force:!0}),V).catch(Fn);return}f=V;const fe=a.value;an&&tg($i(fe.fullPath,j.delta),Gr()),L(V,fe).catch(h=>ft(h,12)?h:ft(h,2)?(k(se(w(h.to),{force:!0}),V).then(m=>{ft(m,20)&&!j.delta&&j.type===Yn.pop&&s.go(-1,!1)}).catch(Fn),Promise.reject()):(j.delta&&s.go(-j.delta,!1),U(h,V,fe))).then(h=>{h=h||M(V,fe,!1),h&&(j.delta&&!ft(h,8)?s.go(-j.delta,!1):j.type===Yn.pop&&ft(h,20)&&s.go(-1,!1)),q(V,fe,h)}).catch(Fn)}))}let ne=Pn(),B=Pn(),Q;function U(x,D,j){St(x);const V=B.list();return V.length?V.forEach(re=>re(x,D,j)):console.error(x),Promise.reject(x)}function pe(){return Q&&a.value!==Ve?Promise.resolve():new Promise((x,D)=>{ne.add([x,D])})}function St(x){return Q||(Q=!x,Z(),ne.list().forEach(([D,j])=>x?j(x):D()),ne.reset()),x}function et(x,D,j,V){const{scrollBehavior:re}=e;if(!an||!re)return Promise.resolve();const fe=!j&&ng($i(x.fullPath,0))||(V||!j)&&history.state&&history.state.scroll||null;return yn().then(()=>re(x,D,fe)).then(h=>h&&eg(h)).catch(h=>U(h,x,D))}const Ie=x=>s.go(x);let nn;const rn=new Set,rr={currentRoute:a,listening:!0,addRoute:g,removeRoute:_,clearRoutes:t.clearRoutes,hasRoute:S,getRoutes:y,resolve:E,options:e,push:v,replace:b,go:Ie,back:()=>Ie(-1),forward:()=>Ie(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:B.add,isReady:pe,install(x){const D=this;x.component("RouterLink",Rg),x.component("RouterView",yc),x.config.globalProperties.$router=D,Object.defineProperty(x.config.globalProperties,"$route",{enumerable:!0,get:()=>me(a)}),an&&!nn&&a.value===Ve&&(nn=!0,v(s.location).catch(re=>{}));const j={};for(const re in Ve)Object.defineProperty(j,re,{get:()=>a.value[re],enumerable:!0});x.provide(Ao,D),x.provide(Oo,ot(j)),x.provide(Gs,a);const V=x.unmount;rn.add(x),x.unmount=function(){rn.delete(x),rn.size<1&&(f=Ve,G&&G(),G=null,a.value=Ve,nn=!1,Q=!1),V()}}};function Be(x){return x.reduce((D,j)=>D.then(()=>I(j)),Promise.resolve())}return rr}function Og(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(f=>wn(f,l))?r.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(f=>wn(f,a))||s.push(a))}return[n,r,s]}function _c(e){return Re(Oo)}const Mg=/(:\w+)\([^)]+\)/g,Lg=/(:\w+)[?+*]/g,Ig=/:\w+/g,Hg=(e,t)=>t.path.replace(Mg,"$1").replace(Lg,"$1").replace(Ig,n=>{var r;return((r=e.params[n.slice(1)])==null?void 0:r.toString())||""}),zs=(e,t)=>{const n=e.route.matched.find(s=>{var o;return((o=s.components)==null?void 0:o.default)===e.Component.type}),r=t??(n==null?void 0:n.meta.key)??(n&&Hg(e.route,n));return typeof r=="function"?r(e.route):r},Ng=(e,t)=>({default:()=>e?we(Pu,e===!0?{}:e,t):t});function Mo(e){return Array.isArray(e)?e:[e]}const $g={layout:"default",prerender:!0},jg={layout:"default",prerender:!0},ps=[{name:"index",path:"/",meta:$g||{},component:()=>Vs(()=>import("./BQiff4QA.js"),__vite__mapDeps([0,1,2,3]),import.meta.url)},{name:"slug",path:"/:slug()",meta:jg||{},component:()=>Vs(()=>import("./DyjXTDH7.js"),__vite__mapDeps([4,1,2,5]),import.meta.url)}],vc=(e,t)=>({default:()=>{var n;return e?we(If,e===!0?{}:e,t):(n=t.default)==null?void 0:n.call(t)}}),Fg=/(:\w+)\([^)]+\)/g,Dg=/(:\w+)[?+*]/g,Bg=/:\w+/g;function Qi(e){const t=(e==null?void 0:e.meta.key)??e.path.replace(Fg,"$1").replace(Dg,"$1").replace(Bg,n=>{var r;return((r=e.params[n.slice(1)])==null?void 0:r.toString())||""});return typeof t=="function"?t(e):t}function Ug(e,t){return e===t||t===Ve?!1:Qi(e)!==Qi(t)?!0:!e.matched.every((r,s)=>{var o,i;return r.components&&r.components.default===((i=(o=t.matched[s])==null?void 0:o.components)==null?void 0:i.default)})}const Kg={scrollBehavior(e,t,n){var a;const r=ge(),s=((a=Le().options)==null?void 0:a.scrollBehaviorType)??"auto";if(e.path===t.path)return t.hash&&!e.hash?{left:0,top:0}:e.hash?{el:e.hash,top:bc(e.hash),behavior:s}:!1;if((typeof e.meta.scrollToTop=="function"?e.meta.scrollToTop(e,t):e.meta.scrollToTop)===!1)return!1;let i=n||void 0;!i&&Ug(e,t)&&(i={left:0,top:0});const l=r._runningTransition?"page:transition:finish":"page:loading:end";return new Promise(f=>{if(t===Ve){f(Zi(e,"instant",i));return}r.hooks.hookOnce(l,()=>{requestAnimationFrame(()=>f(Zi(e,"instant",i)))})})}};function bc(e){try{const t=document.querySelector(e);if(t)return(Number.parseFloat(getComputedStyle(t).scrollMarginTop)||0)+(Number.parseFloat(getComputedStyle(document.documentElement).scrollPaddingTop)||0)}catch{}return 0}function Zi(e,t,n){return n||(e.hash?{el:e.hash,top:bc(e.hash),behavior:t}:{left:0,top:0,behavior:t})}const Vg={strict:!1,hashMode:!1,scrollBehaviorType:"auto"},nt={...Vg,...Kg},Wg=async(e,t)=>{var i;let n,r;if(!((i=e.meta)!=null&&i.validate))return;const s=([n,r]=mn(()=>Promise.resolve(e.meta.validate(e))),n=await n,r(),n);if(s===!0)return;const o=Jt({fatal:!0,statusCode:s&&s.statusCode||404,statusMessage:s&&s.statusMessage||`Page Not Found: ${e.fullPath}`,data:{path:e.fullPath}});return typeof window<"u"&&window.history.pushState({},"",t.fullPath),o},qg=async e=>{let t,n;const r=([t,n]=mn(()=>sc({path:e.path})),t=await t,n(),t);if(r.redirect)return jt(r.redirect,{acceptRelative:!0})?(window.location.href=r.redirect,!1):r.redirect},Gg=[Wg,qg],Bn={};function zg(e,t,n){const{pathname:r,search:s,hash:o}=t,i=e.indexOf("#");if(i>-1){const f=o.includes(e.slice(i))?e.slice(i).length:1;let c=o.slice(f);return c[0]!=="/"&&(c="/"+c),yi(c,"")}const l=yi(r,e),a=!n||Md(l,n)?l:n;return a+(a.includes("?")?"":s)+o}const Jg=at({name:"nuxt:router",enforce:"pre",async setup(e){var S;let t,n,r=nr().app.baseURL;const s=((S=nt.history)==null?void 0:S.call(nt,r))??ig(r),o=nt.routes?([t,n]=mn(()=>nt.routes(ps)),t=await t,n(),t??ps):ps;let i;const l=Ag({...nt,scrollBehavior:(E,w,p)=>{if(w===Ve){i=p;return}if(nt.scrollBehavior){if(l.options.scrollBehavior=nt.scrollBehavior,"scrollRestoration"in window.history){const v=l.beforeEach(()=>{v(),window.history.scrollRestoration="manual"})}return nt.scrollBehavior(E,Ve,i||p)}},history:s,routes:o});"scrollRestoration"in window.history&&(window.history.scrollRestoration="auto"),e.vueApp.use(l);const a=Yt(l.currentRoute.value);l.afterEach((E,w)=>{a.value=w}),Object.defineProperty(e.vueApp.config.globalProperties,"previousRoute",{get:()=>a.value});const f=zg(r,window.location,e.payload.path),c=Yt(l.currentRoute.value),u=()=>{c.value=l.currentRoute.value};e.hook("page:finish",u),l.afterEach((E,w)=>{var p,v,b,C;((v=(p=E.matched[0])==null?void 0:p.components)==null?void 0:v.default)===((C=(b=w.matched[0])==null?void 0:b.components)==null?void 0:C.default)&&u()});const d={};for(const E in c.value)Object.defineProperty(d,E,{get:()=>c.value[E],enumerable:!0});e._route=ot(d),e._middleware||(e._middleware={global:[],named:{}});const g=qr();l.afterEach(async(E,w,p)=>{delete e._processingMiddleware,!e.isHydrating&&g.value&&await e.runWithContext(qa),p&&await e.callHook("page:loading:end")});try{[t,n]=mn(()=>l.isReady()),await t,n()}catch(E){[t,n]=mn(()=>e.runWithContext(()=>Vt(E))),await t,n()}const _=f!==l.currentRoute.value.fullPath?l.resolve(f):l.currentRoute.value;u();const y=e.payload.state._layout;return l.beforeEach(async(E,w)=>{var p;await e.callHook("page:loading:start"),E.meta=wt(E.meta),e.isHydrating&&y&&!_t(E.meta.layout)&&(E.meta.layout=y),e._processingMiddleware=!0;{const v=new Set([...Gg,...e._middleware.global]);for(const b of E.matched){const C=b.meta.middleware;if(C)for(const k of Mo(C))v.add(k)}{const b=await e.runWithContext(()=>sc({path:E.path}));if(b.appMiddleware)for(const C in b.appMiddleware)b.appMiddleware[C]?v.add(C):v.delete(C)}for(const b of v){const C=typeof b=="string"?e._middleware.named[b]||await((p=Bn[b])==null?void 0:p.call(Bn).then(k=>k.default||k)):b;if(!C)throw new Error(`Unknown route middleware: '${b}'.`);try{const k=await e.runWithContext(()=>C(E,w));if(!e.payload.serverRendered&&e.isHydrating&&(k===!1||k instanceof Error)){const K=k||Jt({statusCode:404,statusMessage:`Page Not Found: ${f}`});return await e.runWithContext(()=>Vt(K)),!1}if(k===!0)continue;if(k===!1)return k;if(k)return Ga(k)&&k.fatal&&await e.runWithContext(()=>Vt(k)),k}catch(k){const K=Jt(k);return K.fatal&&await e.runWithContext(()=>Vt(K)),K}}}}),l.onError(async()=>{delete e._processingMiddleware,await e.callHook("page:loading:end")}),l.afterEach(async(E,w)=>{E.matched.length===0&&await e.runWithContext(()=>Vt(Jt({statusCode:404,fatal:!1,statusMessage:`Page not found: ${E.fullPath}`,data:{path:E.fullPath}})))}),e.hooks.hookOnce("app:created",async()=>{try{"name"in _&&(_.name=void 0),await l.replace({..._,force:!0}),l.options.scrollBehavior=nt.scrollBehavior}catch(E){await e.runWithContext(()=>Vt(E))}}),{provide:{router:l}}}}),Js=globalThis.requestIdleCallback||(e=>{const t=Date.now(),n={didTimeout:!1,timeRemaining:()=>Math.max(0,50-(Date.now()-t))};return setTimeout(()=>{e(n)},1)}),Yg=globalThis.cancelIdleCallback||(e=>{clearTimeout(e)}),Lo=e=>{const t=ge();t.isHydrating?t.hooks.hookOnce("app:suspense:resolve",()=>{Js(()=>e())}):Js(()=>e())},Xg=at(()=>{const e=Le();Lo(()=>{e.beforeResolve(async()=>{await new Promise(t=>{setTimeout(t,100),requestAnimationFrame(()=>{setTimeout(t,0)})})})})}),Qg=at(e=>{let t;async function n(){const r=await rc();t&&clearTimeout(t),t=setTimeout(n,Si);try{const s=await $fetch(To("builds/latest.json")+`?${Date.now()}`);s.id!==r.id&&e.hooks.callHook("app:manifest:update",s)}catch{}}Lo(()=>{t=setTimeout(n,Si)})});function Zg(e={}){const t=e.path||window.location.pathname;let n={};try{n=Ar(sessionStorage.getItem("nuxt:reload")||"{}")}catch{}if(e.force||(n==null?void 0:n.path)!==t||(n==null?void 0:n.expires)<Date.now()){try{sessionStorage.setItem("nuxt:reload",JSON.stringify({path:t,expires:Date.now()+(e.ttl??1e4)}))}catch{}if(e.persistState)try{sessionStorage.setItem("nuxt:reload:state",JSON.stringify({state:ge().payload.state}))}catch{}window.location.pathname!==t?window.location.href=t:window.location.reload()}}const em=at({name:"nuxt:chunk-reload",setup(e){const t=Le(),n=nr(),r=new Set;t.beforeEach(()=>{r.clear()}),e.hook("app:chunkError",({error:o})=>{r.add(o)});function s(o){const l="href"in o&&o.href[0]==="#"?n.app.baseURL+o.href:So(n.app.baseURL,o.fullPath);Zg({path:l,persistState:!0})}e.hook("app:manifest:update",()=>{t.beforeResolve(s)}),t.onError((o,i)=>{r.has(o)&&s(i)})}}),tm=at({name:"nuxt:global-components"}),Lt={default:xu(()=>Vs(()=>import("./CEsPZEqm.js"),[],import.meta.url).then(e=>e.default||e))};function nm(e){if(e!=null&&e.__asyncLoader&&!e.__asyncResolved)return e.__asyncLoader()}async function wc(e,t=Le()){const{path:n,matched:r}=t.resolve(e);if(!r.length||(t._routePreloaded||(t._routePreloaded=new Set),t._routePreloaded.has(n)))return;const s=t._preloadPromises||(t._preloadPromises=[]);if(s.length>4)return Promise.all(s).then(()=>wc(e,t));t._routePreloaded.add(n);const o=r.map(i=>{var l;return(l=i.components)==null?void 0:l.default}).filter(i=>typeof i=="function");for(const i of o){const l=Promise.resolve(i()).catch(()=>{}).finally(()=>s.splice(s.indexOf(l)));s.push(l)}await Promise.all(s)}const rm=at({name:"nuxt:prefetch",setup(e){const t=Le();e.hooks.hook("app:mounted",()=>{t.beforeEach(async n=>{var s;const r=(s=n==null?void 0:n.meta)==null?void 0:s.layout;r&&typeof Lt[r]=="function"&&await Lt[r]()})}),e.hooks.hook("link:prefetch",n=>{if(jt(n))return;const r=t.resolve(n);if(!r)return;const s=r.meta.layout;let o=Mo(r.meta.middleware);o=o.filter(i=>typeof i=="string");for(const i of o)typeof Bn[i]=="function"&&Bn[i]();typeof s=="string"&&s in Lt&&nm(Lt[s])})}}),sm="__NUXT_COLOR_MODE__",gs="nuxt-color-mode",om="localStorage",im="$s";function Ec(...e){const t=typeof e[e.length-1]=="string"?e.pop():void 0;typeof e[0]!="string"&&e.unshift(t);const[n,r]=e;if(!n||typeof n!="string")throw new TypeError("[nuxt] [useState] key must be a string: "+n);if(r!==void 0&&typeof r!="function")throw new Error("[nuxt] [useState] init must be a function: "+r);const s=im+n,o=ge(),i=Pl(o.payload.state,s);if(i.value===void 0&&r){const l=r();if(ve(l))return o.payload.state[s]=l,l;i.value=l}return i}const dt=window[sm]||{},lm=at(e=>{const t=Ec("color-mode",()=>wt({preference:dt.preference,value:dt.value,unknown:!1,forced:!1})).value;Le().afterEach(o=>{const i=o.meta.colorMode;i&&i!=="system"?(t.value=i,t.forced=!0):(i==="system"&&console.warn("You cannot force the colorMode to system at the page level."),t.forced=!1,t.value=t.preference==="system"?dt.getColorScheme():t.preference)});let n;function r(){n||!window.matchMedia||(n=window.matchMedia("(prefers-color-scheme: dark)"),n.addEventListener("change",()=>{!t.forced&&t.preference==="system"&&(t.value=dt.getColorScheme())}))}function s(o,i){var l,a;switch(o){case"cookie":window.document.cookie=gs+"="+i;break;case"sessionStorage":(l=window.sessionStorage)==null||l.setItem(gs,i);break;case"localStorage":default:(a=window.localStorage)==null||a.setItem(gs,i)}}$t(()=>t.preference,o=>{t.forced||(o==="system"?(t.value=dt.getColorScheme(),r()):t.value=o,s(om,o))},{immediate:!0}),$t(()=>t.value,(o,i)=>{dt.removeColorScheme(i),dt.addColorScheme(o)}),t.preference==="system"&&r(),e.hook("app:mounted",()=>{t.unknown&&(t.preference=dt.preference,t.value=dt.value,t.unknown=!1)}),e.provide("colorMode",t)}),am=()=>Ec("color-mode").value,cm=at(()=>{console.log("Nuxt app initialized on client");const e=am();{const n=window.matchMedia("(prefers-color-scheme: dark)");if(e.preference==="system"){const r=()=>{e.value=n.matches?"dark":"light"};r(),n.addEventListener("change",r),tn(()=>{n.removeEventListener("change",r)})}}{const n=nr();n.public.gtag&&console.log("Google Analytics initialized"),n.public.baiduAnalytics&&console.log("Baidu Analytics initialized")}const t=n=>{console.error("Global error:",n)};window.addEventListener("error",t),window.addEventListener("unhandledrejection",n=>{t(n.reason)}),"performance"in window&&window.addEventListener("load",()=>{setTimeout(()=>{const n=performance.getEntriesByType("navigation")[0];if(n){const r=n.loadEventEnd-n.loadEventStart,s=n.domContentLoadedEventEnd-n.domContentLoadedEventStart;console.log("Performance metrics:",{loadTime:r,domContentLoaded:s,totalTime:n.loadEventEnd-n.fetchStart})}},0)});{const n=localStorage.getItem("user-preferences");if(n)try{const r=JSON.parse(n);console.log("User preferences loaded:",r)}catch(r){console.warn("Failed to parse user preferences:",r),localStorage.removeItem("user-preferences")}}{const n=r=>{if(r.code==="Space"&&!r.ctrlKey&&!r.metaKey&&!r.altKey){const s=document.activeElement;if((s==null?void 0:s.tagName)!=="INPUT"&&(s==null?void 0:s.tagName)!=="TEXTAREA"){r.preventDefault();const o=document.querySelector(".startButton");o&&!o.disabled&&o.click()}}if(r.code==="Escape"){const s=document.querySelector(".settingsPanel.open");if(s){const o=s.querySelector("button");o==null||o.click()}}};document.addEventListener("keydown",n)}if("ontouchstart"in window){let n=0,r=0;const s=i=>{n=i.touches[0].clientY,r=i.touches[0].clientX},o=i=>{const l=i.changedTouches[0].clientY,a=i.changedTouches[0].clientX,f=n-l,c=r-a;if(Math.abs(f)>Math.abs(c)&&f>50){const u=document.querySelector(".settingsButton");u&&u.click()}};document.addEventListener("touchstart",s,{passive:!0}),document.addEventListener("touchend",o,{passive:!0})}return{provide:{trackEvent:(n,r)=>{window.gtag&&window.gtag("event",n,r)},showNotification:(n,r="info")=>{console.log(`[${r.toUpperCase()}] ${n}`)}}}}),um=[Tp,Pp,Jg,Xg,Qg,em,tm,rm,lm,cm],Sc=(e="RouteProvider")=>Ze({name:e,props:{route:{type:Object,required:!0},vnode:Object,vnodeRef:Object,renderKey:String,trackRootNodes:Boolean},setup(t){const n=t.renderKey,r=t.route,s={};for(const o in t.route)Object.defineProperty(s,o,{get:()=>n===t.renderKey?t.route[o]:r[o],enumerable:!0});return Nt(Zt,ot(s)),()=>t.vnode?we(t.vnode,{ref:t.vnodeRef}):t.vnode}}),fm=Sc(),el=new WeakMap,dm=Ze({name:"NuxtPage",inheritAttrs:!1,props:{name:{type:String},transition:{type:[Boolean,Object],default:void 0},keepalive:{type:[Boolean,Object],default:void 0},route:{type:Object},pageKey:{type:[Function,String],default:null}},setup(e,{attrs:t,slots:n,expose:r}){const s=ge(),o=lt(),i=Re(Zt,null);let l;r({pageRef:o});const a=Re(Ka,null);let f;const c=s.deferHydration();if(s.isHydrating){const d=s.hooks.hookOnce("app:error",c);Le().beforeEach(d)}e.pageKey&&$t(()=>e.pageKey,(d,g)=>{d!==g&&s.callHook("page:loading:start")});let u=!1;{const d=Le().beforeResolve(()=>{u=!1});tn(()=>{d()})}return()=>we(yc,{name:e.name,route:e.route,...t},{default:d=>{const g=pm(i,d.route,d.Component),_=i&&i.matched.length===d.route.matched.length;if(!d.Component){if(f&&!_)return f;c();return}if(f&&a&&!a.isCurrent(d.route))return f;if(g&&i&&(!a||a!=null&&a.isCurrent(i)))return _?f:null;const y=zs(d,e.pageKey),S=gm(i,d.route,d.Component);!s.isHydrating&&l===y&&!S&&yn(()=>{u=!0,s.callHook("page:loading:end")}),l=y;const E=!!(e.transition??d.route.meta.pageTransition??Ei),w=E&&hm([e.transition,d.route.meta.pageTransition,Ei,{onBeforeLeave(){s._runningTransition=!0},onAfterLeave(){delete s._runningTransition,s.callHook("page:transition:finish",d.Component)}}]),p=e.keepalive??d.route.meta.keepalive??ch;return f=vc(E&&w,Ng(p,we(mo,{suspensible:!0,onPending:()=>s.callHook("page:start",d.Component),onResolve:()=>{yn(()=>s.callHook("page:finish",d.Component).then(()=>{if(!u&&!S)return u=!0,s.callHook("page:loading:end")}).finally(c))}},{default:()=>{const v={key:y||void 0,vnode:n.default?mm(n.default,d):d.Component,route:d.route,renderKey:y||void 0,trackRootNodes:E,vnodeRef:o};if(!p)return we(fm,v);const b=d.Component.type,C=b;let k=el.get(C);return k||(k=Sc(b.name||b.__name),el.set(C,k)),we(k,v)}}))).default(),f}})}});function hm(e){const t=e.filter(Boolean).map(n=>({...n,onAfterLeave:n.onAfterLeave?Mo(n.onAfterLeave):void 0}));return Ba(...t)}function pm(e,t,n){if(!e)return!1;const r=t.matched.findIndex(s=>{var o;return((o=s.components)==null?void 0:o.default)===(n==null?void 0:n.type)});return!r||r===-1?!1:t.matched.slice(0,r).some((s,o)=>{var i,l,a;return((i=s.components)==null?void 0:i.default)!==((a=(l=e.matched[o])==null?void 0:l.components)==null?void 0:a.default)})||n&&zs({route:t,Component:n})!==zs({route:e,Component:n})}function gm(e,t,n){return e?t.matched.findIndex(s=>{var o;return((o=s.components)==null?void 0:o.default)===(n==null?void 0:n.type)})<t.matched.length-1:!1}function mm(e,t){const n=e(t);return n.length===1?we(n[0]):we(Ae,void 0,n)}const ym=Ze({name:"LayoutLoader",inheritAttrs:!1,props:{name:String,layoutProps:Object},setup(e,t){return()=>we(Lt[e.name],e.layoutProps,t.slots)}}),_m={name:{type:[String,Boolean,Object],default:null},fallback:{type:[String,Object],default:null}},vm=Ze({name:"NuxtLayout",inheritAttrs:!1,props:_m,setup(e,t){const n=ge(),r=Re(Zt),o=!r||r===Co()?_c():r,i=ye(()=>{let c=me(e.name)??(o==null?void 0:o.meta.layout)??"default";return c&&!(c in Lt)&&e.fallback&&(c=me(e.fallback)),c}),l=Yt();t.expose({layoutRef:l});const a=n.deferHydration();if(n.isHydrating){const c=n.hooks.hookOnce("app:error",a);Le().beforeEach(c)}let f;return()=>{const c=i.value&&i.value in Lt,u=(o==null?void 0:o.meta.layoutTransition)??ah,d=f;return f=i.value,vc(c&&u,{default:()=>we(mo,{suspensible:!0,onResolve:()=>{yn(a)}},{default:()=>we(bm,{layoutProps:_a(t.attrs,{ref:l}),key:i.value||void 0,name:i.value,shouldProvide:!e.name,isRenderingNewLayout:g=>g!==d&&g===i.value,hasTransition:!!u},t.slots)})}).default()}}}),bm=Ze({name:"NuxtLayoutProvider",inheritAttrs:!1,props:{name:{type:[String,Boolean]},layoutProps:{type:Object},hasTransition:{type:Boolean},shouldProvide:{type:Boolean},isRenderingNewLayout:{type:Function,required:!0}},setup(e,t){const n=e.name;e.shouldProvide&&Nt(Ka,{isCurrent:o=>n===(o.meta.layout??"default")});const r=Re(Zt);if(r&&r===Co()){const o=_c(),i={};for(const l in o){const a=l;Object.defineProperty(i,a,{enumerable:!0,get:()=>e.isRenderingNewLayout(e.name)?o[a]:r[a]})}Nt(Zt,ot(i))}return()=>{var o,i;return!n||typeof n=="string"&&!(n in Lt)?(i=(o=t.slots).default)==null?void 0:i.call(o):we(ym,{key:n,layoutProps:e.layoutProps,name:n},t.slots)}}}),wm=Ze({__name:"app",setup(e){return pp({htmlAttrs:{lang:"en"}}),Tn(()=>{console.log("Nuxt app mounted")}),(t,n)=>{const r=dm,s=vm;return We(),yo("div",null,[de(s,null,{default:Br(()=>[de(r)]),_:1})])}}}),Em=(...e)=>e.find(t=>t!==void 0);function Sm(e){const t=e.componentName||"NuxtLink";function n(o){return typeof o=="string"&&o.startsWith("#")}function r(o,i,l){const a=l??e.trailingSlash;if(!o||a!=="append"&&a!=="remove")return o;if(typeof o=="string")return pr(o,a);const f="path"in o&&o.path!==void 0?o.path:i(o).path;return{...o,name:void 0,path:pr(f,a)}}function s(o){const i=Le(),l=nr(),a=ye(()=>!!o.target&&o.target!=="_self"),f=ye(()=>{const S=o.to||o.href||"";return typeof S=="string"&&jt(S,{acceptRelative:!0})}),c=Ko("RouterLink"),u=c&&typeof c!="string"?c.useLink:void 0,d=ye(()=>{if(o.external)return!0;const S=o.to||o.href||"";return typeof S=="object"?!1:S===""||f.value}),g=ye(()=>{const S=o.to||o.href||"";return d.value?S:r(S,i.resolve,o.trailingSlash)}),_=d.value||u==null?void 0:u({...o,to:g}),y=ye(()=>{var E;const S=o.trailingSlash??e.trailingSlash;if(!g.value||f.value||n(g.value))return g.value;if(d.value){const w=typeof g.value=="object"&&"path"in g.value?Fs(g.value):g.value,p=typeof w=="object"?i.resolve(w).href:w;return pr(p,S)}return typeof g.value=="object"?((E=i.resolve(g.value))==null?void 0:E.href)??null:pr(So(l.app.baseURL,g.value),S)});return{to:g,hasTarget:a,isAbsoluteUrl:f,isExternal:d,href:y,isActive:(_==null?void 0:_.isActive)??ye(()=>g.value===i.currentRoute.value.path),isExactActive:(_==null?void 0:_.isExactActive)??ye(()=>g.value===i.currentRoute.value.path),route:(_==null?void 0:_.route)??ye(()=>i.resolve(g.value)),async navigate(S){await Va(y.value,{replace:o.replace,external:d.value||a.value})}}}return Ze({name:t,props:{to:{type:[String,Object],default:void 0,required:!1},href:{type:[String,Object],default:void 0,required:!1},target:{type:String,default:void 0,required:!1},rel:{type:String,default:void 0,required:!1},noRel:{type:Boolean,default:void 0,required:!1},prefetch:{type:Boolean,default:void 0,required:!1},prefetchOn:{type:[String,Object],default:void 0,required:!1},noPrefetch:{type:Boolean,default:void 0,required:!1},activeClass:{type:String,default:void 0,required:!1},exactActiveClass:{type:String,default:void 0,required:!1},prefetchedClass:{type:String,default:void 0,required:!1},replace:{type:Boolean,default:void 0,required:!1},ariaCurrentValue:{type:String,default:void 0,required:!1},external:{type:Boolean,default:void 0,required:!1},custom:{type:Boolean,default:void 0,required:!1},trailingSlash:{type:String,default:void 0,required:!1}},useLink:s,setup(o,{slots:i}){const l=Le(),{to:a,href:f,navigate:c,isExternal:u,hasTarget:d,isAbsoluteUrl:g}=s(o),_=Yt(!1),y=lt(null),S=p=>{var v;y.value=o.custom?(v=p==null?void 0:p.$el)==null?void 0:v.nextElementSibling:p==null?void 0:p.$el};function E(p){var v,b;return!_.value&&(typeof o.prefetchOn=="string"?o.prefetchOn===p:((v=o.prefetchOn)==null?void 0:v[p])??((b=e.prefetchOn)==null?void 0:b[p]))&&(o.prefetch??e.prefetch)!==!1&&o.noPrefetch!==!0&&o.target!=="_blank"&&!xm()}async function w(p=ge()){if(_.value)return;_.value=!0;const v=typeof a.value=="string"?a.value:u.value?Fs(a.value):l.resolve(a.value).fullPath,b=u.value?new URL(v,window.location.href).href:v;await Promise.all([p.hooks.callHook("link:prefetch",b).catch(()=>{}),!u.value&&!d.value&&wc(a.value,l).catch(()=>{})])}if(E("visibility")){const p=ge();let v,b=null;Tn(()=>{const C=Cm();Lo(()=>{v=Js(()=>{var k;(k=y==null?void 0:y.value)!=null&&k.tagName&&(b=C.observe(y.value,async()=>{b==null||b(),b=null,await w(p)}))})})}),tn(()=>{v&&Yg(v),b==null||b(),b=null})}return()=>{var b;if(!u.value&&!d.value&&!n(a.value)){const C={ref:S,to:a.value,activeClass:o.activeClass||e.activeClass,exactActiveClass:o.exactActiveClass||e.exactActiveClass,replace:o.replace,ariaCurrentValue:o.ariaCurrentValue,custom:o.custom};return o.custom||(E("interaction")&&(C.onPointerenter=w.bind(null,void 0),C.onFocus=w.bind(null,void 0)),_.value&&(C.class=o.prefetchedClass||e.prefetchedClass),C.rel=o.rel||void 0),we(Ko("RouterLink"),C,i.default)}const p=o.target||null,v=Em(o.noRel?"":o.rel,e.externalRelAttribute,g.value||d.value?"noopener noreferrer":"")||null;return o.custom?i.default?i.default({href:f.value,navigate:c,prefetch:w,get route(){if(!f.value)return;const C=new URL(f.value,window.location.href);return{path:C.pathname,fullPath:C.pathname,get query(){return Eo(C.search)},hash:C.hash,params:{},name:void 0,matched:[],redirectedFrom:void 0,meta:{},href:f.value}},rel:v,target:p,isExternal:u.value||d.value,isActive:!1,isExactActive:!1}):null:we("a",{ref:y,href:f.value||null,rel:v,target:p},(b=i.default)==null?void 0:b.call(i))}}})}const Tm=Sm(uh);function pr(e,t){const n=t==="append"?Ra:Gn;return jt(e)&&!e.startsWith("http")?e:n(e,!0)}function Cm(){const e=ge();if(e._observer)return e._observer;let t=null;const n=new Map,r=(o,i)=>(t||(t=new IntersectionObserver(l=>{for(const a of l){const f=n.get(a.target);(a.isIntersecting||a.intersectionRatio>0)&&f&&f()}})),n.set(o,i),t.observe(o),()=>{n.delete(o),t==null||t.unobserve(o),n.size===0&&(t==null||t.disconnect(),t=null)});return e._observer={observe:r}}const Rm=/2g/;function xm(){const e=navigator.connection;return!!(e&&(e.saveData||Rm.test(e.effectiveType)))}const km={class:"min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center px-4"},Pm={class:"max-w-md w-full text-center"},Am={class:"text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4"},Om={class:"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-4"},Mm={class:"text-gray-600 dark:text-gray-400 mb-8"},Lm={class:"space-y-4"},Im=Ze({__name:"error",props:{error:{}},setup(e){const t=e,n=()=>{switch(t.error.statusCode){case 404:return"Page Not Found";case 500:return"Internal Server Error";case 403:return"Access Forbidden";case 401:return"Unauthorized";case 400:return"Bad Request";default:return"Something went wrong"}},r=()=>{if(t.error.statusMessage)return t.error.statusMessage;switch(t.error.statusCode){case 404:return"The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.";case 500:return"We're experiencing some technical difficulties. Please try again later.";case 403:return"You don't have permission to access this resource.";case 401:return"You need to be authenticated to access this page.";case 400:return"The request was invalid. Please check your input and try again.";default:return"An unexpected error occurred. Please try again later."}},s=()=>{switch(t.error.statusCode){case 404:return"Go Back";case 500:return"Retry";default:return"Try Again"}},o=()=>{switch(t.error.statusCode){case 404:window.history.length>1?window.history.back():Va("/");break;case 500:window.location.reload();break;default:qa({redirect:"/"});break}};return gp({title:`${t.error.statusCode} - ${n()}`,description:r(),robots:"noindex, nofollow"}),Tn(()=>{console.error("Page Error:",{statusCode:t.error.statusCode,message:t.error.message,url:window.location.href,userAgent:navigator.userAgent,timestamp:new Date().toISOString()})}),(i,l)=>{const a=Tm;return We(),yo("div",km,[Ke("div",Pm,[l[2]||(l[2]=Ke("div",{class:"mb-8"},[Ke("div",{class:"mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center"},[Ke("svg",{class:"w-12 h-12 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[Ke("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])])],-1)),Ke("h1",Am,An(i.error.statusCode),1),Ke("h2",Om,An(n()),1),Ke("p",Mm,An(r()),1),Ke("div",Lm,[Ke("button",{onClick:o,class:"w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"},An(s()),1),de(a,{to:"/",class:"block w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors font-medium"},{default:Br(()=>l[0]||(l[0]=[_o(" Go to Homepage ")])),_:1,__:[0]})]),vf("",!0)])])}}}),Hm=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},Nm=Hm(Im,[["__scopeId","data-v-06dc5cd9"]]),$m={key:0},tl={__name:"nuxt-root",setup(e){const t=()=>null,n=ge(),r=n.deferHydration();if(n.isHydrating){const f=n.hooks.hookOnce("app:error",r);Le().beforeEach(f)}const s=!1;Nt(Zt,Co()),n.hooks.callHookWith(f=>f.map(c=>c()),"vue:setup");const o=qr(),i=!1,l=/bot\b|chrome-lighthouse|facebookexternalhit|google\b/i;Wl((f,c,u)=>{if(n.hooks.callHook("vue:error",f,c,u).catch(d=>console.error("[nuxt] Error in `vue:error` hook",d)),l.test(navigator.userAgent))return n.hooks.callHook("app:error",f),console.error(`[nuxt] Not rendering error page for bot with user agent \`${navigator.userAgent}\`:`,f),!1;if(Ga(f)&&(f.fatal||f.unhandled))return n.runWithContext(()=>Vt(f)),!1});const a=!1;return(f,c)=>(We(),Mt(mo,{onResolve:me(r)},{default:Br(()=>[me(i)?(We(),yo("div",$m)):me(o)?(We(),Mt(me(Nm),{key:1,error:me(o)},null,8,["error"])):me(a)?(We(),Mt(me(t),{key:2,context:me(a)},null,8,["context"])):me(s)?(We(),Mt(Nu(me(s)),{key:3})):(We(),Mt(me(wm),{key:4}))]),_:1},8,["onResolve"]))}};let nl;{let e;nl=async function(){var i,l;if(e)return e;const r=!!(((i=window.__NUXT__)==null?void 0:i.serverRendered)??((l=document.getElementById("__NUXT_DATA__"))==null?void 0:l.dataset.ssr)==="true")?sd(tl):rd(tl),s=gh({vueApp:r});async function o(a){var f;await s.callHook("app:error",a),(f=s.payload).error||(f.error=Jt(a))}r.config.errorHandler=o,s.hook("app:suspense:resolve",()=>{r.config.errorHandler===o&&(r.config.errorHandler=void 0)});try{await _h(s,um)}catch(a){o(a)}try{await s.hooks.callHook("app:created",r),await s.hooks.callHook("app:beforeMount",r),r.mount(dh),await s.hooks.callHook("app:mounted",r),await yn()}catch(a){o(a)}return r},e=nl().catch(t=>{throw console.error("Error while mounting app:",t),t})}export{tr as A,Ou as B,Vl as C,Re as D,wt as E,Ae as F,Vm as G,ol as H,Bm as I,Hm as _,pp as a,yo as b,ye as c,Ze as d,de as e,Ke as f,vf as g,me as h,Dm as i,Co as j,Jt as k,lt as l,$t as m,lu as n,We as o,ge as p,Km as q,Ko as r,Fm as s,An as t,gp as u,Yt as v,Um as w,Pl as x,yn as y,fl as z};
