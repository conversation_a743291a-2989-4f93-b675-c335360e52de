import{v as w,b as k,a as W,_ as b,u as C}from"./HFvYc7_N.js";import{d as S,j as A,k as h,w as p,c as g,u as M,a as H,b as c,e as m,f as n,g as $,t as _,h as r,F as z,i as I,r as L,o as d,_ as N}from"./DedumHzx.js";const O={class:"min-h-screen py-4 bg-white dark:bg-gray-900 overflow-hidden",id:"wheelContainer"},T={class:"h-full flex flex-col items-center max-w-full"},B={class:"text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100 flex-shrink-0"},F={class:"flex flex-col items-center flex-grow w-full"},U={key:0,class:"max-w-4xl mx-auto px-4 mt-12"},V={class:"grid gap-8 md:grid-cols-2"},j={class:"text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100"},E=["innerHTML"],Y=S({__name:"[slug]",async setup(J){let s,l;const o=A().params.slug;if(!w(o))throw h({statusCode:404,statusMessage:"Wheel not found"});const{data:i}=([s,l]=p(()=>C(`/api/wheel-page/${o}`,{server:!0,default:()=>({currentWheel:null,otherWheels:[],success:!1})},"$NLvOxhyXYY")),s=await s,l(),s);if(!i.value.success||!i.value.currentWheel)try{const a=([s,l]=p(()=>k(o)),s=await s,l(),s);i.value={currentWheel:a.wheel,otherWheels:a.otherWheels,success:!0,message:"Data loaded from server"}}catch{throw h({statusCode:404,statusMessage:"Wheel not found"})}const t=g(()=>i.value.currentWheel),v=g(()=>i.value.otherWheels),e=W(o);return M({title:(e==null?void 0:e.title)||t.value.seo.title,description:(e==null?void 0:e.description)||t.value.seo.description,keywords:(e==null?void 0:e.keywords)||t.value.seo.keywords.join(", "),ogTitle:(e==null?void 0:e.title)||t.value.seo.title,ogDescription:(e==null?void 0:e.description)||t.value.seo.description,ogImage:(e==null?void 0:e.ogImage)||"https://decisionsmaker.online/og-image.png",ogUrl:(e==null?void 0:e.canonical)||`https://decisionsmaker.online/${o}`,twitterCard:"summary_large_image",twitterTitle:(e==null?void 0:e.title)||t.value.seo.title,twitterDescription:(e==null?void 0:e.description)||t.value.seo.description}),H({link:[{rel:"canonical",href:(e==null?void 0:e.canonical)||`https://decisionsmaker.online/${o}`}]}),useJsonld({"@context":"https://schema.org","@type":"WebApplication",name:t.value.name,description:t.value.seo.description,url:`https://decisionsmaker.online/${o}`,applicationCategory:"UtilityApplication",operatingSystem:"Any",offers:{"@type":"Offer",price:"0",priceCurrency:"USD"},creator:{"@type":"Organization",name:"DecisionsMaker Online"}}),(a,R)=>{const f=L("AppHeader"),x=b;return d(),c("div",null,[m(f),n("div",O,[n("div",T,[n("h1",B,_(r(t).name),1),n("div",F,[m(x,{"initial-prizes":r(t).prizes,"wheel-id":r(t).slug,title:r(t).name,"other-wheels":r(v)},null,8,["initial-prizes","wheel-id","title","other-wheels"])])]),r(t).articles&&r(t).articles.length>0?(d(),c("div",U,[n("div",V,[(d(!0),c(z,null,I(r(t).articles,(u,y)=>(d(),c("article",{key:y,class:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700"},[n("h2",j,_(u.title),1),n("div",{class:"prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300",innerHTML:u.content},null,8,E)]))),128))])])):$("",!0)])])}}}),G=N(Y,[["__scopeId","data-v-236cd69b"]]);export{G as default};
