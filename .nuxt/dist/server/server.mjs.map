{"version": 3, "file": "server.mjs", "sources": ["../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Ffetch.mjs", "../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Fglobal-polyfills.mjs", "../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Fnuxt.config.mjs", "../../../node_modules/nuxt/dist/app/nuxt.js", "../../../node_modules/nuxt/dist/app/components/injections.js", "../../../node_modules/nuxt/dist/app/composables/router.js", "../../../node_modules/nuxt/dist/app/composables/error.js", "../../../node_modules/nuxt/dist/head/runtime/plugins/unhead.js", "../../../node_modules/nuxt/dist/pages/runtime/utils.js", "../../../node_modules/nuxt/dist/app/composables/manifest.js", "../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Froutes.mjs", "../../../node_modules/nuxt/dist/app/components/utils.js", "../../../node_modules/nuxt/dist/pages/runtime/router.options.js", "../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Frouter.options.mjs", "../../../node_modules/nuxt/dist/pages/runtime/validate.js", "../../../node_modules/nuxt/dist/app/middleware/manifest-route-rule.js", "../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Fmiddleware.mjs", "../../../node_modules/nuxt/dist/pages/runtime/plugins/router.js", "../../../node_modules/nuxt/dist/head/runtime/composables/v3.js", "../../../node_modules/nuxt/dist/app/composables/payload.js", "../../../node_modules/nuxt/dist/app/plugins/revive-payload.server.js", "../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Fcomponents.plugin.mjs", "../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Fcolor-mode-options.mjs", "../../../node_modules/nuxt/dist/app/composables/state.js", "../../../node_modules/@nuxtjs/color-mode/dist/runtime/plugin.server.js", "../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Fplugins.server.mjs", "../../../virtual:nuxt:%2FUsers%2Fecho%2FWebs%2Fmakechoice%2F.nuxt%2Flayouts.mjs", "../../../node_modules/nuxt/dist/app/components/nuxt-layout.js", "../../../node_modules/nuxt/dist/app/components/route-provider.js", "../../../node_modules/nuxt/dist/pages/runtime/page.js", "../../../app.vue", "../../../node_modules/nuxt/dist/app/components/nuxt-link.js", "../../../error.vue", "../../../node_modules/nuxt/dist/app/components/nuxt-root.vue", "../../../node_modules/nuxt/dist/app/entry.js"], "sourcesContent": ["import { $fetch } from 'ofetch'\nimport { baseURL } from '#internal/nuxt/paths'\nif (!globalThis.$fetch) {\n  globalThis.$fetch = $fetch.create({\n    baseURL: baseURL()\n  })\n}", "\nif (!(\"global\" in globalThis)) {\n  globalThis.global = globalThis;\n}", "export const appHead = {\"meta\":[{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"},{\"charset\":\"utf-8\"},{\"name\":\"description\",\"content\":\"Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly. Perfect for personal and business decisions.\"},{\"name\":\"keywords\",\"content\":\"decision making, decision wheel, random choice generator, decisionsmaker online, online decision tool, decision making tool\"},{\"name\":\"author\",\"content\":\"DecisionsMaker Online\"},{\"name\":\"robots\",\"content\":\"index, follow\"},{\"property\":\"og:title\",\"content\":\"DecisionsMaker Online - Smart Decision Making Wheel\"},{\"property\":\"og:description\",\"content\":\"Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly.\"},{\"property\":\"og:url\",\"content\":\"https://decisionsmaker.online\"},{\"property\":\"og:type\",\"content\":\"website\"},{\"property\":\"og:site_name\",\"content\":\"DecisionsMaker Online\"},{\"property\":\"og:image\",\"content\":\"https://decisionsmaker.online/og-image.png\"},{\"property\":\"og:image:width\",\"content\":\"1200\"},{\"property\":\"og:image:height\",\"content\":\"630\"},{\"property\":\"og:image:alt\",\"content\":\"DecisionsMaker Online Logo\"},{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"},{\"name\":\"twitter:title\",\"content\":\"DecisionsMaker Online - Smart Decision Making Wheel\"},{\"name\":\"twitter:description\",\"content\":\"Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly.\"},{\"name\":\"twitter:site\",\"content\":\"@decisionsmaker\"},{\"name\":\"twitter:creator\",\"content\":\"@decisionsmaker\"}],\"link\":[{\"rel\":\"icon\",\"type\":\"image/x-icon\",\"href\":\"/favicon.ico\"},{\"rel\":\"canonical\",\"href\":\"https://decisionsmaker.online\"}],\"style\":[],\"script\":[{\"src\":\"https://www.googletagmanager.com/gtag/js?id=G-36JS659442\",\"async\":true},{\"innerHTML\":\"\\n            window.dataLayer = window.dataLayer || [];\\n            function gtag(){dataLayer.push(arguments);}\\n            gtag('js', new Date());\\n            gtag('config', 'G-36JS659442');\\n          \",\"type\":\"text/javascript\"},{\"innerHTML\":\"\\n            var _hmt = _hmt || [];\\n            (function() {\\n              var hm = document.createElement(\\\"script\\\");\\n              hm.src = \\\"https://hm.baidu.com/hm.js?b209671ea8fa33b06ef52e4025885a97\\\";\\n              var s = document.getElementsByTagName(\\\"script\\\")[0]; \\n              s.parentNode.insertBefore(hm, s);\\n            })();\\n          \",\"type\":\"text/javascript\"}],\"noscript\":[],\"charset\":\"utf-8\",\"viewport\":\"width=device-width, initial-scale=1\",\"title\":\"DecisionsMaker Online - Smart Decision Making Wheel\"}\n\nexport const appBaseURL = \"/\"\n\nexport const appBuildAssetsDir = \"/_nuxt/\"\n\nexport const appCdnURL = \"\"\n\nexport const appLayoutTransition = false\n\nexport const appPageTransition = false\n\nexport const appViewTransition = false\n\nexport const appKeepalive = false\n\nexport const appRootId = \"__nuxt\"\n\nexport const appRootTag = \"div\"\n\nexport const appRootAttrs = {\"id\":\"__nuxt\"}\n\nexport const appTeleportTag = \"div\"\n\nexport const appTeleportId = \"teleports\"\n\nexport const appTeleportAttrs = {\"id\":\"teleports\"}\n\nexport const appSpaLoaderTag = \"div\"\n\nexport const appSpaLoaderAttrs = {\"id\":\"__nuxt-loader\"}\n\nexport const renderJsonPayloads = true\n\nexport const componentIslands = false\n\nexport const payloadExtraction = false\n\nexport const cookieStore = true\n\nexport const appManifest = true\n\nexport const remoteComponentIslands = false\n\nexport const selectiveClient = false\n\nexport const devPagesDir = null\n\nexport const devRootDir = null\n\nexport const devLogs = false\n\nexport const nuxtLinkDefaults = {\"componentName\":\"NuxtLink\",\"prefetch\":true,\"prefetchOn\":{\"visibility\":true}}\n\nexport const asyncDataDefaults = {\"value\":null,\"errorValue\":null,\"deep\":true}\n\nexport const resetAsyncDataToUndefined = true\n\nexport const nuxtDefaultErrorValue = null\n\nexport const fetchDefaults = {}\n\nexport const vueAppRootContainer = '#__nuxt'\n\nexport const viewTransition = false\n\nexport const appId = \"nuxt-app\"\n\nexport const outdatedBuildInterval = 3600000\n\nexport const multiApp = false\n\nexport const chunkErrorEvent = \"vite:preloadError\"\n\nexport const crawlLinks = false\n\nexport const spaLoadingTemplateOutside = false\n\nexport const purgeCachedData = true\n\nexport const granularCachedData = false\n\nexport const pendingWhenIdle = true\n\nexport const alwaysRunFetchOnKeyChange = true", "import { effectScope, getCurrentInstance, getCurrentScope, hasInjectionContext, reactive, shallowReactive } from \"vue\";\nimport { createHooks } from \"hookable\";\nimport { getContext } from \"unctx\";\nimport { appId, chunkErrorEvent, multiApp } from \"#build/nuxt.config.mjs\";\nexport function getNuxtAppCtx(id = appId || \"nuxt-app\") {\n  return getContext(id, {\n    asyncContext: !!__NUXT_ASYNC_CONTEXT__ && import.meta.server\n  });\n}\nexport const NuxtPluginIndicator = \"__nuxt_plugin\";\nexport function createNuxtApp(options) {\n  let hydratingCount = 0;\n  const nuxtApp = {\n    _id: options.id || appId || \"nuxt-app\",\n    _scope: effectScope(),\n    provide: void 0,\n    globalName: \"nuxt\",\n    versions: {\n      get nuxt() {\n        return __NUXT_VERSION__;\n      },\n      get vue() {\n        return nuxtApp.vueApp.version;\n      }\n    },\n    payload: shallowReactive({\n      ...options.ssrContext?.payload || {},\n      data: shallowReactive({}),\n      state: reactive({}),\n      once: /* @__PURE__ */ new Set(),\n      _errors: shallowReactive({})\n    }),\n    static: {\n      data: {}\n    },\n    runWithContext(fn) {\n      if (nuxtApp._scope.active && !getCurrentScope()) {\n        return nuxtApp._scope.run(() => callWithNuxt(nuxtApp, fn));\n      }\n      return callWithNuxt(nuxtApp, fn);\n    },\n    isHydrating: import.meta.client,\n    deferHydration() {\n      if (!nuxtApp.isHydrating) {\n        return () => {\n        };\n      }\n      hydratingCount++;\n      let called = false;\n      return () => {\n        if (called) {\n          return;\n        }\n        called = true;\n        hydratingCount--;\n        if (hydratingCount === 0) {\n          nuxtApp.isHydrating = false;\n          return nuxtApp.callHook(\"app:suspense:resolve\");\n        }\n      };\n    },\n    _asyncDataPromises: {},\n    _asyncData: shallowReactive({}),\n    _payloadRevivers: {},\n    ...options\n  };\n  if (import.meta.server) {\n    nuxtApp.payload.serverRendered = true;\n  }\n  if (import.meta.server && nuxtApp.ssrContext) {\n    nuxtApp.payload.path = nuxtApp.ssrContext.url;\n    nuxtApp.ssrContext.nuxt = nuxtApp;\n    nuxtApp.ssrContext.payload = nuxtApp.payload;\n    nuxtApp.ssrContext.config = {\n      public: nuxtApp.ssrContext.runtimeConfig.public,\n      app: nuxtApp.ssrContext.runtimeConfig.app\n    };\n  }\n  if (import.meta.client) {\n    const __NUXT__ = multiApp ? window.__NUXT__?.[nuxtApp._id] : window.__NUXT__;\n    if (__NUXT__) {\n      for (const key in __NUXT__) {\n        switch (key) {\n          case \"data\":\n          case \"state\":\n          case \"_errors\":\n            Object.assign(nuxtApp.payload[key], __NUXT__[key]);\n            break;\n          default:\n            nuxtApp.payload[key] = __NUXT__[key];\n        }\n      }\n    }\n  }\n  nuxtApp.hooks = createHooks();\n  nuxtApp.hook = nuxtApp.hooks.hook;\n  if (import.meta.server) {\n    const contextCaller = async function(hooks, args) {\n      for (const hook of hooks) {\n        await nuxtApp.runWithContext(() => hook(...args));\n      }\n    };\n    nuxtApp.hooks.callHook = (name, ...args) => nuxtApp.hooks.callHookWith(contextCaller, name, ...args);\n  }\n  nuxtApp.callHook = nuxtApp.hooks.callHook;\n  nuxtApp.provide = (name, value) => {\n    const $name = \"$\" + name;\n    defineGetter(nuxtApp, $name, value);\n    defineGetter(nuxtApp.vueApp.config.globalProperties, $name, value);\n  };\n  defineGetter(nuxtApp.vueApp, \"$nuxt\", nuxtApp);\n  defineGetter(nuxtApp.vueApp.config.globalProperties, \"$nuxt\", nuxtApp);\n  if (import.meta.client) {\n    if (chunkErrorEvent) {\n      window.addEventListener(chunkErrorEvent, (event) => {\n        nuxtApp.callHook(\"app:chunkError\", { error: event.payload });\n        if (event.payload.message.includes(\"Unable to preload CSS\")) {\n          event.preventDefault();\n        }\n      });\n    }\n    window.useNuxtApp ||= useNuxtApp;\n    const unreg = nuxtApp.hook(\"app:error\", (...args) => {\n      console.error(\"[nuxt] error caught during app initialization\", ...args);\n    });\n    nuxtApp.hook(\"app:mounted\", unreg);\n  }\n  const runtimeConfig = import.meta.server ? options.ssrContext.runtimeConfig : nuxtApp.payload.config;\n  nuxtApp.provide(\"config\", import.meta.client && import.meta.dev ? wrappedConfig(runtimeConfig) : runtimeConfig);\n  return nuxtApp;\n}\nexport function registerPluginHooks(nuxtApp, plugin) {\n  if (plugin.hooks) {\n    nuxtApp.hooks.addHooks(plugin.hooks);\n  }\n}\nexport async function applyPlugin(nuxtApp, plugin) {\n  if (typeof plugin === \"function\") {\n    const { provide } = await nuxtApp.runWithContext(() => plugin(nuxtApp)) || {};\n    if (provide && typeof provide === \"object\") {\n      for (const key in provide) {\n        nuxtApp.provide(key, provide[key]);\n      }\n    }\n  }\n}\nexport async function applyPlugins(nuxtApp, plugins) {\n  const resolvedPlugins = /* @__PURE__ */ new Set();\n  const unresolvedPlugins = [];\n  const parallels = [];\n  const errors = [];\n  let promiseDepth = 0;\n  async function executePlugin(plugin) {\n    const unresolvedPluginsForThisPlugin = plugin.dependsOn?.filter((name) => plugins.some((p) => p._name === name) && !resolvedPlugins.has(name)) ?? [];\n    if (unresolvedPluginsForThisPlugin.length > 0) {\n      unresolvedPlugins.push([new Set(unresolvedPluginsForThisPlugin), plugin]);\n    } else {\n      const promise = applyPlugin(nuxtApp, plugin).then(async () => {\n        if (plugin._name) {\n          resolvedPlugins.add(plugin._name);\n          await Promise.all(unresolvedPlugins.map(async ([dependsOn, unexecutedPlugin]) => {\n            if (dependsOn.has(plugin._name)) {\n              dependsOn.delete(plugin._name);\n              if (dependsOn.size === 0) {\n                promiseDepth++;\n                await executePlugin(unexecutedPlugin);\n              }\n            }\n          }));\n        }\n      });\n      if (plugin.parallel) {\n        parallels.push(promise.catch((e) => errors.push(e)));\n      } else {\n        await promise;\n      }\n    }\n  }\n  for (const plugin of plugins) {\n    if (import.meta.server && nuxtApp.ssrContext?.islandContext && plugin.env?.islands === false) {\n      continue;\n    }\n    registerPluginHooks(nuxtApp, plugin);\n  }\n  for (const plugin of plugins) {\n    if (import.meta.server && nuxtApp.ssrContext?.islandContext && plugin.env?.islands === false) {\n      continue;\n    }\n    await executePlugin(plugin);\n  }\n  await Promise.all(parallels);\n  if (promiseDepth) {\n    for (let i = 0; i < promiseDepth; i++) {\n      await Promise.all(parallels);\n    }\n  }\n  if (errors.length) {\n    throw errors[0];\n  }\n}\n// @__NO_SIDE_EFFECTS__\nexport function defineNuxtPlugin(plugin) {\n  if (typeof plugin === \"function\") {\n    return plugin;\n  }\n  const _name = plugin._name || plugin.name;\n  delete plugin.name;\n  return Object.assign(plugin.setup || (() => {\n  }), plugin, { [NuxtPluginIndicator]: true, _name });\n}\nexport const definePayloadPlugin = defineNuxtPlugin;\nexport function isNuxtPlugin(plugin) {\n  return typeof plugin === \"function\" && NuxtPluginIndicator in plugin;\n}\nexport function callWithNuxt(nuxt, setup, args) {\n  const fn = () => args ? setup(...args) : setup();\n  const nuxtAppCtx = getNuxtAppCtx(nuxt._id);\n  if (import.meta.server) {\n    return nuxt.vueApp.runWithContext(() => nuxtAppCtx.callAsync(nuxt, fn));\n  } else {\n    nuxtAppCtx.set(nuxt);\n    return nuxt.vueApp.runWithContext(fn);\n  }\n}\nexport function tryUseNuxtApp(id) {\n  let nuxtAppInstance;\n  if (hasInjectionContext()) {\n    nuxtAppInstance = getCurrentInstance()?.appContext.app.$nuxt;\n  }\n  nuxtAppInstance ||= getNuxtAppCtx(id).tryUse();\n  return nuxtAppInstance || null;\n}\nexport function useNuxtApp(id) {\n  const nuxtAppInstance = tryUseNuxtApp(id);\n  if (!nuxtAppInstance) {\n    if (import.meta.dev) {\n      throw new Error(\"[nuxt] A composable that requires access to the Nuxt instance was called outside of a plugin, Nuxt hook, Nuxt middleware, or Vue setup function. This is probably not a Nuxt bug. Find out more at `https://nuxt.com/docs/guide/concepts/auto-imports#vue-and-nuxt-composables`.\");\n    } else {\n      throw new Error(\"[nuxt] instance unavailable\");\n    }\n  }\n  return nuxtAppInstance;\n}\n// @__NO_SIDE_EFFECTS__\nexport function useRuntimeConfig(_event) {\n  return useNuxtApp().$config;\n}\nfunction defineGetter(obj, key, val) {\n  Object.defineProperty(obj, key, { get: () => val });\n}\nexport function defineAppConfig(config) {\n  return config;\n}\nconst loggedKeys = /* @__PURE__ */ new Set();\nfunction wrappedConfig(runtimeConfig) {\n  if (!import.meta.dev || import.meta.server) {\n    return runtimeConfig;\n  }\n  const keys = Object.keys(runtimeConfig).map((key) => `\\`${key}\\``);\n  const lastKey = keys.pop();\n  return new Proxy(runtimeConfig, {\n    get(target, p, receiver) {\n      if (typeof p === \"string\" && p !== \"public\" && !(p in target) && !p.startsWith(\"__v\")) {\n        if (!loggedKeys.has(p)) {\n          loggedKeys.add(p);\n          console.warn(`[nuxt] Could not access \\`${p}\\`. The only available runtime config keys on the client side are ${keys.join(\", \")} and ${lastKey}. See https://nuxt.com/docs/guide/going-further/runtime-config for more information.`);\n        }\n      }\n      return Reflect.get(target, p, receiver);\n    }\n  });\n}\n", "export const LayoutMetaSymbol = Symbol(\"layout-meta\");\nexport const PageRouteSymbol = Symbol(\"route\");\n", "import { getCurrentInstance, hasInjectionContext, inject, onScopeDispose } from \"vue\";\nimport { sanitizeStatusCode } from \"h3\";\nimport { hasProtocol, isScriptProtocol, joinURL, parseQuery, parseURL, withQuery } from \"ufo\";\nimport { useNuxtApp, useRuntimeConfig } from \"../nuxt.js\";\nimport { PageRouteSymbol } from \"../components/injections.js\";\nimport { createError, showError } from \"./error.js\";\nexport const useRouter = () => {\n  return useNuxtApp()?.$router;\n};\nexport const useRoute = () => {\n  if (import.meta.dev && !getCurrentInstance() && isProcessingMiddleware()) {\n    console.warn(\"[nuxt] Calling `useRoute` within middleware may lead to misleading results. Instead, use the (to, from) arguments passed to the middleware to access the new and old routes.\");\n  }\n  if (hasInjectionContext()) {\n    return inject(PageRouteSymbol, useNuxtApp()._route);\n  }\n  return useNuxtApp()._route;\n};\nexport const onBeforeRouteLeave = (guard) => {\n  const unsubscribe = useRouter().beforeEach((to, from, next) => {\n    if (to === from) {\n      return;\n    }\n    return guard(to, from, next);\n  });\n  onScopeDispose(unsubscribe);\n};\nexport const onBeforeRouteUpdate = (guard) => {\n  const unsubscribe = useRouter().beforeEach(guard);\n  onScopeDispose(unsubscribe);\n};\n// @__NO_SIDE_EFFECTS__\nexport function defineNuxtRouteMiddleware(middleware) {\n  return middleware;\n}\nexport const addRouteMiddleware = (name, middleware, options = {}) => {\n  const nuxtApp = useNuxtApp();\n  const global = options.global || typeof name !== \"string\";\n  const mw = typeof name !== \"string\" ? name : middleware;\n  if (!mw) {\n    console.warn(\"[nuxt] No route middleware passed to `addRouteMiddleware`.\", name);\n    return;\n  }\n  if (global) {\n    nuxtApp._middleware.global.push(mw);\n  } else {\n    nuxtApp._middleware.named[name] = mw;\n  }\n};\nconst isProcessingMiddleware = () => {\n  try {\n    if (useNuxtApp()._processingMiddleware) {\n      return true;\n    }\n  } catch {\n    return false;\n  }\n  return false;\n};\nconst URL_QUOTE_RE = /\"/g;\nexport const navigateTo = (to, options) => {\n  to ||= \"/\";\n  const toPath = typeof to === \"string\" ? to : \"path\" in to ? resolveRouteObject(to) : useRouter().resolve(to).href;\n  if (import.meta.client && options?.open) {\n    const { target = \"_blank\", windowFeatures = {} } = options.open;\n    const features = Object.entries(windowFeatures).filter(([_, value]) => value !== void 0).map(([feature, value]) => `${feature.toLowerCase()}=${value}`).join(\", \");\n    open(toPath, target, features);\n    return Promise.resolve();\n  }\n  const isExternalHost = hasProtocol(toPath, { acceptRelative: true });\n  const isExternal = options?.external || isExternalHost;\n  if (isExternal) {\n    if (!options?.external) {\n      throw new Error(\"Navigating to an external URL is not allowed by default. Use `navigateTo(url, { external: true })`.\");\n    }\n    const { protocol } = new URL(toPath, import.meta.client ? window.location.href : \"http://localhost\");\n    if (protocol && isScriptProtocol(protocol)) {\n      throw new Error(`Cannot navigate to a URL with '${protocol}' protocol.`);\n    }\n  }\n  const inMiddleware = isProcessingMiddleware();\n  if (import.meta.client && !isExternal && inMiddleware) {\n    if (options?.replace) {\n      if (typeof to === \"string\") {\n        const { pathname, search, hash } = parseURL(to);\n        return {\n          path: pathname,\n          ...search && { query: parseQuery(search) },\n          ...hash && { hash },\n          replace: true\n        };\n      }\n      return { ...to, replace: true };\n    }\n    return to;\n  }\n  const router = useRouter();\n  const nuxtApp = useNuxtApp();\n  if (import.meta.server) {\n    if (nuxtApp.ssrContext) {\n      const fullPath = typeof to === \"string\" || isExternal ? toPath : router.resolve(to).fullPath || \"/\";\n      const location2 = isExternal ? toPath : joinURL(useRuntimeConfig().app.baseURL, fullPath);\n      const redirect = async function(response) {\n        await nuxtApp.callHook(\"app:redirected\");\n        const encodedLoc = location2.replace(URL_QUOTE_RE, \"%22\");\n        const encodedHeader = encodeURL(location2, isExternalHost);\n        nuxtApp.ssrContext._renderResponse = {\n          statusCode: sanitizeStatusCode(options?.redirectCode || 302, 302),\n          body: `<!DOCTYPE html><html><head><meta http-equiv=\"refresh\" content=\"0; url=${encodedLoc}\"></head></html>`,\n          headers: { location: encodedHeader }\n        };\n        return response;\n      };\n      if (!isExternal && inMiddleware) {\n        router.afterEach((final) => final.fullPath === fullPath ? redirect(false) : void 0);\n        return to;\n      }\n      return redirect(!inMiddleware ? void 0 : (\n        /* abort route navigation */\n        false\n      ));\n    }\n  }\n  if (isExternal) {\n    nuxtApp._scope.stop();\n    if (options?.replace) {\n      location.replace(toPath);\n    } else {\n      location.href = toPath;\n    }\n    if (inMiddleware) {\n      if (!nuxtApp.isHydrating) {\n        return false;\n      }\n      return new Promise(() => {\n      });\n    }\n    return Promise.resolve();\n  }\n  return options?.replace ? router.replace(to) : router.push(to);\n};\nexport const abortNavigation = (err) => {\n  if (import.meta.dev && !isProcessingMiddleware()) {\n    throw new Error(\"abortNavigation() is only usable inside a route middleware handler.\");\n  }\n  if (!err) {\n    return false;\n  }\n  err = createError(err);\n  if (err.fatal) {\n    useNuxtApp().runWithContext(() => showError(err));\n  }\n  throw err;\n};\nexport const setPageLayout = (layout) => {\n  const nuxtApp = useNuxtApp();\n  if (import.meta.server) {\n    if (import.meta.dev && getCurrentInstance() && nuxtApp.payload.state._layout !== layout) {\n      console.warn(\"[warn] [nuxt] `setPageLayout` should not be called to change the layout on the server within a component as this will cause hydration errors.\");\n    }\n    nuxtApp.payload.state._layout = layout;\n  }\n  if (import.meta.dev && nuxtApp.isHydrating && nuxtApp.payload.serverRendered && nuxtApp.payload.state._layout !== layout) {\n    console.warn(\"[warn] [nuxt] `setPageLayout` should not be called to change the layout during hydration as this will cause hydration errors.\");\n  }\n  const inMiddleware = isProcessingMiddleware();\n  if (inMiddleware || import.meta.server || nuxtApp.isHydrating) {\n    const unsubscribe = useRouter().beforeResolve((to) => {\n      to.meta.layout = layout;\n      unsubscribe();\n    });\n  }\n  if (!inMiddleware) {\n    useRoute().meta.layout = layout;\n  }\n};\nexport function resolveRouteObject(to) {\n  return withQuery(to.path || \"\", to.query || {}) + (to.hash || \"\");\n}\nexport function encodeURL(location2, isExternalHost = false) {\n  const url = new URL(location2, \"http://localhost\");\n  if (!isExternalHost) {\n    return url.pathname + url.search + url.hash;\n  }\n  if (location2.startsWith(\"//\")) {\n    return url.toString().replace(url.protocol, \"\");\n  }\n  return url.toString();\n}\n", "import { createError as createH3Error } from \"h3\";\nimport { toRef } from \"vue\";\nimport { useNuxtApp } from \"../nuxt.js\";\nimport { useRouter } from \"./router.js\";\nimport { nuxtDefaultErrorValue } from \"#build/nuxt.config.mjs\";\nexport const NUXT_ERROR_SIGNATURE = \"__nuxt_error\";\nexport const useError = () => toRef(useNuxtApp().payload, \"error\");\nexport const showError = (error) => {\n  const nuxtError = createError(error);\n  try {\n    const nuxtApp = useNuxtApp();\n    const error2 = useError();\n    if (import.meta.client) {\n      nuxtApp.hooks.callHook(\"app:error\", nuxtError);\n    }\n    error2.value ||= nuxtError;\n  } catch {\n    throw nuxtError;\n  }\n  return nuxtError;\n};\nexport const clearError = async (options = {}) => {\n  const nuxtApp = useNuxtApp();\n  const error = useError();\n  nuxtApp.callHook(\"app:error:cleared\", options);\n  if (options.redirect) {\n    await useRouter().replace(options.redirect);\n  }\n  error.value = nuxtDefaultErrorValue;\n};\nexport const isNuxtError = (error) => !!error && typeof error === \"object\" && NUXT_ERROR_SIGNATURE in error;\nexport const createError = (error) => {\n  const nuxtError = createH3Error(error);\n  Object.defineProperty(nuxtError, NUXT_ERROR_SIGNATURE, {\n    value: true,\n    configurable: false,\n    writable: false\n  });\n  return nuxtError;\n};\n", "import { createHead as createClientHead, renderDOMHead } from \"@unhead/vue/client\";\nimport { defineNuxtPlugin } from \"#app/nuxt\";\nimport unheadOptions from \"#build/unhead-options.mjs\";\nexport default defineNuxtPlugin({\n  name: \"nuxt:head\",\n  enforce: \"pre\",\n  setup(nuxtApp) {\n    const head = import.meta.server ? nuxtApp.ssrContext.head : createClientHead(unheadOptions);\n    nuxtApp.vueApp.use(head);\n    if (import.meta.client) {\n      let pauseDOMUpdates = true;\n      const syncHead = async () => {\n        pauseDOMUpdates = false;\n        await renderDOMHead(head);\n      };\n      head.hooks.hook(\"dom:beforeRender\", (context) => {\n        context.shouldRender = !pauseDOMUpdates;\n      });\n      nuxtApp.hooks.hook(\"page:start\", () => {\n        pauseDOMUpdates = true;\n      });\n      nuxtApp.hooks.hook(\"page:finish\", () => {\n        if (!nuxtApp.isHydrating) {\n          syncHead();\n        }\n      });\n      nuxtApp.hooks.hook(\"app:error\", syncHead);\n      nuxtApp.hooks.hook(\"app:suspense:resolve\", syncHead);\n    }\n  }\n});\n", "import { KeepAlive, h } from \"vue\";\nconst ROUTE_KEY_PARENTHESES_RE = /(:\\w+)\\([^)]+\\)/g;\nconst ROUTE_KEY_SYMBOLS_RE = /(:\\w+)[?+*]/g;\nconst ROUTE_KEY_NORMAL_RE = /:\\w+/g;\nconst interpolatePath = (route, match) => {\n  return match.path.replace(ROUTE_KEY_PARENTHESES_RE, \"$1\").replace(ROUTE_KEY_SYMBOLS_RE, \"$1\").replace(ROUTE_KEY_NORMAL_RE, (r) => route.params[r.slice(1)]?.toString() || \"\");\n};\nexport const generateRouteKey = (routeProps, override) => {\n  const matchedRoute = routeProps.route.matched.find((m) => m.components?.default === routeProps.Component.type);\n  const source = override ?? matchedRoute?.meta.key ?? (matchedRoute && interpolatePath(routeProps.route, matchedRoute));\n  return typeof source === \"function\" ? source(routeProps.route) : source;\n};\nexport const wrapInKeepAlive = (props, children) => {\n  return { default: () => import.meta.client && props ? h(KeepAlive, props === true ? {} : props, children) : children };\n};\nexport function toArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n", "import { createMatcherFromExport, createRouter as createRadix<PERSON>outer, toRouteMatcher } from \"radix3\";\nimport { defu } from \"defu\";\nimport { useNuxtApp, useRuntimeConfig } from \"../nuxt.js\";\nimport { appManifest as isAppManifestEnabled } from \"#build/nuxt.config.mjs\";\nimport { buildAssetsURL } from \"#internal/nuxt/paths\";\nlet manifest;\nlet matcher;\nfunction fetchManifest() {\n  if (!isAppManifestEnabled) {\n    throw new Error(\"[nuxt] app manifest should be enabled with `experimental.appManifest`\");\n  }\n  if (import.meta.server) {\n    manifest = import(\"#app-manifest\");\n  } else {\n    manifest = $fetch(buildAssetsURL(`builds/meta/${useRuntimeConfig().app.buildId}.json`), {\n      responseType: \"json\"\n    });\n  }\n  manifest.then((m) => {\n    matcher = createMatcherFromExport(m.matcher);\n  }).catch((e) => {\n    console.error(\"[nuxt] Error fetching app manifest.\", e);\n  });\n  return manifest;\n}\nexport function getAppManifest() {\n  if (!isAppManifestEnabled) {\n    throw new Error(\"[nuxt] app manifest should be enabled with `experimental.appManifest`\");\n  }\n  if (import.meta.server) {\n    useNuxtApp().ssrContext._preloadManifest = true;\n  }\n  return manifest || fetchManifest();\n}\nexport async function getRouteRules(arg) {\n  const path = typeof arg === \"string\" ? arg : arg.path;\n  if (import.meta.server) {\n    useNuxtApp().ssrContext._preloadManifest = true;\n    const _routeRulesMatcher = toRouteMatcher(\n      createRadixRouter({ routes: useRuntimeConfig().nitro.routeRules })\n    );\n    return defu({}, ..._routeRulesMatcher.matchAll(path).reverse());\n  }\n  await getAppManifest();\n  if (!matcher) {\n    console.error(\"[nuxt] Error creating app manifest matcher.\", matcher);\n    return {};\n  }\n  try {\n    return defu({}, ...matcher.matchAll(path).reverse());\n  } catch (e) {\n    console.error(\"[nuxt] Error matching route rules.\", e);\n    return {};\n  }\n}\n", "\nif (import.meta.hot) {\n  import.meta.hot.accept((mod) => {\n    const router = import.meta.hot.data.router\n    const generateRoutes = import.meta.hot.data.generateRoutes\n    if (!router || !generateRoutes) {\n      import.meta.hot.invalidate('[nuxt] Cannot replace routes because there is no active router. Reloading.')\n      return\n    }\n    router.clearRoutes()\n    const routes = generateRoutes(mod.default || mod)\n    function addRoutes (routes) {\n      for (const route of routes) {\n        router.addRoute(route)\n      }\n      router.replace(router.currentRoute.value.fullPath)\n    }\n    if (routes && 'then' in routes) {\n      routes.then(addRoutes)\n    } else {\n      addRoutes(routes)\n    }\n  })\n}\n\nexport function handleHotUpdate(_router, _generateRoutes) {\n  if (import.meta.hot) {\n    import.meta.hot.data ||= {}\n    import.meta.hot.data.router = _router\n    import.meta.hot.data.generateRoutes = _generateRoutes\n  }\n}\nimport { default as index7CJl_pF_GfV6EXXXp4UTmBgqi9NLiBieHHomBD6BDfsMeta } from \"/Users/<USER>/Webs/makechoice/pages/index.vue?macro=true\";\nimport { default as _91slug_935cKhrCBRPBVApek5crAj0apII21kYok7gFS8InRNIp0Meta } from \"/Users/<USER>/Webs/makechoice/pages/[slug].vue?macro=true\";\nexport default [\n  {\n    name: \"index\",\n    path: \"/\",\n    meta: index7CJl_pF_GfV6EXXXp4UTmBgqi9NLiBieHHomBD6BDfsMeta || {},\n    component: () => import(\"/Users/<USER>/Webs/makechoice/pages/index.vue\")\n  },\n  {\n    name: \"slug\",\n    path: \"/:slug()\",\n    meta: _91slug_935cKhrCBRPBVApek5crAj0apII21kYok7gFS8InRNIp0Meta || {},\n    component: () => import(\"/Users/<USER>/Webs/makechoice/pages/[slug].vue\")\n  }\n]", "import { Transition, createStaticVNode, h } from \"vue\";\nimport { isString, isPromise, isArray, isObject } from \"@vue/shared\";\nimport { START_LOCATION } from \"#build/pages\";\nexport const _wrapInTransition = (props, children) => {\n  return { default: () => import.meta.client && props ? h(Transition, props === true ? {} : props, children) : children.default?.() };\n};\nconst ROUTE_KEY_PARENTHESES_RE = /(:\\w+)\\([^)]+\\)/g;\nconst ROUTE_KEY_SYMBOLS_RE = /(:\\w+)[?+*]/g;\nconst ROUTE_KEY_NORMAL_RE = /:\\w+/g;\nfunction generateRouteKey(route) {\n  const source = route?.meta.key ?? route.path.replace(ROUTE_KEY_PARENTHESES_RE, \"$1\").replace(ROUTE_KEY_SYMBOLS_RE, \"$1\").replace(ROUTE_KEY_NORMAL_RE, (r) => route.params[r.slice(1)]?.toString() || \"\");\n  return typeof source === \"function\" ? source(route) : source;\n}\nexport function isChangingPage(to, from) {\n  if (to === from || from === START_LOCATION) {\n    return false;\n  }\n  if (generateRouteKey(to) !== generateRouteKey(from)) {\n    return true;\n  }\n  const areComponentsSame = to.matched.every(\n    (comp, index) => comp.components && comp.components.default === from.matched[index]?.components?.default\n  );\n  if (areComponentsSame) {\n    return false;\n  }\n  return true;\n}\nexport function createBuffer() {\n  let appendable = false;\n  const buffer = [];\n  return {\n    getBuffer() {\n      return buffer;\n    },\n    push(item) {\n      const isStringItem = isString(item);\n      if (appendable && isStringItem) {\n        buffer[buffer.length - 1] += item;\n      } else {\n        buffer.push(item);\n      }\n      appendable = isStringItem;\n      if (isPromise(item) || isArray(item) && item.hasAsync) {\n        buffer.hasAsync = true;\n      }\n    }\n  };\n}\nexport function vforToArray(source) {\n  if (isArray(source)) {\n    return source;\n  } else if (isString(source)) {\n    return source.split(\"\");\n  } else if (typeof source === \"number\") {\n    if (import.meta.dev && !Number.isInteger(source)) {\n      console.warn(`The v-for range expect an integer value but got ${source}.`);\n    }\n    const array = [];\n    for (let i = 0; i < source; i++) {\n      array[i] = i;\n    }\n    return array;\n  } else if (isObject(source)) {\n    if (source[Symbol.iterator]) {\n      return Array.from(\n        source,\n        (item) => item\n      );\n    } else {\n      const keys = Object.keys(source);\n      const array = new Array(keys.length);\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const key = keys[i];\n        array[i] = source[key];\n      }\n      return array;\n    }\n  }\n  return [];\n}\nexport function getFragmentHTML(element, withoutSlots = false) {\n  if (element) {\n    if (element.nodeName === \"#comment\" && element.nodeValue === \"[\") {\n      return getFragmentChildren(element, [], withoutSlots);\n    }\n    if (withoutSlots) {\n      const clone = element.cloneNode(true);\n      clone.querySelectorAll(\"[data-island-slot]\").forEach((n) => {\n        n.innerHTML = \"\";\n      });\n      return [clone.outerHTML];\n    }\n    return [element.outerHTML];\n  }\n}\nfunction getFragmentChildren(element, blocks = [], withoutSlots = false) {\n  if (element && element.nodeName) {\n    if (isEndFragment(element)) {\n      return blocks;\n    } else if (!isStartFragment(element)) {\n      const clone = element.cloneNode(true);\n      if (withoutSlots) {\n        clone.querySelectorAll?.(\"[data-island-slot]\").forEach((n) => {\n          n.innerHTML = \"\";\n        });\n      }\n      blocks.push(clone.outerHTML);\n    }\n    getFragmentChildren(element.nextSibling, blocks, withoutSlots);\n  }\n  return blocks;\n}\nexport function elToStaticVNode(el, staticNodeFallback) {\n  const fragment = el ? getFragmentHTML(el) : staticNodeFallback ? [staticNodeFallback] : void 0;\n  if (fragment) {\n    return createStaticVNode(fragment.join(\"\"), fragment.length);\n  }\n  return h(\"div\");\n}\nexport function isStartFragment(element) {\n  return element.nodeName === \"#comment\" && element.nodeValue === \"[\";\n}\nexport function isEndFragment(element) {\n  return element.nodeName === \"#comment\" && element.nodeValue === \"]\";\n}\n", "import { START_LOCATION } from \"vue-router\";\nimport { useNuxtApp } from \"#app/nuxt\";\nimport { isChangingPage } from \"#app/components/utils\";\nimport { useRouter } from \"#app/composables/router\";\nexport default {\n  scrollBehavior(to, from, savedPosition) {\n    const nuxtApp = useNuxtApp();\n    const behavior = useRouter().options?.scrollBehaviorType ?? \"auto\";\n    if (to.path === from.path) {\n      if (from.hash && !to.hash) {\n        return { left: 0, top: 0 };\n      }\n      if (to.hash) {\n        return { el: to.hash, top: _getHashElementScrollMarginTop(to.hash), behavior };\n      }\n      return false;\n    }\n    const routeAllowsScrollToTop = typeof to.meta.scrollToTop === \"function\" ? to.meta.scrollToTop(to, from) : to.meta.scrollToTop;\n    if (routeAllowsScrollToTop === false) {\n      return false;\n    }\n    let position = savedPosition || void 0;\n    if (!position && isChangingPage(to, from)) {\n      position = { left: 0, top: 0 };\n    }\n    const hookToWait = nuxtApp._runningTransition ? \"page:transition:finish\" : \"page:loading:end\";\n    return new Promise((resolve) => {\n      if (from === START_LOCATION) {\n        resolve(_calculatePosition(to, \"instant\", position));\n        return;\n      }\n      nuxtApp.hooks.hookOnce(hookToWait, () => {\n        requestAnimationFrame(() => resolve(_calculatePosition(to, \"instant\", position)));\n      });\n    });\n  }\n};\nfunction _getHashElementScrollMarginTop(selector) {\n  try {\n    const elem = document.querySelector(selector);\n    if (elem) {\n      return (Number.parseFloat(getComputedStyle(elem).scrollMarginTop) || 0) + (Number.parseFloat(getComputedStyle(document.documentElement).scrollPaddingTop) || 0);\n    }\n  } catch {\n  }\n  return 0;\n}\nfunction _calculatePosition(to, scrollBehaviorType, position) {\n  if (position) {\n    return position;\n  }\n  if (to.hash) {\n    return {\n      el: to.hash,\n      top: _getHashElementScrollMarginTop(to.hash),\n      behavior: scrollBehaviorType\n    };\n  }\n  return { left: 0, top: 0, behavior: scrollBehaviorType };\n}\n", "import routerOptions0 from \"/Users/<USER>/Webs/makechoice/node_modules/nuxt/dist/pages/runtime/router.options.js\";\nconst configRouterOptions = {\n  strict: false,\n  hashMode: false,\n  scrollBehaviorType: \"auto\"\n}\nexport const hashMode = false\nexport default {\n...configRouterOptions,\n...routerOptions0,\n}", "import { createError } from \"#app/composables/error\";\nimport { defineNuxtRouteMiddleware } from \"#app/composables/router\";\nexport default defineNuxtRouteMiddleware(async (to, from) => {\n  if (!to.meta?.validate) {\n    return;\n  }\n  const result = await Promise.resolve(to.meta.validate(to));\n  if (result === true) {\n    return;\n  }\n  const error = createError({\n    fatal: import.meta.client,\n    statusCode: result && result.statusCode || 404,\n    statusMessage: result && result.statusMessage || `Page Not Found: ${to.fullPath}`,\n    data: {\n      path: to.fullPath\n    }\n  });\n  if (typeof window !== \"undefined\") {\n    window.history.pushState({}, \"\", from.fullPath);\n  }\n  return error;\n});\n", "import { hasProtocol } from \"ufo\";\nimport { defineNuxtRouteMiddleware } from \"../composables/router.js\";\nimport { getRouteRules } from \"../composables/manifest.js\";\nexport default defineNuxtRouteMiddleware(async (to) => {\n  if (import.meta.server || import.meta.test) {\n    return;\n  }\n  const rules = await getRouteRules({ path: to.path });\n  if (rules.redirect) {\n    if (hasProtocol(rules.redirect, { acceptRelative: true })) {\n      window.location.href = rules.redirect;\n      return false;\n    }\n    return rules.redirect;\n  }\n});\n", "import validate from \"/Users/<USER>/Webs/makechoice/node_modules/nuxt/dist/pages/runtime/validate.js\";\nimport manifest_45route_45rule from \"/Users/<USER>/Webs/makechoice/node_modules/nuxt/dist/app/middleware/manifest-route-rule.js\";\nexport const globalMiddleware = [\n  validate,\n  manifest_45route_45rule\n]\nexport const namedMiddleware = {}", "import { isReadonly, reactive, shallowReactive, shallowRef } from \"vue\";\nimport { START_LOCATION, createMemoryHistory, createRouter, createWebHashHistory, createWebHistory } from \"vue-router\";\nimport { isSamePath, withoutBase } from \"ufo\";\nimport { toArray } from \"../utils.js\";\nimport { getRouteRules } from \"#app/composables/manifest\";\nimport { defineNuxtPlugin, useRuntimeConfig } from \"#app/nuxt\";\nimport { clearError, createError, isNuxtError, showError, useError } from \"#app/composables/error\";\nimport { navigateTo } from \"#app/composables/router\";\nimport { appManifest as isAppManifestEnabled } from \"#build/nuxt.config.mjs\";\nimport _routes, { handleHotUpdate } from \"#build/routes\";\nimport routerOptions, { hashMode } from \"#build/router.options\";\nimport { globalMiddleware, namedMiddleware } from \"#build/middleware\";\nfunction createCurrentLocation(base, location, renderedPath) {\n  const { pathname, search, hash } = location;\n  const hashPos = base.indexOf(\"#\");\n  if (hashPos > -1) {\n    const slicePos = hash.includes(base.slice(hashPos)) ? base.slice(hashPos).length : 1;\n    let pathFromHash = hash.slice(slicePos);\n    if (pathFromHash[0] !== \"/\") {\n      pathFromHash = \"/\" + pathFromHash;\n    }\n    return withoutBase(pathFromHash, \"\");\n  }\n  const displayedPath = withoutBase(pathname, base);\n  const path = !renderedPath || isSamePath(displayedPath, renderedPath) ? displayedPath : renderedPath;\n  return path + (path.includes(\"?\") ? \"\" : search) + hash;\n}\nconst plugin = defineNuxtPlugin({\n  name: \"nuxt:router\",\n  enforce: \"pre\",\n  async setup(nuxtApp) {\n    let routerBase = useRuntimeConfig().app.baseURL;\n    if (hashMode && !routerBase.includes(\"#\")) {\n      routerBase += \"#\";\n    }\n    const history = routerOptions.history?.(routerBase) ?? (import.meta.client ? hashMode ? createWebHashHistory(routerBase) : createWebHistory(routerBase) : createMemoryHistory(routerBase));\n    const routes = routerOptions.routes ? await routerOptions.routes(_routes) ?? _routes : _routes;\n    let startPosition;\n    const router = createRouter({\n      ...routerOptions,\n      scrollBehavior: (to, from, savedPosition) => {\n        if (from === START_LOCATION) {\n          startPosition = savedPosition;\n          return;\n        }\n        if (routerOptions.scrollBehavior) {\n          router.options.scrollBehavior = routerOptions.scrollBehavior;\n          if (\"scrollRestoration\" in window.history) {\n            const unsub = router.beforeEach(() => {\n              unsub();\n              window.history.scrollRestoration = \"manual\";\n            });\n          }\n          return routerOptions.scrollBehavior(to, START_LOCATION, startPosition || savedPosition);\n        }\n      },\n      history,\n      routes\n    });\n    if (import.meta.hot) {\n      handleHotUpdate(router, routerOptions.routes ? routerOptions.routes : (routes2) => routes2);\n    }\n    if (import.meta.client && \"scrollRestoration\" in window.history) {\n      window.history.scrollRestoration = \"auto\";\n    }\n    nuxtApp.vueApp.use(router);\n    const previousRoute = shallowRef(router.currentRoute.value);\n    router.afterEach((_to, from) => {\n      previousRoute.value = from;\n    });\n    Object.defineProperty(nuxtApp.vueApp.config.globalProperties, \"previousRoute\", {\n      get: () => previousRoute.value\n    });\n    const initialURL = import.meta.server ? nuxtApp.ssrContext.url : createCurrentLocation(routerBase, window.location, nuxtApp.payload.path);\n    const _route = shallowRef(router.currentRoute.value);\n    const syncCurrentRoute = () => {\n      _route.value = router.currentRoute.value;\n    };\n    nuxtApp.hook(\"page:finish\", syncCurrentRoute);\n    router.afterEach((to, from) => {\n      if (to.matched[0]?.components?.default === from.matched[0]?.components?.default) {\n        syncCurrentRoute();\n      }\n    });\n    const route = {};\n    for (const key in _route.value) {\n      Object.defineProperty(route, key, {\n        get: () => _route.value[key],\n        enumerable: true\n      });\n    }\n    nuxtApp._route = shallowReactive(route);\n    nuxtApp._middleware ||= {\n      global: [],\n      named: {}\n    };\n    const error = useError();\n    if (import.meta.client || !nuxtApp.ssrContext?.islandContext) {\n      router.afterEach(async (to, _from, failure) => {\n        delete nuxtApp._processingMiddleware;\n        if (import.meta.client && !nuxtApp.isHydrating && error.value) {\n          await nuxtApp.runWithContext(clearError);\n        }\n        if (failure) {\n          await nuxtApp.callHook(\"page:loading:end\");\n        }\n        if (import.meta.server && failure?.type === 4) {\n          return;\n        }\n        if (import.meta.server && to.redirectedFrom && to.fullPath !== initialURL) {\n          await nuxtApp.runWithContext(() => navigateTo(to.fullPath || \"/\"));\n        }\n      });\n    }\n    try {\n      if (import.meta.server) {\n        await router.push(initialURL);\n      }\n      await router.isReady();\n    } catch (error2) {\n      await nuxtApp.runWithContext(() => showError(error2));\n    }\n    const resolvedInitialRoute = import.meta.client && initialURL !== router.currentRoute.value.fullPath ? router.resolve(initialURL) : router.currentRoute.value;\n    syncCurrentRoute();\n    if (import.meta.server && nuxtApp.ssrContext?.islandContext) {\n      return { provide: { router } };\n    }\n    const initialLayout = nuxtApp.payload.state._layout;\n    router.beforeEach(async (to, from) => {\n      await nuxtApp.callHook(\"page:loading:start\");\n      to.meta = reactive(to.meta);\n      if (nuxtApp.isHydrating && initialLayout && !isReadonly(to.meta.layout)) {\n        to.meta.layout = initialLayout;\n      }\n      nuxtApp._processingMiddleware = true;\n      if (import.meta.client || !nuxtApp.ssrContext?.islandContext) {\n        const middlewareEntries = /* @__PURE__ */ new Set([...globalMiddleware, ...nuxtApp._middleware.global]);\n        for (const component of to.matched) {\n          const componentMiddleware = component.meta.middleware;\n          if (!componentMiddleware) {\n            continue;\n          }\n          for (const entry of toArray(componentMiddleware)) {\n            middlewareEntries.add(entry);\n          }\n        }\n        if (isAppManifestEnabled) {\n          const routeRules = await nuxtApp.runWithContext(() => getRouteRules({ path: to.path }));\n          if (routeRules.appMiddleware) {\n            for (const key in routeRules.appMiddleware) {\n              if (routeRules.appMiddleware[key]) {\n                middlewareEntries.add(key);\n              } else {\n                middlewareEntries.delete(key);\n              }\n            }\n          }\n        }\n        for (const entry of middlewareEntries) {\n          const middleware = typeof entry === \"string\" ? nuxtApp._middleware.named[entry] || await namedMiddleware[entry]?.().then((r) => r.default || r) : entry;\n          if (!middleware) {\n            if (import.meta.dev) {\n              throw new Error(`Unknown route middleware: '${entry}'. Valid middleware: ${Object.keys(namedMiddleware).map((mw) => `'${mw}'`).join(\", \")}.`);\n            }\n            throw new Error(`Unknown route middleware: '${entry}'.`);\n          }\n          try {\n            const result = await nuxtApp.runWithContext(() => middleware(to, from));\n            if (import.meta.server || !nuxtApp.payload.serverRendered && nuxtApp.isHydrating) {\n              if (result === false || result instanceof Error) {\n                const error2 = result || createError({\n                  statusCode: 404,\n                  statusMessage: `Page Not Found: ${initialURL}`\n                });\n                await nuxtApp.runWithContext(() => showError(error2));\n                return false;\n              }\n            }\n            if (result === true) {\n              continue;\n            }\n            if (result === false) {\n              return result;\n            }\n            if (result) {\n              if (isNuxtError(result) && result.fatal) {\n                await nuxtApp.runWithContext(() => showError(result));\n              }\n              return result;\n            }\n          } catch (err) {\n            const error2 = createError(err);\n            if (error2.fatal) {\n              await nuxtApp.runWithContext(() => showError(error2));\n            }\n            return error2;\n          }\n        }\n      }\n    });\n    router.onError(async () => {\n      delete nuxtApp._processingMiddleware;\n      await nuxtApp.callHook(\"page:loading:end\");\n    });\n    router.afterEach(async (to, _from) => {\n      if (to.matched.length === 0) {\n        await nuxtApp.runWithContext(() => showError(createError({\n          statusCode: 404,\n          fatal: false,\n          statusMessage: `Page not found: ${to.fullPath}`,\n          data: {\n            path: to.fullPath\n          }\n        })));\n      }\n    });\n    nuxtApp.hooks.hookOnce(\"app:created\", async () => {\n      try {\n        if (\"name\" in resolvedInitialRoute) {\n          resolvedInitialRoute.name = void 0;\n        }\n        await router.replace({\n          ...resolvedInitialRoute,\n          force: true\n        });\n        router.options.scrollBehavior = routerOptions.scrollBehavior;\n      } catch (error2) {\n        await nuxtApp.runWithContext(() => showError(error2));\n      }\n    });\n    return { provide: { router } };\n  }\n});\nexport default plugin;\n", "import { hasInjectionContext, inject } from \"vue\";\nimport {\n  useHead as headCore,\n  useHeadSafe as headSafe,\n  headSymbol,\n  useSeoMeta as seoMeta,\n  useServerHead as serverHead,\n  useServerHeadSafe as serverHeadSafe,\n  useServerSeoMeta as serverSeoMeta\n} from \"@unhead/vue\";\nimport { tryUseNuxtApp } from \"#app/nuxt\";\nexport function injectHead(nuxtApp) {\n  const nuxt = nuxtApp || tryUseNuxtApp();\n  return nuxt?.ssrContext?.head || nuxt?.runWithContext(() => {\n    if (hasInjectionContext()) {\n      return inject(headSymbol);\n    }\n  });\n}\nexport function useHead(input, options = {}) {\n  const head = injectHead(options.nuxt);\n  if (head) {\n    return headCore(input, { head, ...options });\n  }\n}\nexport function useHeadSafe(input, options = {}) {\n  const head = injectHead(options.nuxt);\n  if (head) {\n    return headSafe(input, { head, ...options });\n  }\n}\nexport function useSeoMeta(input, options = {}) {\n  const head = injectHead(options.nuxt);\n  if (head) {\n    return seoMeta(input, { head, ...options });\n  }\n}\nexport function useServerHead(input, options = {}) {\n  const head = injectHead(options.nuxt);\n  if (head) {\n    return serverHead(input, { head, ...options });\n  }\n}\nexport function useServerHeadSafe(input, options = {}) {\n  const head = injectHead(options.nuxt);\n  if (head) {\n    return serverHeadSafe(input, { head, ...options });\n  }\n}\nexport function useServerSeoMeta(input, options = {}) {\n  const head = injectHead(options.nuxt);\n  if (head) {\n    return serverSeoMeta(input, { head, ...options });\n  }\n}\n", "import { hasProtocol, joinUR<PERSON>, withoutTrailingSlash } from \"ufo\";\nimport { parse } from \"devalue\";\nimport { getCurrentInstance, onServerPrefetch, reactive } from \"vue\";\nimport { useNuxtApp, useRuntimeConfig } from \"../nuxt.js\";\nimport { useHead } from \"./head.js\";\nimport { useRoute } from \"./router.js\";\nimport { getAppManifest, getRouteRules } from \"./manifest.js\";\nimport { appId, appManifest, multiApp, payloadExtraction, renderJsonPayloads } from \"#build/nuxt.config.mjs\";\nexport async function loadPayload(url, opts = {}) {\n  if (import.meta.server || !payloadExtraction) {\n    return null;\n  }\n  const shouldLoadPayload = await isPrerendered(url);\n  if (!shouldLoadPayload) {\n    return null;\n  }\n  const payloadURL = await _getPayloadURL(url, opts);\n  return await _importPayload(payloadURL) || null;\n}\nlet linkRelType;\nfunction detectLinkRelType() {\n  if (import.meta.server) {\n    return \"preload\";\n  }\n  if (linkRelType) {\n    return linkRelType;\n  }\n  const relList = document.createElement(\"link\").relList;\n  linkRelType = relList && relList.supports && relList.supports(\"prefetch\") ? \"prefetch\" : \"preload\";\n  return linkRelType;\n}\nexport function preloadPayload(url, opts = {}) {\n  const nuxtApp = useNuxtApp();\n  const promise = _getPayloadURL(url, opts).then((payloadURL) => {\n    const link = renderJsonPayloads ? { rel: detectLinkRelType(), as: \"fetch\", crossorigin: \"anonymous\", href: payloadURL } : { rel: \"modulepreload\", crossorigin: \"\", href: payloadURL };\n    if (import.meta.server) {\n      nuxtApp.runWithContext(() => useHead({ link: [link] }));\n    } else {\n      const linkEl = document.createElement(\"link\");\n      for (const key of Object.keys(link)) {\n        linkEl[key === \"crossorigin\" ? \"crossOrigin\" : key] = link[key];\n      }\n      document.head.appendChild(linkEl);\n      return new Promise((resolve, reject) => {\n        linkEl.addEventListener(\"load\", () => resolve());\n        linkEl.addEventListener(\"error\", () => reject());\n      });\n    }\n  });\n  if (import.meta.server) {\n    onServerPrefetch(() => promise);\n  }\n  return promise;\n}\nconst filename = renderJsonPayloads ? \"_payload.json\" : \"_payload.js\";\nasync function _getPayloadURL(url, opts = {}) {\n  const u = new URL(url, \"http://localhost\");\n  if (u.host !== \"localhost\" || hasProtocol(u.pathname, { acceptRelative: true })) {\n    throw new Error(\"Payload URL must not include hostname: \" + url);\n  }\n  const config = useRuntimeConfig();\n  const hash = opts.hash || (opts.fresh ? Date.now() : config.app.buildId);\n  const cdnURL = config.app.cdnURL;\n  const baseOrCdnURL = cdnURL && await isPrerendered(url) ? cdnURL : config.app.baseURL;\n  return joinURL(baseOrCdnURL, u.pathname, filename + (hash ? `?${hash}` : \"\"));\n}\nasync function _importPayload(payloadURL) {\n  if (import.meta.server || !payloadExtraction) {\n    return null;\n  }\n  const payloadPromise = renderJsonPayloads ? fetch(payloadURL, { cache: \"force-cache\" }).then((res) => res.text().then(parsePayload)) : import(\n    /* webpackIgnore: true */\n    /* @vite-ignore */\n    payloadURL\n  ).then((r) => r.default || r);\n  try {\n    return await payloadPromise;\n  } catch (err) {\n    console.warn(\"[nuxt] Cannot load payload \", payloadURL, err);\n  }\n  return null;\n}\nexport async function isPrerendered(url = useRoute().path) {\n  const nuxtApp = useNuxtApp();\n  if (!appManifest) {\n    return !!nuxtApp.payload.prerenderedAt;\n  }\n  url = withoutTrailingSlash(url);\n  const manifest = await getAppManifest();\n  if (manifest.prerendered.includes(url)) {\n    return true;\n  }\n  return nuxtApp.runWithContext(async () => {\n    const rules = await getRouteRules({ path: url });\n    return !!rules.prerender && !rules.redirect;\n  });\n}\nlet payloadCache = null;\nexport async function getNuxtClientPayload() {\n  if (import.meta.server) {\n    return null;\n  }\n  if (payloadCache) {\n    return payloadCache;\n  }\n  const el = multiApp ? document.querySelector(`[data-nuxt-data=\"${appId}\"]`) : document.getElementById(\"__NUXT_DATA__\");\n  if (!el) {\n    return {};\n  }\n  const inlineData = await parsePayload(el.textContent || \"\");\n  const externalData = el.dataset.src ? await _importPayload(el.dataset.src) : void 0;\n  payloadCache = {\n    ...inlineData,\n    ...externalData,\n    ...multiApp ? window.__NUXT__?.[appId] : window.__NUXT__\n  };\n  if (payloadCache.config?.public) {\n    payloadCache.config.public = reactive(payloadCache.config.public);\n  }\n  return payloadCache;\n}\nexport async function parsePayload(payload) {\n  return await parse(payload, useNuxtApp()._payloadRevivers);\n}\nexport function definePayloadReducer(name, reduce) {\n  if (import.meta.server) {\n    useNuxtApp().ssrContext._payloadReducers[name] = reduce;\n  }\n}\nexport function definePayloadReviver(name, revive) {\n  if (import.meta.dev && getCurrentInstance()) {\n    console.warn(\"[nuxt] [definePayloadReviver] This function must be called in a Nuxt plugin that is `unshift`ed to the beginning of the Nuxt plugins array.\");\n  }\n  if (import.meta.client) {\n    useNuxtApp()._payloadRevivers[name] = revive;\n  }\n}\n", "import { isReactive, isRef, isShallow, toRaw } from \"vue\";\nimport { definePayloadReducer } from \"../composables/payload.js\";\nimport { isNuxtError } from \"../composables/error.js\";\nimport { defineNuxtPlugin } from \"../nuxt.js\";\nimport { componentIslands } from \"#build/nuxt.config.mjs\";\nconst reducers = [\n  [\"NuxtError\", (data) => isNuxtError(data) && data.toJSON()],\n  [\"EmptyShallowRef\", (data) => isRef(data) && isShallow(data) && !data.value && (typeof data.value === \"bigint\" ? \"0n\" : JSON.stringify(data.value) || \"_\")],\n  [\"EmptyRef\", (data) => isRef(data) && !data.value && (typeof data.value === \"bigint\" ? \"0n\" : JSON.stringify(data.value) || \"_\")],\n  [\"ShallowRef\", (data) => isRef(data) && isShallow(data) && data.value],\n  [\"ShallowReactive\", (data) => isReactive(data) && isShallow(data) && toRaw(data)],\n  [\"Ref\", (data) => isRef(data) && data.value],\n  [\"Reactive\", (data) => isReactive(data) && toRaw(data)]\n];\nif (componentIslands) {\n  reducers.push([\"Island\", (data) => data && data?.__nuxt_island]);\n}\nexport default defineNuxtPlugin({\n  name: \"nuxt:revive-payload:server\",\n  setup() {\n    for (const [reducer, fn] of reducers) {\n      definePayloadReducer(reducer, fn);\n    }\n  }\n});\n", "\nimport { defineNuxtPlugin } from '#app/nuxt'\nexport default defineNuxtPlugin({\n  name: 'nuxt:global-components',\n})\n", "export const preference = \"system\"\n      \nexport const fallback = \"light\"\n      \nexport const hid = \"nuxt-color-mode-script\"\n      \nexport const globalName = \"__NUXT_COLOR_MODE__\"\n      \nexport const componentName = \"ColorScheme\"\n      \nexport const classPrefix = \"\"\n      \nexport const classSuffix = \"\"\n      \nexport const dataValue = \"\"\n      \nexport const storageKey = \"nuxt-color-mode\"\n      \nexport const storage = \"localStorage\"\n      \nexport const disableTransition = false\n      \nexport const script = \"\\\"use strict\\\";(()=>{const t=window,e=document.documentElement,c=[\\\"dark\\\",\\\"light\\\"],n=getStorageValue(\\\"localStorage\\\",\\\"nuxt-color-mode\\\")||\\\"system\\\";let i=n===\\\"system\\\"?u():n;const r=e.getAttribute(\\\"data-color-mode-forced\\\");r&&(i=r),l(i),t[\\\"__NUXT_COLOR_MODE__\\\"]={preference:n,value:i,getColorScheme:u,addColorScheme:l,removeColorScheme:d};function l(o){const s=\\\"\\\"+o+\\\"\\\",a=\\\"\\\";e.classList?e.classList.add(s):e.className+=\\\" \\\"+s,a&&e.setAttribute(\\\"data-\\\"+a,o)}function d(o){const s=\\\"\\\"+o+\\\"\\\",a=\\\"\\\";e.classList?e.classList.remove(s):e.className=e.className.replace(new RegExp(s,\\\"g\\\"),\\\"\\\"),a&&e.removeAttribute(\\\"data-\\\"+a)}function f(o){return t.matchMedia(\\\"(prefers-color-scheme\\\"+o+\\\")\\\")}function u(){if(t.matchMedia&&f(\\\"\\\").media!==\\\"not all\\\"){for(const o of c)if(f(\\\":\\\"+o).matches)return o}return\\\"light\\\"}})();function getStorageValue(t,e){switch(t){case\\\"localStorage\\\":return window.localStorage.getItem(e);case\\\"sessionStorage\\\":return window.sessionStorage.getItem(e);case\\\"cookie\\\":return getCookie(e);default:return null}}function getCookie(t){const c=(\\\"; \\\"+window.document.cookie).split(\\\"; \\\"+t+\\\"=\\\");if(c.length===2)return c.pop()?.split(\\\";\\\").shift()}\"\n      ", "import { isRef, toRef } from \"vue\";\nimport { useNuxtApp } from \"../nuxt.js\";\nimport { toArray } from \"../utils.js\";\nconst useStateKeyPrefix = \"$s\";\nexport function useState(...args) {\n  const autoKey = typeof args[args.length - 1] === \"string\" ? args.pop() : void 0;\n  if (typeof args[0] !== \"string\") {\n    args.unshift(autoKey);\n  }\n  const [_key, init] = args;\n  if (!_key || typeof _key !== \"string\") {\n    throw new TypeError(\"[nuxt] [useState] key must be a string: \" + _key);\n  }\n  if (init !== void 0 && typeof init !== \"function\") {\n    throw new Error(\"[nuxt] [useState] init must be a function: \" + init);\n  }\n  const key = useStateKeyPrefix + _key;\n  const nuxtApp = useNuxtApp();\n  const state = toRef(nuxtApp.payload.state, key);\n  if (state.value === void 0 && init) {\n    const initialValue = init();\n    if (isRef(initialValue)) {\n      nuxtApp.payload.state[key] = initialValue;\n      return initialValue;\n    }\n    state.value = initialValue;\n  }\n  return state;\n}\nexport function clearNuxtState(keys) {\n  const nuxtApp = useNuxtApp();\n  const _allKeys = Object.keys(nuxtApp.payload.state).map((key) => key.substring(useStateKeyPrefix.length));\n  const _keys = !keys ? _allKeys : typeof keys === \"function\" ? _allKeys.filter(keys) : toArray(keys);\n  for (const _key of _keys) {\n    const key = useStateKeyPrefix + _key;\n    if (key in nuxtApp.payload.state) {\n      nuxtApp.payload.state[key] = void 0;\n    }\n  }\n}\n", "import { reactive, ref } from \"vue\";\nimport { defineNuxtPlugin, isVue2, isVue3, useHead, useState, useRouter, useRequestHeaders } from \"#imports\";\nimport { preference, hid, script, dataValue, storage, storageKey } from \"#color-mode-options\";\nconst addScript = (head) => {\n  head.script = head.script || [];\n  head.script.push({\n    hid,\n    innerHTML: script\n  });\n  const serializeProp = \"__dangerouslyDisableSanitizersByTagID\";\n  head[serializeProp] = head[serializeProp] || {};\n  head[serializeProp][hid] = [\"innerHTML\"];\n};\nexport default defineNuxtPlugin((nuxtApp) => {\n  const colorMode = nuxtApp.ssrContext?.islandContext ? ref({}) : useState(\"color-mode\", () => reactive({\n    preference,\n    value: preference,\n    unknown: true,\n    forced: false\n  })).value;\n  const htmlAttrs = {};\n  if (isVue2) {\n    const app = nuxtApp.nuxt2Context.app;\n    if (typeof app.head === \"function\") {\n      const originalHead = app.head;\n      app.head = function() {\n        const head = originalHead.call(this) || {};\n        addScript(head);\n        head.htmlAttrs = htmlAttrs;\n        return head;\n      };\n    } else {\n      addScript(app.head);\n      app.head.htmlAttrs = htmlAttrs;\n    }\n  }\n  if (isVue3) {\n    if (storage === \"cookie\") {\n      const { cookie } = useRequestHeaders([\"cookie\"]);\n      const [, value] = cookie?.split(\"; \").map((s) => s.split(\"=\")).find(([k]) => k === storageKey) ?? [];\n      if (value) {\n        colorMode.preference = value;\n      }\n    }\n    useHead({ htmlAttrs });\n  }\n  useRouter().afterEach((to) => {\n    const forcedColorMode = isVue2 ? to.matched[0]?.components.default?.options?.colorMode : to.meta.colorMode;\n    if (forcedColorMode && forcedColorMode !== \"system\") {\n      colorMode.value = htmlAttrs[\"data-color-mode-forced\"] = forcedColorMode;\n      if (dataValue) {\n        htmlAttrs[`data-${dataValue}`] = colorMode.value;\n      }\n      colorMode.forced = true;\n    } else if (forcedColorMode === \"system\") {\n      console.warn(\"You cannot force the colorMode to system at the page level.\");\n    }\n  });\n  nuxtApp.provide(\"colorMode\", colorMode);\n});\n", "import unhead_k2P3m_ZDyjlr2mMYnoDPwavjsDN8hBlk9cFai0bbopU from \"/Users/<USER>/Webs/makechoice/node_modules/nuxt/dist/head/runtime/plugins/unhead.js\";\nimport router_GNCWhvtYfLTYRZZ135CdFAEjxdMexN0ixiUYCAN_tpw from \"/Users/<USER>/Webs/makechoice/node_modules/nuxt/dist/pages/runtime/plugins/router.js\";\nimport revive_payload_server_MVtmlZaQpj6ApFmshWfUWl5PehCebzaBf2NuRMiIbms from \"/Users/<USER>/Webs/makechoice/node_modules/nuxt/dist/app/plugins/revive-payload.server.js\";\nimport components_plugin_z4hgvsiddfKkfXTP6M8M4zG5Cb7sGnDhcryKVM45Di4 from \"/Users/<USER>/Webs/makechoice/.nuxt/components.plugin.mjs\";\nimport plugin_server_9Ca9_HhnjAGwBWpwAydRauMHxWoxTDY60BrArRnXN_A from \"/Users/<USER>/Webs/makechoice/node_modules/@nuxtjs/color-mode/dist/runtime/plugin.server.js\";\nexport default [\n  unhead_k2P3m_ZDyjlr2mMYnoDPwavjsDN8hBlk9cFai0bbopU,\n  router_GNCWhvtYfLTYRZZ135CdFAEjxdMexN0ixiUYCAN_tpw,\n  revive_payload_server_MVtmlZaQpj6ApFmshWfUWl5PehCebzaBf2NuRMiIbms,\n  components_plugin_z4hgvsiddfKkfXTP6M8M4zG5Cb7sGnDhcryKVM45Di4,\n  plugin_server_9Ca9_HhnjAGwBWpwAydRauMHxWoxTDY60BrArRnXN_A\n]", "import { defineAsyncComponent } from 'vue'\nexport default {\n  default: defineAsyncComponent(() => import(\"/Users/<USER>/Webs/makechoice/layouts/default.vue\").then(m => m.default || m))\n}", "import { Suspense, computed, defineComponent, h, inject, mergeProps, nextTick, onMounted, provide, shallowReactive, shallowRef, unref } from \"vue\";\nimport { useRoute, useRouter } from \"../composables/router.js\";\nimport { useNuxtApp } from \"../nuxt.js\";\nimport { _wrapInTransition } from \"./utils.js\";\nimport { LayoutMetaSymbol, PageRouteSymbol } from \"./injections.js\";\nimport { useRoute as useVueRouterRoute } from \"#build/pages\";\nimport layouts from \"#build/layouts\";\nimport { appLayoutTransition as defaultLayoutTransition } from \"#build/nuxt.config.mjs\";\nconst LayoutLoader = defineComponent({\n  name: \"LayoutLoader\",\n  inheritAttrs: false,\n  props: {\n    name: String,\n    layoutProps: Object\n  },\n  setup(props, context) {\n    return () => h(layouts[props.name], props.layoutProps, context.slots);\n  }\n});\nconst nuxtLayoutProps = {\n  name: {\n    type: [String, Boolean, Object],\n    default: null\n  },\n  fallback: {\n    type: [String, Object],\n    default: null\n  }\n};\nexport default defineComponent({\n  name: \"NuxtLayout\",\n  inheritAttrs: false,\n  props: nuxtLayoutProps,\n  setup(props, context) {\n    const nuxtApp = useNuxtApp();\n    const injectedRoute = inject(PageRouteSymbol);\n    const shouldUseEagerRoute = !injectedRoute || injectedRoute === useRoute();\n    const route = shouldUseEagerRoute ? useVueRouterRoute() : injectedRoute;\n    const layout = computed(() => {\n      let layout2 = unref(props.name) ?? route?.meta.layout ?? \"default\";\n      if (layout2 && !(layout2 in layouts)) {\n        if (import.meta.dev && layout2 !== \"default\") {\n          console.warn(`Invalid layout \\`${layout2}\\` selected.`);\n        }\n        if (props.fallback) {\n          layout2 = unref(props.fallback);\n        }\n      }\n      return layout2;\n    });\n    const layoutRef = shallowRef();\n    context.expose({ layoutRef });\n    const done = nuxtApp.deferHydration();\n    if (import.meta.client && nuxtApp.isHydrating) {\n      const removeErrorHook = nuxtApp.hooks.hookOnce(\"app:error\", done);\n      useRouter().beforeEach(removeErrorHook);\n    }\n    if (import.meta.dev) {\n      nuxtApp._isNuxtLayoutUsed = true;\n    }\n    let lastLayout;\n    return () => {\n      const hasLayout = layout.value && layout.value in layouts;\n      const transitionProps = route?.meta.layoutTransition ?? defaultLayoutTransition;\n      const previouslyRenderedLayout = lastLayout;\n      lastLayout = layout.value;\n      return _wrapInTransition(hasLayout && transitionProps, {\n        default: () => h(Suspense, { suspensible: true, onResolve: () => {\n          nextTick(done);\n        } }, {\n          default: () => h(\n            LayoutProvider,\n            {\n              layoutProps: mergeProps(context.attrs, { ref: layoutRef }),\n              key: layout.value || void 0,\n              name: layout.value,\n              shouldProvide: !props.name,\n              isRenderingNewLayout: (name) => {\n                return name !== previouslyRenderedLayout && name === layout.value;\n              },\n              hasTransition: !!transitionProps\n            },\n            context.slots\n          )\n        })\n      }).default();\n    };\n  }\n});\nconst LayoutProvider = defineComponent({\n  name: \"NuxtLayoutProvider\",\n  inheritAttrs: false,\n  props: {\n    name: {\n      type: [String, Boolean]\n    },\n    layoutProps: {\n      type: Object\n    },\n    hasTransition: {\n      type: Boolean\n    },\n    shouldProvide: {\n      type: Boolean\n    },\n    isRenderingNewLayout: {\n      type: Function,\n      required: true\n    }\n  },\n  setup(props, context) {\n    const name = props.name;\n    if (props.shouldProvide) {\n      provide(LayoutMetaSymbol, {\n        isCurrent: (route) => name === (route.meta.layout ?? \"default\")\n      });\n    }\n    const injectedRoute = inject(PageRouteSymbol);\n    const isNotWithinNuxtPage = injectedRoute && injectedRoute === useRoute();\n    if (isNotWithinNuxtPage) {\n      const vueRouterRoute = useVueRouterRoute();\n      const reactiveChildRoute = {};\n      for (const _key in vueRouterRoute) {\n        const key = _key;\n        Object.defineProperty(reactiveChildRoute, key, {\n          enumerable: true,\n          get: () => {\n            return props.isRenderingNewLayout(props.name) ? vueRouterRoute[key] : injectedRoute[key];\n          }\n        });\n      }\n      provide(PageRouteSymbol, shallowReactive(reactiveChildRoute));\n    }\n    let vnode;\n    if (import.meta.dev && import.meta.client) {\n      onMounted(() => {\n        nextTick(() => {\n          if ([\"#comment\", \"#text\"].includes(vnode?.el?.nodeName)) {\n            if (name) {\n              console.warn(`[nuxt] \\`${name}\\` layout does not have a single root node and will cause errors when navigating between routes.`);\n            } else {\n              console.warn(\"[nuxt] `<NuxtLayout>` needs to be passed a single root node in its default slot.\");\n            }\n          }\n        });\n      });\n    }\n    return () => {\n      if (!name || typeof name === \"string\" && !(name in layouts)) {\n        if (import.meta.dev && import.meta.client && props.hasTransition) {\n          vnode = context.slots.default?.();\n          return vnode;\n        }\n        return context.slots.default?.();\n      }\n      if (import.meta.dev && import.meta.client && props.hasTransition) {\n        vnode = h(\n          LayoutLoader,\n          { key: name, layoutProps: props.layoutProps, name },\n          context.slots\n        );\n        return vnode;\n      }\n      return h(\n        LayoutLoader,\n        { key: name, layoutProps: props.layoutProps, name },\n        context.slots\n      );\n    };\n  }\n});\n", "import { defineComponent, h, nextTick, onMounted, provide, shallowReactive } from \"vue\";\nimport { PageRouteSymbol } from \"./injections.js\";\nexport const defineRouteProvider = (name = \"RouteProvider\") => defineComponent({\n  name,\n  props: {\n    route: {\n      type: Object,\n      required: true\n    },\n    vnode: Object,\n    vnodeRef: Object,\n    renderKey: String,\n    trackRootNodes: Boolean\n  },\n  setup(props) {\n    const previousKey = props.renderKey;\n    const previousRoute = props.route;\n    const route = {};\n    for (const key in props.route) {\n      Object.defineProperty(route, key, {\n        get: () => previousKey === props.renderKey ? props.route[key] : previousRoute[key],\n        enumerable: true\n      });\n    }\n    provide(PageRouteSymbol, shallowReactive(route));\n    let vnode;\n    if (import.meta.dev && import.meta.client && props.trackRootNodes) {\n      onMounted(() => {\n        nextTick(() => {\n          if ([\"#comment\", \"#text\"].includes(vnode?.el?.nodeName)) {\n            const filename = vnode?.type?.__file;\n            console.warn(`[nuxt] \\`${filename}\\` does not have a single root node and will cause errors when navigating between routes.`);\n          }\n        });\n      });\n    }\n    return () => {\n      if (!props.vnode) {\n        return props.vnode;\n      }\n      if (import.meta.dev && import.meta.client) {\n        vnode = h(props.vnode, { ref: props.vnodeRef });\n        return vnode;\n      }\n      return h(props.vnode, { ref: props.vnodeRef });\n    };\n  }\n});\nexport const RouteProvider = defineRouteProvider();\n", "import { Fragment, Suspense, defineComponent, h, inject, nextTick, onBeforeUnmount, ref, watch } from \"vue\";\nimport { RouterView } from \"vue-router\";\nimport { defu } from \"defu\";\nimport { generateRouteKey, toArray, wrapInKeepAlive } from \"./utils.js\";\nimport { RouteProvider, defineRouteProvider } from \"#app/components/route-provider\";\nimport { useNuxtApp } from \"#app/nuxt\";\nimport { useRouter } from \"#app/composables/router\";\nimport { _wrapInTransition } from \"#app/components/utils\";\nimport { LayoutMetaSymbol, PageRouteSymbol } from \"#app/components/injections\";\nimport { appKeepalive as defaultKeepaliveConfig, appPageTransition as defaultPageTransition } from \"#build/nuxt.config.mjs\";\nconst _routeProviders = import.meta.dev ? /* @__PURE__ */ new Map() : /* @__PURE__ */ new WeakMap();\nexport default defineComponent({\n  name: \"NuxtPage\",\n  inheritAttrs: false,\n  props: {\n    name: {\n      type: String\n    },\n    transition: {\n      type: [Boolean, Object],\n      default: void 0\n    },\n    keepalive: {\n      type: [Boolean, Object],\n      default: void 0\n    },\n    route: {\n      type: Object\n    },\n    pageKey: {\n      type: [Function, String],\n      default: null\n    }\n  },\n  setup(props, { attrs, slots, expose }) {\n    const nuxtApp = useNuxtApp();\n    const pageRef = ref();\n    const forkRoute = inject(PageRouteSymbol, null);\n    let previousPageKey;\n    expose({ pageRef });\n    const _layoutMeta = inject(LayoutMetaSymbol, null);\n    let vnode;\n    const done = nuxtApp.deferHydration();\n    if (import.meta.client && nuxtApp.isHydrating) {\n      const removeErrorHook = nuxtApp.hooks.hookOnce(\"app:error\", done);\n      useRouter().beforeEach(removeErrorHook);\n    }\n    if (import.meta.client && props.pageKey) {\n      watch(() => props.pageKey, (next, prev) => {\n        if (next !== prev) {\n          nuxtApp.callHook(\"page:loading:start\");\n        }\n      });\n    }\n    if (import.meta.dev) {\n      nuxtApp._isNuxtPageUsed = true;\n    }\n    let pageLoadingEndHookAlreadyCalled = false;\n    if (import.meta.client) {\n      const unsub = useRouter().beforeResolve(() => {\n        pageLoadingEndHookAlreadyCalled = false;\n      });\n      onBeforeUnmount(() => {\n        unsub();\n      });\n    }\n    return () => {\n      return h(RouterView, { name: props.name, route: props.route, ...attrs }, {\n        default: import.meta.server ? (routeProps) => {\n          return h(Suspense, { suspensible: true }, {\n            default() {\n              return h(RouteProvider, {\n                vnode: slots.default ? normalizeSlot(slots.default, routeProps) : routeProps.Component,\n                route: routeProps.route,\n                vnodeRef: pageRef\n              });\n            }\n          });\n        } : (routeProps) => {\n          const isRenderingNewRouteInOldFork = haveParentRoutesRendered(forkRoute, routeProps.route, routeProps.Component);\n          const hasSameChildren = forkRoute && forkRoute.matched.length === routeProps.route.matched.length;\n          if (!routeProps.Component) {\n            if (vnode && !hasSameChildren) {\n              return vnode;\n            }\n            done();\n            return;\n          }\n          if (vnode && _layoutMeta && !_layoutMeta.isCurrent(routeProps.route)) {\n            return vnode;\n          }\n          if (isRenderingNewRouteInOldFork && forkRoute && (!_layoutMeta || _layoutMeta?.isCurrent(forkRoute))) {\n            if (hasSameChildren) {\n              return vnode;\n            }\n            return null;\n          }\n          const key = generateRouteKey(routeProps, props.pageKey);\n          const willRenderAnotherChild = hasChildrenRoutes(forkRoute, routeProps.route, routeProps.Component);\n          if (!nuxtApp.isHydrating && previousPageKey === key && !willRenderAnotherChild) {\n            nextTick(() => {\n              pageLoadingEndHookAlreadyCalled = true;\n              nuxtApp.callHook(\"page:loading:end\");\n            });\n          }\n          previousPageKey = key;\n          const hasTransition = !!(props.transition ?? routeProps.route.meta.pageTransition ?? defaultPageTransition);\n          const transitionProps = hasTransition && _mergeTransitionProps([\n            props.transition,\n            routeProps.route.meta.pageTransition,\n            defaultPageTransition,\n            {\n              onBeforeLeave() {\n                nuxtApp._runningTransition = true;\n              },\n              onAfterLeave() {\n                delete nuxtApp._runningTransition;\n                nuxtApp.callHook(\"page:transition:finish\", routeProps.Component);\n              }\n            }\n          ]);\n          const keepaliveConfig = props.keepalive ?? routeProps.route.meta.keepalive ?? defaultKeepaliveConfig;\n          vnode = _wrapInTransition(\n            hasTransition && transitionProps,\n            wrapInKeepAlive(\n              keepaliveConfig,\n              h(Suspense, {\n                suspensible: true,\n                onPending: () => nuxtApp.callHook(\"page:start\", routeProps.Component),\n                onResolve: () => {\n                  nextTick(() => nuxtApp.callHook(\"page:finish\", routeProps.Component).then(() => {\n                    if (!pageLoadingEndHookAlreadyCalled && !willRenderAnotherChild) {\n                      pageLoadingEndHookAlreadyCalled = true;\n                      return nuxtApp.callHook(\"page:loading:end\");\n                    }\n                  }).finally(done));\n                }\n              }, {\n                default: () => {\n                  const routeProviderProps = {\n                    key: key || void 0,\n                    vnode: slots.default ? normalizeSlot(slots.default, routeProps) : routeProps.Component,\n                    route: routeProps.route,\n                    renderKey: key || void 0,\n                    trackRootNodes: hasTransition,\n                    vnodeRef: pageRef\n                  };\n                  if (!keepaliveConfig) {\n                    return h(RouteProvider, routeProviderProps);\n                  }\n                  const routerComponentType = routeProps.Component.type;\n                  const routeProviderKey = import.meta.dev ? routerComponentType.name || routerComponentType.__name : routerComponentType;\n                  let PageRouteProvider = _routeProviders.get(routeProviderKey);\n                  if (!PageRouteProvider) {\n                    PageRouteProvider = defineRouteProvider(routerComponentType.name || routerComponentType.__name);\n                    _routeProviders.set(routeProviderKey, PageRouteProvider);\n                  }\n                  return h(PageRouteProvider, routeProviderProps);\n                }\n              })\n            )\n          ).default();\n          return vnode;\n        }\n      });\n    };\n  }\n});\nfunction _mergeTransitionProps(routeProps) {\n  const _props = routeProps.filter(Boolean).map((prop) => ({\n    ...prop,\n    onAfterLeave: prop.onAfterLeave ? toArray(prop.onAfterLeave) : void 0\n  }));\n  return defu(..._props);\n}\nfunction haveParentRoutesRendered(fork, newRoute, Component) {\n  if (!fork) {\n    return false;\n  }\n  const index = newRoute.matched.findIndex((m) => m.components?.default === Component?.type);\n  if (!index || index === -1) {\n    return false;\n  }\n  return newRoute.matched.slice(0, index).some(\n    (c, i) => c.components?.default !== fork.matched[i]?.components?.default\n  ) || Component && generateRouteKey({ route: newRoute, Component }) !== generateRouteKey({ route: fork, Component });\n}\nfunction hasChildrenRoutes(fork, newRoute, Component) {\n  if (!fork) {\n    return false;\n  }\n  const index = newRoute.matched.findIndex((m) => m.components?.default === Component?.type);\n  return index < newRoute.matched.length - 1;\n}\nfunction normalizeSlot(slot, data) {\n  const slotContent = slot(data);\n  return slotContent.length === 1 ? h(slotContent[0]) : h(Fragment, void 0, slotContent);\n}\n", "<template>\n  <div>\n    <NuxtLayout>\n      <NuxtPage />\n    </NuxtLayout>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\n// 全局应用配置\nuseHead({\n  htmlAttrs: {\n    lang: 'en'\n  }\n})\n\n// 确保客户端水合正常\nonMounted(() => {\n  // 初始化应用\n  console.log('Nuxt app mounted')\n})\n</script>\n\n<style>\n/* 全局样式已在 assets/css/main.css 中定义 */\n</style>\n", "import { computed, defineComponent, h, inject, onBeforeUnmount, onMounted, provide, ref, resolveComponent, shallowRef } from \"vue\";\nimport { hasProtocol, joinURL, parseQuery, withTrailingSlash, withoutTrailingSlash } from \"ufo\";\nimport { preloadRouteComponents } from \"../composables/preload.js\";\nimport { onNuxtReady } from \"../composables/ready.js\";\nimport { navigateTo, resolveRouteObject, useRouter } from \"../composables/router.js\";\nimport { useNuxtApp, useRuntimeConfig } from \"../nuxt.js\";\nimport { cancelIdleCallback, requestIdleCallback } from \"../compat/idle-callback.js\";\nimport { nuxtLinkDefaults } from \"#build/nuxt.config.mjs\";\nimport { hashMode } from \"#build/router.options\";\nconst firstNonUndefined = (...args) => args.find((arg) => arg !== void 0);\nconst NuxtLinkDevKeySymbol = Symbol(\"nuxt-link-dev-key\");\n// @__NO_SIDE_EFFECTS__\nexport function defineNuxtLink(options) {\n  const componentName = options.componentName || \"NuxtLink\";\n  function checkPropConflicts(props, main, sub) {\n    if (import.meta.dev && props[main] !== void 0 && props[sub] !== void 0) {\n      console.warn(`[${componentName}] \\`${main}\\` and \\`${sub}\\` cannot be used together. \\`${sub}\\` will be ignored.`);\n    }\n  }\n  function isHashLinkWithoutHashMode(link) {\n    return !hashMode && typeof link === \"string\" && link.startsWith(\"#\");\n  }\n  function resolveTrailingSlashBehavior(to, resolve, trailingSlash) {\n    const effectiveTrailingSlash = trailingSlash ?? options.trailingSlash;\n    if (!to || effectiveTrailingSlash !== \"append\" && effectiveTrailingSlash !== \"remove\") {\n      return to;\n    }\n    if (typeof to === \"string\") {\n      return applyTrailingSlashBehavior(to, effectiveTrailingSlash);\n    }\n    const path = \"path\" in to && to.path !== void 0 ? to.path : resolve(to).path;\n    const resolvedPath = {\n      ...to,\n      name: void 0,\n      // named routes would otherwise always override trailing slash behavior\n      path: applyTrailingSlashBehavior(path, effectiveTrailingSlash)\n    };\n    return resolvedPath;\n  }\n  function useNuxtLink(props) {\n    const router = useRouter();\n    const config = useRuntimeConfig();\n    const hasTarget = computed(() => !!props.target && props.target !== \"_self\");\n    const isAbsoluteUrl = computed(() => {\n      const path = props.to || props.href || \"\";\n      return typeof path === \"string\" && hasProtocol(path, { acceptRelative: true });\n    });\n    const builtinRouterLink = resolveComponent(\"RouterLink\");\n    const useBuiltinLink = builtinRouterLink && typeof builtinRouterLink !== \"string\" ? builtinRouterLink.useLink : void 0;\n    const isExternal = computed(() => {\n      if (props.external) {\n        return true;\n      }\n      const path = props.to || props.href || \"\";\n      if (typeof path === \"object\") {\n        return false;\n      }\n      return path === \"\" || isAbsoluteUrl.value;\n    });\n    const to = computed(() => {\n      checkPropConflicts(props, \"to\", \"href\");\n      const path = props.to || props.href || \"\";\n      if (isExternal.value) {\n        return path;\n      }\n      return resolveTrailingSlashBehavior(path, router.resolve, props.trailingSlash);\n    });\n    const link = isExternal.value ? void 0 : useBuiltinLink?.({ ...props, to });\n    const href = computed(() => {\n      const effectiveTrailingSlash = props.trailingSlash ?? options.trailingSlash;\n      if (!to.value || isAbsoluteUrl.value || isHashLinkWithoutHashMode(to.value)) {\n        return to.value;\n      }\n      if (isExternal.value) {\n        const path = typeof to.value === \"object\" && \"path\" in to.value ? resolveRouteObject(to.value) : to.value;\n        const href2 = typeof path === \"object\" ? router.resolve(path).href : path;\n        return applyTrailingSlashBehavior(href2, effectiveTrailingSlash);\n      }\n      if (typeof to.value === \"object\") {\n        return router.resolve(to.value)?.href ?? null;\n      }\n      return applyTrailingSlashBehavior(joinURL(config.app.baseURL, to.value), effectiveTrailingSlash);\n    });\n    return {\n      to,\n      hasTarget,\n      isAbsoluteUrl,\n      isExternal,\n      //\n      href,\n      isActive: link?.isActive ?? computed(() => to.value === router.currentRoute.value.path),\n      isExactActive: link?.isExactActive ?? computed(() => to.value === router.currentRoute.value.path),\n      route: link?.route ?? computed(() => router.resolve(to.value)),\n      async navigate(_e) {\n        await navigateTo(href.value, { replace: props.replace, external: isExternal.value || hasTarget.value });\n      }\n    };\n  }\n  return defineComponent({\n    name: componentName,\n    props: {\n      // Routing\n      to: {\n        type: [String, Object],\n        default: void 0,\n        required: false\n      },\n      href: {\n        type: [String, Object],\n        default: void 0,\n        required: false\n      },\n      // Attributes\n      target: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      rel: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      noRel: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      // Prefetching\n      prefetch: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      prefetchOn: {\n        type: [String, Object],\n        default: void 0,\n        required: false\n      },\n      noPrefetch: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      // Styling\n      activeClass: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      exactActiveClass: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      prefetchedClass: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      // Vue Router's `<RouterLink>` additional props\n      replace: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      ariaCurrentValue: {\n        type: String,\n        default: void 0,\n        required: false\n      },\n      // Edge cases handling\n      external: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      // Slot API\n      custom: {\n        type: Boolean,\n        default: void 0,\n        required: false\n      },\n      // Behavior\n      trailingSlash: {\n        type: String,\n        default: void 0,\n        required: false\n      }\n    },\n    useLink: useNuxtLink,\n    setup(props, { slots }) {\n      const router = useRouter();\n      const { to, href, navigate, isExternal, hasTarget, isAbsoluteUrl } = useNuxtLink(props);\n      const prefetched = shallowRef(false);\n      const el = import.meta.server ? void 0 : ref(null);\n      const elRef = import.meta.server ? void 0 : (ref2) => {\n        el.value = props.custom ? ref2?.$el?.nextElementSibling : ref2?.$el;\n      };\n      function shouldPrefetch(mode) {\n        if (import.meta.server) {\n          return;\n        }\n        return !prefetched.value && (typeof props.prefetchOn === \"string\" ? props.prefetchOn === mode : props.prefetchOn?.[mode] ?? options.prefetchOn?.[mode]) && (props.prefetch ?? options.prefetch) !== false && props.noPrefetch !== true && props.target !== \"_blank\" && !isSlowConnection();\n      }\n      async function prefetch(nuxtApp = useNuxtApp()) {\n        if (import.meta.server) {\n          return;\n        }\n        if (prefetched.value) {\n          return;\n        }\n        prefetched.value = true;\n        const path = typeof to.value === \"string\" ? to.value : isExternal.value ? resolveRouteObject(to.value) : router.resolve(to.value).fullPath;\n        const normalizedPath = isExternal.value ? new URL(path, window.location.href).href : path;\n        await Promise.all([\n          nuxtApp.hooks.callHook(\"link:prefetch\", normalizedPath).catch(() => {\n          }),\n          !isExternal.value && !hasTarget.value && preloadRouteComponents(to.value, router).catch(() => {\n          })\n        ]);\n      }\n      if (import.meta.client) {\n        checkPropConflicts(props, \"prefetch\", \"noPrefetch\");\n        if (shouldPrefetch(\"visibility\")) {\n          const nuxtApp = useNuxtApp();\n          let idleId;\n          let unobserve = null;\n          onMounted(() => {\n            const observer = useObserver();\n            onNuxtReady(() => {\n              idleId = requestIdleCallback(() => {\n                if (el?.value?.tagName) {\n                  unobserve = observer.observe(el.value, async () => {\n                    unobserve?.();\n                    unobserve = null;\n                    await prefetch(nuxtApp);\n                  });\n                }\n              });\n            });\n          });\n          onBeforeUnmount(() => {\n            if (idleId) {\n              cancelIdleCallback(idleId);\n            }\n            unobserve?.();\n            unobserve = null;\n          });\n        }\n      }\n      if (import.meta.dev && import.meta.server && !props.custom) {\n        const isNuxtLinkChild = inject(NuxtLinkDevKeySymbol, false);\n        if (isNuxtLinkChild) {\n          console.log(\"[nuxt] [NuxtLink] You can't nest one <a> inside another <a>. This will cause a hydration error on client-side. You can pass the `custom` prop to take full control of the markup.\");\n        } else {\n          provide(NuxtLinkDevKeySymbol, true);\n        }\n      }\n      return () => {\n        if (!isExternal.value && !hasTarget.value && !isHashLinkWithoutHashMode(to.value)) {\n          const routerLinkProps = {\n            ref: elRef,\n            to: to.value,\n            activeClass: props.activeClass || options.activeClass,\n            exactActiveClass: props.exactActiveClass || options.exactActiveClass,\n            replace: props.replace,\n            ariaCurrentValue: props.ariaCurrentValue,\n            custom: props.custom\n          };\n          if (!props.custom) {\n            if (import.meta.client) {\n              if (shouldPrefetch(\"interaction\")) {\n                routerLinkProps.onPointerenter = prefetch.bind(null, void 0);\n                routerLinkProps.onFocus = prefetch.bind(null, void 0);\n              }\n              if (prefetched.value) {\n                routerLinkProps.class = props.prefetchedClass || options.prefetchedClass;\n              }\n            }\n            routerLinkProps.rel = props.rel || void 0;\n          }\n          return h(\n            resolveComponent(\"RouterLink\"),\n            routerLinkProps,\n            slots.default\n          );\n        }\n        const target = props.target || null;\n        checkPropConflicts(props, \"noRel\", \"rel\");\n        const rel = firstNonUndefined(\n          // converts `\"\"` to `null` to prevent the attribute from being added as empty (`rel=\"\"`)\n          props.noRel ? \"\" : props.rel,\n          options.externalRelAttribute,\n          /*\n          * A fallback rel of `noopener noreferrer` is applied for external links or links that open in a new tab.\n          * This solves a reverse tabnapping security flaw in browsers pre-2021 as well as improving privacy.\n          */\n          isAbsoluteUrl.value || hasTarget.value ? \"noopener noreferrer\" : \"\"\n        ) || null;\n        if (props.custom) {\n          if (!slots.default) {\n            return null;\n          }\n          return slots.default({\n            href: href.value,\n            navigate,\n            prefetch,\n            get route() {\n              if (!href.value) {\n                return void 0;\n              }\n              const url = new URL(href.value, import.meta.client ? window.location.href : \"http://localhost\");\n              return {\n                path: url.pathname,\n                fullPath: url.pathname,\n                get query() {\n                  return parseQuery(url.search);\n                },\n                hash: url.hash,\n                params: {},\n                name: void 0,\n                matched: [],\n                redirectedFrom: void 0,\n                meta: {},\n                href: href.value\n              };\n            },\n            rel,\n            target,\n            isExternal: isExternal.value || hasTarget.value,\n            isActive: false,\n            isExactActive: false\n          });\n        }\n        return h(\"a\", { ref: el, href: href.value || null, rel, target }, slots.default?.());\n      };\n    }\n    // }) as unknown as DefineComponent<NuxtLinkProps, object, object, ComputedOptions, MethodOptions, object, object, EmitsOptions, string, object, NuxtLinkProps, object, SlotsType<NuxtLinkSlots>>\n  });\n}\nexport default /* @__PURE__ */ defineNuxtLink(nuxtLinkDefaults);\nfunction applyTrailingSlashBehavior(to, trailingSlash) {\n  const normalizeFn = trailingSlash === \"append\" ? withTrailingSlash : withoutTrailingSlash;\n  const hasProtocolDifferentFromHttp = hasProtocol(to) && !to.startsWith(\"http\");\n  if (hasProtocolDifferentFromHttp) {\n    return to;\n  }\n  return normalizeFn(to, true);\n}\nfunction useObserver() {\n  if (import.meta.server) {\n    return;\n  }\n  const nuxtApp = useNuxtApp();\n  if (nuxtApp._observer) {\n    return nuxtApp._observer;\n  }\n  let observer = null;\n  const callbacks = /* @__PURE__ */ new Map();\n  const observe = (element, callback) => {\n    observer ||= new IntersectionObserver((entries) => {\n      for (const entry of entries) {\n        const callback2 = callbacks.get(entry.target);\n        const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n        if (isVisible && callback2) {\n          callback2();\n        }\n      }\n    });\n    callbacks.set(element, callback);\n    observer.observe(element);\n    return () => {\n      callbacks.delete(element);\n      observer?.unobserve(element);\n      if (callbacks.size === 0) {\n        observer?.disconnect();\n        observer = null;\n      }\n    };\n  };\n  const _observer = nuxtApp._observer = {\n    observe\n  };\n  return _observer;\n}\nconst IS_2G_RE = /2g/;\nfunction isSlowConnection() {\n  if (import.meta.server) {\n    return;\n  }\n  const cn = navigator.connection;\n  if (cn && (cn.saveData || IS_2G_RE.test(cn.effectiveType))) {\n    return true;\n  }\n  return false;\n}\n", "<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center px-4\">\n    <div class=\"max-w-md w-full text-center\">\n      <!-- 错误图标 -->\n      <div class=\"mb-8\">\n        <div class=\"mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center\">\n          <svg\n            class=\"w-12 h-12 text-red-600 dark:text-red-400\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"round\"\n              stroke-width=\"2\"\n              d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            />\n          </svg>\n        </div>\n      </div>\n\n      <!-- 错误信息 -->\n      <h1 class=\"text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4\">\n        {{ error.statusCode }}\n      </h1>\n      \n      <h2 class=\"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-4\">\n        {{ getErrorTitle() }}\n      </h2>\n      \n      <p class=\"text-gray-600 dark:text-gray-400 mb-8\">\n        {{ getErrorMessage() }}\n      </p>\n\n      <!-- 操作按钮 -->\n      <div class=\"space-y-4\">\n        <button\n          @click=\"handleError\"\n          class=\"w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n        >\n          {{ getActionText() }}\n        </button>\n        \n        <NuxtLink\n          to=\"/\"\n          class=\"block w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors font-medium\"\n        >\n          Go to Homepage\n        </NuxtLink>\n      </div>\n\n      <!-- 调试信息（仅开发环境） -->\n      <div\n        v-if=\"isDevelopment && error.stack\"\n        class=\"mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-left\"\n      >\n        <details>\n          <summary class=\"cursor-pointer font-medium text-gray-900 dark:text-gray-100 mb-2\">\n            Debug Information\n          </summary>\n          <pre class=\"text-xs text-gray-600 dark:text-gray-400 overflow-auto\">{{ error.stack }}</pre>\n        </details>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\ninterface ErrorProps {\n  error: {\n    statusCode: number\n    statusMessage?: string\n    message?: string\n    stack?: string\n    data?: any\n  }\n}\n\nconst props = defineProps<ErrorProps>()\n\n// 检查是否为开发环境\nconst isDevelopment = process.env.NODE_ENV === 'development'\n\n// 获取错误标题\nconst getErrorTitle = (): string => {\n  switch (props.error.statusCode) {\n    case 404:\n      return 'Page Not Found'\n    case 500:\n      return 'Internal Server Error'\n    case 403:\n      return 'Access Forbidden'\n    case 401:\n      return 'Unauthorized'\n    case 400:\n      return 'Bad Request'\n    default:\n      return 'Something went wrong'\n  }\n}\n\n// 获取错误消息\nconst getErrorMessage = (): string => {\n  if (props.error.statusMessage) {\n    return props.error.statusMessage\n  }\n\n  switch (props.error.statusCode) {\n    case 404:\n      return \"The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.\"\n    case 500:\n      return \"We're experiencing some technical difficulties. Please try again later.\"\n    case 403:\n      return \"You don't have permission to access this resource.\"\n    case 401:\n      return \"You need to be authenticated to access this page.\"\n    case 400:\n      return \"The request was invalid. Please check your input and try again.\"\n    default:\n      return \"An unexpected error occurred. Please try again later.\"\n  }\n}\n\n// 获取操作按钮文本\nconst getActionText = (): string => {\n  switch (props.error.statusCode) {\n    case 404:\n      return 'Go Back'\n    case 500:\n      return 'Retry'\n    default:\n      return 'Try Again'\n  }\n}\n\n// 处理错误操作\nconst handleError = () => {\n  switch (props.error.statusCode) {\n    case 404:\n      // 返回上一页或首页\n      if (window.history.length > 1) {\n        window.history.back()\n      } else {\n        navigateTo('/')\n      }\n      break\n    case 500:\n      // 重新加载页面\n      window.location.reload()\n      break\n    default:\n      // 清除错误并返回首页\n      clearError({ redirect: '/' })\n      break\n  }\n}\n\n// SEO 配置\nuseSeoMeta({\n  title: `${props.error.statusCode} - ${getErrorTitle()}`,\n  description: getErrorMessage(),\n  robots: 'noindex, nofollow'\n})\n\n// 页面配置\ndefinePageMeta({\n  layout: false\n})\n\n// 错误上报（生产环境）\nonMounted(() => {\n  if (!isDevelopment) {\n    // 这里可以添加错误上报逻辑\n    console.error('Page Error:', {\n      statusCode: props.error.statusCode,\n      message: props.error.message,\n      url: window.location.href,\n      userAgent: navigator.userAgent,\n      timestamp: new Date().toISOString()\n    })\n  }\n})\n</script>\n\n<style scoped>\n/* 错误页面动画 */\n.error-page-enter-active {\n  transition: all 0.3s ease-out;\n}\n\n.error-page-enter-from {\n  opacity: 0;\n  transform: translateY(20px);\n}\n\n/* 按钮悬停效果 */\nbutton:hover,\na:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.dark button:hover,\n.dark a:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n}\n\n/* 调试信息样式 */\ndetails summary::-webkit-details-marker {\n  display: none;\n}\n\ndetails summary::before {\n  content: '▶';\n  margin-right: 0.5rem;\n  transition: transform 0.2s ease;\n}\n\ndetails[open] summary::before {\n  transform: rotate(90deg);\n}\n\n/* 响应式调整 */\n@media (max-width: 640px) {\n  .max-w-md {\n    max-width: 100%;\n  }\n  \n  h1 {\n    font-size: 2.5rem;\n  }\n  \n  h2 {\n    font-size: 1.25rem;\n  }\n}\n\n/* 高对比度模式 */\n@media (prefers-contrast: high) {\n  .bg-red-100 {\n    background-color: #fecaca;\n  }\n  \n  .dark .bg-red-900\\/20 {\n    background-color: rgba(127, 29, 29, 0.4);\n  }\n}\n\n/* 减少动画模式 */\n@media (prefers-reduced-motion: reduce) {\n  .error-page-enter-active,\n  button,\n  a {\n    transition: none;\n  }\n  \n  button:hover,\n  a:hover {\n    transform: none;\n  }\n}\n</style>\n", "<template>\n  <Suspense @resolve=\"onResolve\">\n    <div v-if=\"abortRender\" />\n    <ErrorComponent\n      v-else-if=\"error\"\n      :error=\"error\"\n    />\n    <IslandRenderer\n      v-else-if=\"islandContext\"\n      :context=\"islandContext\"\n    />\n    <component\n      :is=\"SingleRenderer\"\n      v-else-if=\"SingleRenderer\"\n    />\n    <AppComponent v-else />\n  </Suspense>\n</template>\n\n<script setup>\nimport { defineAsyncComponent, onErrorCaptured, onServerPrefetch, provide } from \"vue\";\nimport { useNuxtApp } from \"../nuxt\";\nimport { isNuxtError, showError, useError } from \"../composables/error\";\nimport { useRoute, useRouter } from \"../composables/router\";\nimport { PageRouteSymbol } from \"../components/injections\";\nimport AppComponent from \"#build/app-component.mjs\";\nimport ErrorComponent from \"#build/error-component.mjs\";\nimport { componentIslands } from \"#build/nuxt.config.mjs\";\nconst IslandRenderer = import.meta.server && componentIslands ? defineAsyncComponent(() => import(\"./island-renderer\").then((r) => r.default || r)) : () => null;\nconst nuxtApp = useNuxtApp();\nconst onResolve = nuxtApp.deferHydration();\nif (import.meta.client && nuxtApp.isHydrating) {\n  const removeErrorHook = nuxtApp.hooks.hookOnce(\"app:error\", onResolve);\n  useRouter().beforeEach(removeErrorHook);\n}\nconst url = import.meta.server ? nuxtApp.ssrContext.url : window.location.pathname;\nconst SingleRenderer = import.meta.test && import.meta.dev && import.meta.server && url.startsWith(\"/__nuxt_component_test__/\") && defineAsyncComponent(() => import(\"#build/test-component-wrapper.mjs\").then((r) => r.default(import.meta.server ? url : window.location.href)));\nprovide(PageRouteSymbol, useRoute());\nconst results = nuxtApp.hooks.callHookWith((hooks) => hooks.map((hook) => hook()), \"vue:setup\");\nif (import.meta.dev && results && results.some((i) => i && \"then\" in i)) {\n  console.error(\"[nuxt] Error in `vue:setup`. Callbacks must be synchronous.\");\n}\nconst error = useError();\nconst abortRender = import.meta.server && error.value && !nuxtApp.ssrContext.error;\nconst BOT_RE = /bot\\b|chrome-lighthouse|facebookexternalhit|google\\b/i;\nonErrorCaptured((err, target, info) => {\n  nuxtApp.hooks.callHook(\"vue:error\", err, target, info).catch((hookError) => console.error(\"[nuxt] Error in `vue:error` hook\", hookError));\n  if (import.meta.client && BOT_RE.test(navigator.userAgent)) {\n    nuxtApp.hooks.callHook(\"app:error\", err);\n    console.error(`[nuxt] Not rendering error page for bot with user agent \\`${navigator.userAgent}\\`:`, err);\n    return false;\n  }\n  if (import.meta.server || isNuxtError(err) && (err.fatal || err.unhandled)) {\n    const p = nuxtApp.runWithContext(() => showError(err));\n    onServerPrefetch(() => p);\n    return false;\n  }\n});\nconst islandContext = import.meta.server && nuxtApp.ssrContext.islandContext;\n</script>\n", "import { createApp, create<PERSON>RA<PERSON>, nextTick } from \"vue\";\nimport \"#build/fetch.mjs\";\nimport \"#build/global-polyfills.mjs\";\nimport { applyPlugins, createNuxtApp } from \"./nuxt.js\";\nimport { createError } from \"./composables/error.js\";\nimport \"#build/css\";\nimport plugins from \"#build/plugins\";\nimport RootComponent from \"#build/root-component.mjs\";\nimport { appId, appSpaLoaderAttrs, multiApp, spaLoadingTemplateOutside, vueAppRootContainer } from \"#build/nuxt.config.mjs\";\nlet entry;\nif (import.meta.server) {\n  entry = async function createNuxtAppServer(ssrContext) {\n    const vueApp = createApp(RootComponent);\n    const nuxt = createNuxtApp({ vueApp, ssrContext });\n    try {\n      await applyPlugins(nuxt, plugins);\n      await nuxt.hooks.callHook(\"app:created\", vueApp);\n    } catch (error) {\n      await nuxt.hooks.callHook(\"app:error\", error);\n      nuxt.payload.error ||= createError(error);\n    }\n    if (ssrContext?._renderResponse) {\n      throw new Error(\"skipping render\");\n    }\n    return vueApp;\n  };\n}\nif (import.meta.client) {\n  if (import.meta.dev && import.meta.webpackHot) {\n    import.meta.webpackHot.accept();\n  }\n  let vueAppPromise;\n  entry = async function initApp() {\n    if (vueAppPromise) {\n      return vueAppPromise;\n    }\n    const isSSR = Boolean(\n      (multiApp ? window.__NUXT__?.[appId] : window.__NUXT__)?.serverRendered ?? (multiApp ? document.querySelector(`[data-nuxt-data=\"${appId}\"]`) : document.getElementById(\"__NUXT_DATA__\"))?.dataset.ssr === \"true\"\n    );\n    const vueApp = isSSR ? createSSRApp(RootComponent) : createApp(RootComponent);\n    const nuxt = createNuxtApp({ vueApp });\n    async function handleVueError(error) {\n      await nuxt.callHook(\"app:error\", error);\n      nuxt.payload.error ||= createError(error);\n    }\n    vueApp.config.errorHandler = handleVueError;\n    nuxt.hook(\"app:suspense:resolve\", () => {\n      if (vueApp.config.errorHandler === handleVueError) {\n        vueApp.config.errorHandler = void 0;\n      }\n    });\n    if (spaLoadingTemplateOutside && !isSSR && appSpaLoaderAttrs.id) {\n      nuxt.hook(\"app:suspense:resolve\", () => {\n        document.getElementById(appSpaLoaderAttrs.id)?.remove();\n      });\n    }\n    try {\n      await applyPlugins(nuxt, plugins);\n    } catch (err) {\n      handleVueError(err);\n    }\n    try {\n      await nuxt.hooks.callHook(\"app:created\", vueApp);\n      await nuxt.hooks.callHook(\"app:beforeMount\", vueApp);\n      vueApp.mount(vueAppRootContainer);\n      await nuxt.hooks.callHook(\"app:mounted\", vueApp);\n      await nextTick();\n    } catch (err) {\n      handleVueError(err);\n    }\n    return vueApp;\n  };\n  vueAppPromise = entry().catch((error) => {\n    console.error(\"Error while mounting app:\", error);\n    throw error;\n  });\n}\nexport default (ssrContext) => entry(ssrContext);\n"], "names": ["plugin", "provide", "plugins", "_a", "createH3Error", "createRadixRouter", "index7CJl_pF_GfV6EXXXp4UTmBgqi9NLiBieHHomBD6BDfsMeta", "_91slug_935cKhrCBRPBVApek5crAj0apII21kYok7gFS8InRNIp0Meta", "__executeAsync", "createRouter", "_b", "_c", "entry", "headCore", "seoMeta", "router_GNCWhvtYfLTYRZZ135CdFAEjxdMexN0ixiUYCAN_tpw", "useVueRouterRoute", "defaultLayoutTransition", "_push", "_parent", "_createVNode", "_ssrRenderAttrs", "_mergeProps", "error", "_unref", "_ssrRenderComponent", "RootComponent"], "mappings": ";;;;;;;;;;;;AAEA,IAAI,CAAC,WAAW,QAAQ;AACtB,aAAW,SAAS,OAAO,OAAO;AAAA,IAChC,SAAS,QAAO;AAAA,EACpB,CAAG;AACH;ACLA,IAAI,EAAE,YAAY,aAAa;AAC7B,aAAW,SAAS;AACtB;ACKO,MAAM,sBAAsB;AA4C5B,MAAM,mBAAmB,EAAC,iBAAgB,WAA2D;AAErG,MAAM,oBAAoB,EAAC,SAAQ,MAAK,cAAa,MAAK,QAAO,KAAA;AAMjE,MAAM,gBAAgB,CAAA;AAMtB,MAAM,QAAQ;AC9Dd,SAAS,cAAc,KAAK,OAAqB;AACtD,SAAO,WAAW,IAAI;AAAA,IACpB,cAAc;AAAA,EAAA,CACf;AACH;AACO,MAAM,sBAAsB;AAC5B,SAAS,cAAc,SAAS;;AACrC,MAAI,iBAAiB;AACrB,QAAM,UAAU;AAAA,IACd,KAAK,QAAQ,MAAM,SAAS;AAAA,IAC5B,QAAQ,YAAA;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,UAAU;AAAA,MACR,IAAI,OAAO;AACT,eAAO;AAAA,MAAA;AAAA,MAET,IAAI,MAAM;AACR,eAAO,QAAQ,OAAO;AAAA,MAAA;AAAA,IACxB;AAAA,IAEF,SAAS,gBAAgB;AAAA,MACvB,KAAG,aAAQ,eAAR,mBAAoB,YAAW,CAAA;AAAA,MAClC,MAAM,gBAAgB,EAAE;AAAA,MACxB,OAAO,SAAS,EAAE;AAAA,MAClB,0BAA0B,IAAA;AAAA,MAC1B,SAAS,gBAAgB,CAAA,CAAE;AAAA,IAAA,CAC5B;AAAA,IACD,QAAQ;AAAA,MACN,MAAM,CAAA;AAAA,IAAC;AAAA,IAET,eAAe,IAAI;AACjB,UAAI,QAAQ,OAAO,UAAU,CAAC,mBAAmB;AAC/C,eAAO,QAAQ,OAAO,IAAI,MAAM,aAAa,SAAS,EAAE,CAAC;AAAA,MAAA;AAE3D,aAAO,aAAa,SAAS,EAAE;AAAA,IAAA;AAAA,IAEjC,aAAa;AAAA,IACb,iBAAiB;AACf,UAAI,CAAC,QAAQ,aAAa;AACxB,eAAO,MAAM;AAAA,QAAA;AAAA,MACb;AAEF;AACA,UAAI,SAAS;AACb,aAAO,MAAM;AACX,YAAI,QAAQ;AACV;AAAA,QAAA;AAEF,iBAAS;AACT;AACA,YAAI,mBAAmB,GAAG;AACxB,kBAAQ,cAAc;AACtB,iBAAO,QAAQ,SAAS,sBAAsB;AAAA,QAAA;AAAA,MAChD;AAAA,IACF;AAAA,IAEF,oBAAoB,CAAA;AAAA,IACpB,YAAY,gBAAgB,EAAE;AAAA,IAC9B,kBAAkB,CAAA;AAAA,IAClB,GAAG;AAAA,EAAA;AAEmB;AACtB,YAAQ,QAAQ,iBAAiB;AAAA,EAAA;AAEnC,MAA0B,QAAQ,YAAY;AAC5C,YAAQ,QAAQ,OAAO,QAAQ,WAAW;AAC1C,YAAQ,WAAW,OAAO;AAC1B,YAAQ,WAAW,UAAU,QAAQ;AACrC,YAAQ,WAAW,SAAS;AAAA,MAC1B,QAAQ,QAAQ,WAAW,cAAc;AAAA,MACzC,KAAK,QAAQ,WAAW,cAAc;AAAA,IAAA;AAAA,EACxC;AAkBF,UAAQ,QAAQ,YAAA;AAChB,UAAQ,OAAO,QAAQ,MAAM;AACL;AACtB,UAAM,gBAAgB,eAAe,OAAO,MAAM;AAChD,iBAAW,QAAQ,OAAO;AACxB,cAAM,QAAQ,eAAe,MAAM,KAAK,GAAG,IAAI,CAAC;AAAA,MAAA;AAAA,IAClD;AAEF,YAAQ,MAAM,WAAW,CAAC,SAAS,SAAS,QAAQ,MAAM,aAAa,eAAe,MAAM,GAAG,IAAI;AAAA,EAAA;AAErG,UAAQ,WAAW,QAAQ,MAAM;AACjC,UAAQ,UAAU,CAAC,MAAM,UAAU;AACjC,UAAM,QAAQ,MAAM;AACpB,iBAAa,SAAS,OAAO,KAAK;AAClC,iBAAa,QAAQ,OAAO,OAAO,kBAAkB,OAAO,KAAK;AAAA,EAAA;AAEnE,eAAa,QAAQ,QAAQ,SAAS,OAAO;AAC7C,eAAa,QAAQ,OAAO,OAAO,kBAAkB,SAAS,OAAO;AAgBrE,QAAM,gBAAqC,QAAQ,WAAW;AAC9D,UAAQ,QAAQ,UAAiF,aAAa;AAC9G,SAAO;AACT;AACO,SAAS,oBAAoB,SAASA,SAAQ;AACnD,MAAIA,QAAO,OAAO;AAChB,YAAQ,MAAM,SAASA,QAAO,KAAK;AAAA,EAAA;AAEvC;AACA,eAAsB,YAAY,SAASA,SAAQ;AACjD,MAAI,OAAOA,YAAW,YAAY;AAChC,UAAM,EAAE,SAAAC,SAAA,IAAY,MAAM,QAAQ,eAAe,MAAMD,QAAO,OAAO,CAAC,KAAK,CAAA;AAC3E,QAAIC,YAAW,OAAOA,aAAY,UAAU;AAC1C,iBAAW,OAAOA,UAAS;AACzB,gBAAQ,QAAQ,KAAKA,SAAQ,GAAG,CAAC;AAAA,MAAA;AAAA,IACnC;AAAA,EACF;AAEJ;AACA,eAAsB,aAAa,SAASC,UAAS;;AACnD,QAAM,sCAAsC,IAAA;AAC5C,QAAM,oBAAoB,CAAA;AAC1B,QAAM,YAAY,CAAA;AAClB,QAAM,SAAS,CAAA;AACf,MAAI,eAAe;AACnB,iBAAe,cAAcF,SAAQ;;AACnC,UAAM,mCAAiCG,MAAAH,QAAO,cAAP,gBAAAG,IAAkB,OAAO,CAAC,SAASD,SAAQ,KAAK,CAAC,MAAM,EAAE,UAAU,IAAI,KAAK,CAAC,gBAAgB,IAAI,IAAI,OAAM,CAAA;AAClJ,QAAI,+BAA+B,SAAS,GAAG;AAC7C,wBAAkB,KAAK,CAAC,IAAI,IAAI,8BAA8B,GAAGF,OAAM,CAAC;AAAA,IAAA,OACnE;AACL,YAAM,UAAU,YAAY,SAASA,OAAM,EAAE,KAAK,YAAY;AAC5D,YAAIA,QAAO,OAAO;AAChB,0BAAgB,IAAIA,QAAO,KAAK;AAChC,gBAAM,QAAQ,IAAI,kBAAkB,IAAI,OAAO,CAAC,WAAW,gBAAgB,MAAM;AAC/E,gBAAI,UAAU,IAAIA,QAAO,KAAK,GAAG;AAC/B,wBAAU,OAAOA,QAAO,KAAK;AAC7B,kBAAI,UAAU,SAAS,GAAG;AACxB;AACA,sBAAM,cAAc,gBAAgB;AAAA,cAAA;AAAA,YACtC;AAAA,UACF,CACD,CAAC;AAAA,QAAA;AAAA,MACJ,CACD;AACD,UAAIA,QAAO,UAAU;AACnB,kBAAU,KAAK,QAAQ,MAAM,CAAC,MAAM,OAAO,KAAK,CAAC,CAAC,CAAC;AAAA,MAAA,OAC9C;AACL,cAAM;AAAA,MAAA;AAAA,IACR;AAAA,EACF;AAEF,aAAWA,WAAUE,UAAS;AAC5B,UAA0B,aAAQ,eAAR,mBAAoB,oBAAiB,KAAAF,QAAO,QAAP,mBAAY,aAAY,OAAO;AAC5F;AAAA,IAAA;AAEF,wBAAoB,SAASA,OAAM;AAAA,EAAA;AAErC,aAAWA,WAAUE,UAAS;AAC5B,UAA0B,aAAQ,eAAR,mBAAoB,oBAAiB,KAAAF,QAAO,QAAP,mBAAY,aAAY,OAAO;AAC5F;AAAA,IAAA;AAEF,UAAM,cAAcA,OAAM;AAAA,EAAA;AAE5B,QAAM,QAAQ,IAAI,SAAS;AAC3B,MAAI,cAAc;AAChB,aAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,YAAM,QAAQ,IAAI,SAAS;AAAA,IAAA;AAAA,EAC7B;AAEF,MAAI,OAAO,QAAQ;AACjB,UAAM,OAAO,CAAC;AAAA,EAAA;AAElB;AAAA;AAEO,SAAS,iBAAiBA,SAAQ;AACvC,MAAI,OAAOA,YAAW,YAAY;AAChC,WAAOA;AAAA,EAAA;AAET,QAAM,QAAQA,QAAO,SAASA,QAAO;AACrC,SAAOA,QAAO;AACd,SAAO,OAAO,OAAOA,QAAO,UAAU,MAAM;AAAA,EAAA,IACxCA,SAAQ,EAAE,CAAC,mBAAmB,GAAG,MAAM,OAAO;AACpD;AAKO,SAAS,aAAa,MAAM,OAAO,MAAM;AAC9C,QAAM,KAAK,MAA8B,MAAA;AACzC,QAAM,aAAa,cAAc,KAAK,GAAG;AACjB;AACtB,WAAO,KAAK,OAAO,eAAe,MAAM,WAAW,UAAU,MAAM,EAAE,CAAC;AAAA,EAAA;AAK1E;AACO,SAAS,cAAc,IAAI;;AAChC,MAAI;AACJ,MAAI,uBAAuB;AACzB,uBAAkB,wBAAA,MAAA,mBAAsB,WAAW,IAAI;AAAA,EAAA;AAEzD,wCAAoB,cAAc,EAAE,EAAE,OAAA;AACtC,SAAO,mBAAmB;AAC5B;AACO,SAAS,WAAW,IAAI;AAC7B,QAAM,kBAAkB,cAAc,EAAE;AACxC,MAAI,CAAC,iBAAiB;AAGb;AACL,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAAA;AAAA,EAC/C;AAEF,SAAO;AACT;AAAA;AAEO,SAAS,iBAAiB,QAAQ;AACvC,SAAO,aAAa;AACtB;AACA,SAAS,aAAa,KAAK,KAAK,KAAK;AACnC,SAAO,eAAe,KAAK,KAAK,EAAE,KAAK,MAAM,KAAK;AACpD;ACzPO,MAAM,mBAAmB,OAAO,aAAa;AAC7C,MAAM,kBAAkB,OAAO,OAAO;ACKtC,MAAM,YAAY,MAAM;;AAC7B,UAAO,sBAAA,mBAAc;AACvB;AACO,MAAM,WAAW,MAAM;AAI5B,MAAI,uBAAuB;AACzB,WAAO,OAAO,iBAAiB,WAAA,EAAa,MAAM;AAAA,EAAA;AAEpD,SAAO,aAAa;AACtB;AAAA;AAeO,SAAS,0BAA0B,YAAY;AACpD,SAAO;AACT;AAeA,MAAM,yBAAyB,MAAM;AACnC,MAAI;AACF,QAAI,WAAA,EAAa,uBAAuB;AACtC,aAAO;AAAA,IAAA;AAAA,EACT,QACM;AACN,WAAO;AAAA,EAAA;AAET,SAAO;AACT;AACA,MAAM,eAAe;AACd,MAAM,aAAa,CAAC,IAAI,YAAY;AACzC,cAAO;AACP,QAAM,SAAS,OAAO,OAAO,WAAW,KAAK,UAAU,KAAK,mBAAmB,EAAE,IAAI,UAAA,EAAY,QAAQ,EAAE,EAAE;AAO7G,QAAM,iBAAiB,YAAY,QAAQ,EAAE,gBAAgB,MAAM;AACnE,QAAM,cAAa,mCAAS,aAAY;AACxC,MAAI,YAAY;AACd,QAAI,EAAC,mCAAS,WAAU;AACtB,YAAM,IAAI,MAAM,qGAAqG;AAAA,IAAA;AAEvH,UAAM,EAAE,SAAA,IAAa,IAAI,IAAI,QAAoD,kBAAkB;AACnG,QAAI,YAAY,iBAAiB,QAAQ,GAAG;AAC1C,YAAM,IAAI,MAAM,kCAAkC,QAAQ,aAAa;AAAA,IAAA;AAAA,EACzE;AAEF,QAAM,eAAe,uBAAA;AAgBrB,QAAM,SAAS,UAAA;AACf,QAAM,UAAU,WAAA;AACQ;AACtB,QAAI,QAAQ,YAAY;AACtB,YAAM,WAAW,OAAO,OAAO,YAAY,aAAa,SAAS,OAAO,QAAQ,EAAE,EAAE,YAAY;AAChG,YAAM,YAAY,aAAa,SAAS,SAAQ,oCAAmB,IAAI,SAAS,QAAQ;AACxF,YAAM,WAAW,eAAe,UAAU;AACxC,cAAM,QAAQ,SAAS,gBAAgB;AACvC,cAAM,aAAa,UAAU,QAAQ,cAAc,KAAK;AACxD,cAAM,gBAAgB,UAAU,WAAW,cAAc;AACzD,gBAAQ,WAAW,kBAAkB;AAAA,UACnC,YAAY,oBAAmB,mCAAS,iBAAgB,KAAK,GAAG;AAAA,UAChE,MAAM,yEAAyE,UAAU;AAAA,UACzF,SAAS,EAAE,UAAU,cAAA;AAAA,QAAc;AAErC,eAAO;AAAA,MAAA;AAET,UAAI,CAAC,cAAc,cAAc;AAC/B,eAAO,UAAU,CAAC,UAAU,MAAM,aAAa,WAAW,SAAS,KAAK,IAAI,MAAM;AAClF,eAAO;AAAA,MAAA;AAET,aAAO,SAAS,CAAC,eAAe;AAAA;AAAA,QAE9B;AAAA,OACD;AAAA,IAAA;AAAA,EACH;AAEF,MAAI,YAAY;AACd,YAAQ,OAAO,KAAA;AACf,QAAI,mCAAS,SAAS;AACpB,MAAA,SAAS,QAAQ,MAAM;AAAA,IAAA,OAClB;AACL,MAAA,SAAS,OAAO;AAAA,IAAA;AAElB,QAAI,cAAc;AAChB,UAAI,CAAC,QAAQ,aAAa;AACxB,eAAO;AAAA,MAAA;AAET,aAAO,IAAI,QAAQ,MAAM;AAAA,MAAA,CACxB;AAAA,IAAA;AAEH,WAAO,QAAQ,QAAA;AAAA,EAAQ;AAEzB,UAAO,mCAAS,WAAU,OAAO,QAAQ,EAAE,IAAI,OAAO,KAAK,EAAE;AAC/D;AAoCO,SAAS,mBAAmB,IAAI;AACrC,SAAO,UAAU,GAAG,QAAQ,IAAI,GAAG,SAAS,EAAE,KAAK,GAAG,QAAQ;AAChE;AACO,SAAS,UAAU,WAAW,iBAAiB,OAAO;AAC3D,QAAM,MAAM,IAAI,IAAI,WAAW,kBAAkB;AACjD,MAAI,CAAC,gBAAgB;AACnB,WAAO,IAAI,WAAW,IAAI,SAAS,IAAI;AAAA,EAAA;AAEzC,MAAI,UAAU,WAAW,IAAI,GAAG;AAC9B,WAAO,IAAI,SAAA,EAAW,QAAQ,IAAI,UAAU,EAAE;AAAA,EAAA;AAEhD,SAAO,IAAI,SAAA;AACb;ACvLO,MAAM,uBAAuB;AAC7B,MAAM,WAAW,MAAM,MAAM,WAAU,EAAG,SAAS,OAAO;AAC1D,MAAM,YAAY,CAAC,UAAU;AAClC,QAAM,YAAY,YAAY,KAAK;AACnC,MAAI;AACF,UAAM,UAAU,WAAU;AAC1B,UAAM,SAAS,SAAQ;AACvB,QAAI,MAAoB;AAGxB,WAAO,UAAP,OAAO,QAAU;AAAA,EACrB,QAAU;AACN,UAAM;AAAA,EACV;AACE,SAAO;AACT;AAUO,MAAM,cAAc,CAAC,UAAU,CAAC,CAAC,SAAS,OAAO,UAAU,YAAY,wBAAwB;AAC1F,MAAC,cAAc,CAAC,UAAU;AACpC,QAAM,YAAYI,cAAc,KAAK;AACrC,SAAO,eAAe,WAAW,sBAAsB;AAAA,IACrD,OAAO;AAAA,IACP,cAAc;AAAA,IACd,UAAU;AAAA,EACd,CAAG;AACD,SAAO;AACT;ACpCA,MAAA,qDAAe,iCAAiB;AAAA,EAC9B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM,SAAS;AACb,UAAM,OAA4B,QAAQ,WAAW;AACrD,YAAQ,OAAO,IAAI,IAAI;AAAA,EAqB3B;AACA,CAAC;ACfM,SAAS,QAAQ,OAAO;AAC7B,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;ACiBO,eAAe,cAAc,KAAK;AACvC,QAAM,OAAO,OAAO,QAAQ,WAAW,MAAM,IAAI;AACzB;AACtB,eAAU,EAAG,WAAW,mBAAmB;AAC3C,UAAM,qBAAqB;AAAA,MACzBC,aAAkB,EAAE,SAAQ,iCAAgB,GAAG,MAAM,WAAU,CAAE;AAAA,IACvE;AACI,WAAO,KAAK,CAAA,GAAI,GAAG,mBAAmB,SAAS,IAAI,EAAE,SAAS;AAAA,EAClE;AAYA;;;;;;;;;ACpBA,MAAA,UAAe;AAAA,EACb;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAMC,sBAAwD,CAAA;AAAA,IAC9D,WAAW,MAAM,OAAO,2BAA6C;AAAA,EAAA;AAAA,EAEvE;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAMC,oBAA6D,CAAA;AAAA,IACnE,WAAW,MAAM,OAAO,4BAA8C;AAAA,EAAA;AAE1E;AC5CO,MAAM,oBAAoB,CAAC,OAAO,aAAa;AACpD,SAAO,EAAE,SAAS;;AAA2F,0BAAS,YAAT;AAAA,IAAoB;AACnI;AACA,MAAM,2BAA2B;AACjC,MAAM,uBAAuB;AAC7B,MAAM,sBAAsB;AAC5B,SAAS,iBAAiB,OAAO;AAC/B,QAAM,UAAS,+BAAO,KAAK,QAAO,MAAM,KAAK,QAAQ,0BAA0B,IAAI,EAAE,QAAQ,sBAAsB,IAAI,EAAE,QAAQ,qBAAqB,CAAC,MAAC;;AAAK,wBAAM,OAAO,EAAE,MAAM,CAAC,CAAC,MAAvB,mBAA0B,eAAc;AAAA,GAAE;AACvM,SAAO,OAAO,WAAW,aAAa,OAAO,KAAK,IAAI;AACxD;AACO,SAAS,eAAe,IAAI,MAAM;AACvC,MAAI,OAAO,QAAQ,SAAS,gBAAgB;AAC1C,WAAO;AAAA,EACX;AACE,MAAI,iBAAiB,EAAE,MAAM,iBAAiB,IAAI,GAAG;AACnD,WAAO;AAAA,EACX;AACE,QAAM,oBAAoB,GAAG,QAAQ;AAAA,IACnC,CAAC,MAAM;;AAAU,kBAAK,cAAc,KAAK,WAAW,cAAY,gBAAK,QAAQ,KAAK,MAAlB,mBAAqB,eAArB,mBAAiC;AAAA;AAAA,EACrG;AACE,MAAI,mBAAmB;AACrB,WAAO;AAAA,EACX;AACE,SAAO;AACT;ACvBA,MAAA,iBAAe;AAAA,EACb,eAAe,IAAI,MAAM,eAAe;;AACtC,UAAM,UAAU,WAAA;AAChB,UAAM,aAAW,eAAA,EAAY,YAAZ,mBAAqB,uBAAsB;AAC5D,QAAI,GAAG,SAAS,KAAK,MAAM;AACzB,UAAI,KAAK,QAAQ,CAAC,GAAG,MAAM;AACzB,eAAO,EAAE,MAAM,GAAG,KAAK,EAAA;AAAA,MAAE;AAE3B,UAAI,GAAG,MAAM;AACX,eAAO,EAAE,IAAI,GAAG,MAAM,KAAK,+BAA+B,GAAG,IAAI,GAAG,SAAA;AAAA,MAAS;AAE/E,aAAO;AAAA,IAAA;AAET,UAAM,yBAAyB,OAAO,GAAG,KAAK,gBAAgB,aAAa,GAAG,KAAK,YAAY,IAAI,IAAI,IAAI,GAAG,KAAK;AACnH,QAAI,2BAA2B,OAAO;AACpC,aAAO;AAAA,IAAA;AAET,QAAI,WAAW,iBAAiB;AAChC,QAAI,CAAC,YAAY,eAAe,IAAI,IAAI,GAAG;AACzC,iBAAW,EAAE,MAAM,GAAG,KAAK,EAAA;AAAA,IAAE;AAE/B,UAAM,aAAa,QAAQ,qBAAqB,2BAA2B;AAC3E,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,UAAI,SAAS,gBAAgB;AAC3B,gBAAQ,mBAAmB,IAAI,WAAW,QAAQ,CAAC;AACnD;AAAA,MAAA;AAEF,cAAQ,MAAM,SAAS,YAAY,MAAM;AACvC,8BAAsB,MAAM,QAAQ,mBAAmB,IAAI,WAAW,QAAQ,CAAC,CAAC;AAAA,MAAA,CACjF;AAAA,IAAA,CACF;AAAA,EAAA;AAEL;AACA,SAAS,+BAA+B,UAAU;AAChD,MAAI;AACF,UAAM,OAAO,SAAS,cAAc,QAAQ;AAC5C,QAAI,MAAM;AACR,cAAQ,OAAO,WAAW,iBAAiB,IAAI,EAAE,eAAe,KAAK,MAAM,OAAO,WAAW,iBAAiB,SAAS,eAAe,EAAE,gBAAgB,KAAK;AAAA,IAAA;AAAA,EAC/J,QACM;AAAA,EAAA;AAER,SAAO;AACT;AACA,SAAS,mBAAmB,IAAI,oBAAoB,UAAU;AAC5D,MAAI,UAAU;AACZ,WAAO;AAAA,EAAA;AAET,MAAI,GAAG,MAAM;AACX,WAAO;AAAA,MACL,IAAI,GAAG;AAAA,MACP,KAAK,+BAA+B,GAAG,IAAI;AAAA,MAC3C,UAAU;AAAA,IAAA;AAAA,EACZ;AAEF,SAAO,EAAE,MAAM,GAAG,KAAK,GAAG,UAAU,mBAAA;AACtC;AC1DA,MAAM,sBAAsB;AAAA,EAC1B,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,oBAAoB;AACtB;AAEA,MAAA,gBAAe;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AACH;ACPE,MAAA,WAAQ,0CAAgB,OAAA,IAAA,SAAA;;AAAA,MAAA,QAAA;AACtB,MAAA,GAAA,QAAA,SAAA,mBAAA,WAAA;AACF;AAAA,EACA;AACA,kBAAe,CAAA,QAAM,SAAA,IAAAC,aAAA,MAAA,QAAA,QAAA,GAAA,KAAA,SAAA,EAAA,CAAA,CAAA,GAAA,SAAA,MAAA,QAAA,UAAA,GAAA;AACnB,MAAA,WAAA,MAAA;AACF;AAAA,EACA;AAA0B,QACxB,QAAO,YAAA;AAAA,IACP,OAAA;AAAA,IACA,sBAAe,OAAU,cAAO;AAAA,IAChC,eAAM,UAAA,OAAA,iBAAA,mBAAA,GAAA,QAAA;AAAA,IAAA;MAEN,MAAA,GAAA;AAAA,IAAA;AAAA,EAEF,CAAA;AAIF,SAAC;;AClBC,MAAA,0BAA4C,0CAAA,OAAA,OAAA;AAC1C;AACF;AAAA,EACA;;ACLK,MAAM,mBAAmB;AAAA,EAC9B;AAAA,EACA;AACF;AACO,MAAM,kBAAkB,CAAA;ACqBC,MAC9B,SAAM,iCAAA;AAAA,EACN,MAAA;AAAA,EACA;EACE,MAAI,MAAA,SAAa;;AAAA,QAAA,QAAA;AACjB,QAAI,cAAa,oCAAW,IAAY;AAIxC,UAAM,+BAAuB,mDAAA,gBAA2C,oBAAK,UAAU;AACvF,UAAI,SAAA,cAAA,UAAA,CAAA,QAAA,SAAA,IAAAA,aAAA,MAAA,cAAA,OAAA,OAAA,CAAA,GAAA,SAAA,MAAA,QAAA,UAAA,GAAA,WAAA,UAAA;AACJ,QAAA;AAA4B,UACvB,SAAAC,eAAA;AAAA,MACH,GAAA;AAAA,MACE,gBAAa,CAAA,IAAA,MAAA,kBAAgB;AAC3B,YAAA,SAAA,gBAAgB;AAChB,0BAAA;AACF;AAAA,QACA;AACE,YAAA,8BAAgC;AAChC,iBAAI,QAAA,+BAAuC;AACzC,cAAA,uBAAqB,SAAW,SAAM;AACpC,kBAAA,QAAM,OAAA,WAAA,MAAA;AACN,oBAAA;AACD,cAAA,SAAA,QAAA,oBAAA;AAAA,YAAA,CACH;AAAA,UACA;AACF,iBAAA,cAAA,eAAA,IAAA,gBAAA,iBAAA,aAAA;AAAA,QAAA;AAAA,MACF;AAAA,MAEA;AAAA,MACD;AAAA,IACD,CAAA;AAOA,YAAM,OAAA,IAAA,MAAgB;AACtB,UAAA,gBAAkB,WAAc,OAAA,aAAA,KAAA;AAC9B,WAAA,UAAc,CAAA,KAAA,SAAQ;AACvB,oBAAA,QAAA;AAAA,IACD,CAAA;AAA+E,WACxE,eAAM,QAAc,OAAA,OAAA,kBAAA,iBAAA;AAAA,MAC1B,KAAA,MAAA,cAAA;AAAA,IACD,CAAA;AACA,UAAM,aAAoB,QAAO,WAAA;AACjC,UAAM,oBAAmB,OAAM,aAAA,KAAA;AAC7B,UAAA,mBAAsB,MAAA;AACxB,aAAA,QAAA,OAAA,aAAA;AAAA,IACA;AACA,YAAO,KAAA,eAAe,gBAAS;AAC7B,WAAI,UAAW,CAAC,IAAG,SAAA;;AACjB,YAAAC,OAAAP,MAAA,GAAA,QAAA,CAAA,MAAA,gBAAAA,IAAiB,eAAjB,gBAAAO,IAAiB,eAAA,MAAAC,MAAA,KAAA,QAAA,CAAA,MAAA,gBAAAA,IAAA,eAAA,mBAAA,UAAA;AACnB,yBAAA;AAAA,MAAA;AAAA,IAEF,CAAA;AACA,UAAA,QAAW,CAAA;AACT,eAAO,OAAA,cAAsB;AAAK,aAC3B,eAAa,OAAS,KAAA;AAAA,QAC3B,KAAA,MAAA,OAAY,MAAA,GAAA;AAAA,QACb,YAAA;AAAA,MAAA,CACH;AAAA,IACA;AACA,YAAQ,SAAA,gBAAgB,KAAA;AAAA,4BAAA,sBACb;AAAA,MACT,QAAQ,CAAA;AAAA,MACV,OAAA,CAAA;AAAA,IACA;AACmC,aAAA;AACjC,QAAA,GAAA,aAAO,eAAP,mBAAiB,gBAAkB;AACjC,aAAA,UAAe,OAAA,IAAA,OAAA,YAAA;AACf,eAAI,QAA2D;AAI7D,YAAA,SAAM;AACR,gBAAA,QAAA,SAAA,kBAAA;AAAA,QACA;AACE,aAAA,mCAAA,UAAA,GAAA;AACF;AAAA,QACA;AACE,YAAA,GAAA,kBAAc,GAAA,yBAAmC;AACnD,gBAAA,QAAA,eAAA,MAAA,WAAA,GAAA,YAAA,GAAA,CAAA;AAAA,QAAA;AAAA,MACD,CACH;AAAA,IACA;AACE,QAAA;AACE,UAAA,MAAA;AACF;AAAA,QAAA,CAAA,QAAA,SAAA,IAAAH,aAAA,MAAA,OAAA,KAAA,UAAA,CAAA,GAAA,MAAA,QAAA,UAAA;AAAA;AAAA,MACA;;eACO,SAAQ,IAAAA,aAAA,MAAA,OAAA,QAAA,CAAA,GAAA,MAAA,QAAA,UAAA;AAAA;AAAA,IACf;AACF,MAAA,CAAA,QAAA,SAAA,IAAAA,aAAA,MAAA,QAAA,eAAA,MAAA,UAAA,MAAA,CAAA,CAAA,GAAA,MAAA,QAAA,UAAA;AAAA,IACA;AACA,UAAA,uBAAiB,OAAA,aAAA;AACjB;AACE,SAAA,aAAS,eAAT,mBAAoB,eAAS;AAC/B,aAAA,EAAA,SAAA,EAAA,SAAA;AAAA,IACA;AACA,UAAA,gBAAkB,QAAW,QAAS,MAAA;AACpC,WAAA,kBAAuB,IAAA,SAAA;;AACvB,YAAG,QAAO,SAAY,oBAAI;AAC1B,SAAA,OAAI;AACF,UAAA,QAAQ,eAAS,iBAAA,CAAA,WAAA,GAAA,KAAA,MAAA,GAAA;AACnB,WAAA,KAAA,SAAA;AAAA,MACA;AACA,cAA2B,wBAAoB;AAC7C,UAAA,GAAAL,MAAA,QAAM,eAAN,gBAAAA,IAAM,gBAAoC;AAC1C,cAAA,oBAAoC,oBAAA,IAAA,CAAA,GAAA,kBAAA,GAAA,QAAA,YAAA,MAAA,CAAA;AAClC,mBAAM,aAAA,GAAA;AACN,gBAAK,sBAAqB,UAAA,KAAA;AACxB,cAAA,CAAA,qBAAA;AACF;AAAA,UACA;AACE,qBAAAS,UAAkB,QAAI,mBAAK,GAAA;AAC7B,8BAAA,IAAAA,MAAA;AAAA,UAAA;AAAA,QAEF;AACqB;AACnB,6BAAe,MAAA,QAAe,eAAA,MAAA,cAAA,EAAA,MAAA,GAAA,KAAA,CAAA,CAAA;AAC5B,cAAA,WAAW;AACT,uBAAI,OAAW,WAAA,eAAoB;AACjC,kBAAA,WAAA,cAAyB,GAAA,GAAA;AAC3B,kCAAO,IAAA,GAAA;AAAA,cACL,OAAA;AACF,kCAAA,OAAA,GAAA;AAAA,cAAA;AAAA,YACF;AAAA,UACF;AAAA,QAEF;AACE,mBAAMA,UAAA,mBAA8B;AACpC,gBAAK,aAAY,OAAAA,WAAA,WAAA,QAAA,YAAA,MAAAA,MAAA,KAAA,QAAAF,MAAA,gBAAAE,YAAA,gBAAAF,IAAA,sBAAA,KAAA,CAAA,MAAA,EAAA,WAAA,MAAAE;AACf,cAAA,CAAA,YAAqB;AAIvB,kBAAA,IAAA,MAAA,8BAAAA,MAAA,IAAA;AAAA,UACA;AACE,cAAA;AACA,kBAAI,SAA8E,MAAA,QAAA,eAAA,MAAA,WAAA,IAAA,IAAA,CAAA;AAChF,gBAAA,MAAI;AACF,kBAAA,WAAM,2BAA+B,OAAA;AAAA,sBACnC,SAAY,UAAA,YAAA;AAAA,kBACZ,YAAA;AAAA,kBACD,eAAA,mBAAA,UAAA;AAAA,gBACD,CAAA;AACA,sBAAA,QAAO,eAAA,MAAA,UAAA,MAAA,CAAA;AACT,uBAAA;AAAA,cAAA;AAAA,YAEF;AACE,gBAAA,WAAA,MAAA;AACF;AAAA,YACA;AACE,gBAAA,WAAO,OAAA;AACT,qBAAA;AAAA,YACA;AACE,gBAAA,QAAI;AACF,kBAAA,YAAc,MAAA,KAAA,OAAe,OAAM;AACrC,sBAAA,QAAA,eAAA,MAAA,UAAA,MAAA,CAAA;AAAA,cACA;AACF,qBAAA;AAAA,YAAA;AAAA,UAEA,SAAM,KAAA;AACN,2BAAW,YAAO,GAAA;AAChB,gBAAA,OAAM,OAAQ;AAChB,oBAAA,QAAA,eAAA,MAAA,UAAA,MAAA,CAAA;AAAA,YACA;AACF,mBAAA;AAAA,UAAA;AAAA,QACF;AAAA,MACF;AAAA,IAEF,CAAA;AACE,WAAA,QAAO,YAAQ;AACf,aAAM;AACP,YAAA,QAAA,SAAA,kBAAA;AAAA,IACD,CAAA;AACE,WAAI,UAAW,OAAA,IAAW,UAAG;AAC3B,UAAA,GAAA,QAAM,WAAQ,GAAA;AAA2C,cACvD,QAAY,eAAA,MAAA,UAAA,YAAA;AAAA,UACZ,YAAO;AAAA,UACP,OAAA;AAAA,UACA,eAAM,mBAAA,GAAA,QAAA;AAAA,UAAA;YAEN,MAAA,GAAA;AAAA,UAAA;AAAA,QACC,CACL,CAAA,CAAA;AAAA,MAAA;AAAA,IAEF,CAAA;AACE,YAAI,MAAA,SAAA,eAAA,YAAA;AACF,UAAA;AACE,YAAA,UAAA,sBAA4B;AAC9B,+BAAA,OAAA;AAAA,QACA;AAAqB,cAChB,OAAA,QAAA;AAAA,UACH,GAAA;AAAA,UACD,OAAA;AAAA,QACD,CAAA;eACO,QAAQ,iBAAA,cAAA;AAAA,MACf,SAAM,QAAQ;AAChB,cAAA,QAAA,eAAA,MAAA,UAAA,MAAA,CAAA;AAAA,MAAA;AAAA,IAEF,CAAA;AACF,WAAA,EAAA,SAAA,EAAA,SAAA;AAAA,EACF;AACA,CAAA;AC9NO,SAAS,WAAW,SAAS;;AAClC,QAAM,OAAO,WAAW,cAAa;AACrC,WAAO,kCAAM,eAAN,mBAAkB,UAAQ,6BAAM,eAAe,MAAM;AAC1D,QAAI,oBAAmB,GAAI;AACzB,aAAO,OAAO,UAAU;AAAA,IAC9B;AAAA,EACA;AACA;AACO,SAAS,QAAQ,OAAO,UAAU,IAAI;AAC3C,QAAM,OAAO,WAAW,QAAQ,IAAI;AACpC,MAAI,MAAM;AACR,WAAOC,UAAS,OAAO,EAAE,MAAM,GAAG,QAAO,CAAE;AAAA,EAC/C;AACA;AAOO,SAAS,WAAW,OAAO,UAAU,IAAI;AAC9C,QAAM,OAAO,WAAW,QAAQ,IAAI;AACpC,MAAI,MAAM;AACR,WAAOC,aAAQ,OAAO,EAAE,MAAM,GAAG,QAAO,CAAE;AAAA,EAC9C;AACA;ACwFO,SAAS,qBAAqB,MAAM,QAAQ;AACzB;AACtB,eAAA,EAAa,WAAW,iBAAiB,IAAI,IAAI;AAAA,EAAA;AAErD;AC3HA,MAAM,WAAW;AAAA,EACf,CAAC,aAAa,CAAC,SAAS,YAAY,IAAI,KAAK,KAAK,QAAQ;AAAA,EAC1D,CAAC,mBAAmB,CAAC,SAAS,MAAM,IAAI,KAAK,UAAU,IAAI,KAAK,CAAC,KAAK,UAAU,OAAO,KAAK,UAAU,WAAW,OAAO,KAAK,UAAU,KAAK,KAAK,KAAK,IAAI;AAAA,EAC1J,CAAC,YAAY,CAAC,SAAS,MAAM,IAAI,KAAK,CAAC,KAAK,UAAU,OAAO,KAAK,UAAU,WAAW,OAAO,KAAK,UAAU,KAAK,KAAK,KAAK,IAAI;AAAA,EAChI,CAAC,cAAc,CAAC,SAAS,MAAM,IAAI,KAAK,UAAU,IAAI,KAAK,KAAK,KAAK;AAAA,EACrE,CAAC,mBAAmB,CAAC,SAAS,WAAW,IAAI,KAAK,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC;AAAA,EAChF,CAAC,OAAO,CAAC,SAAS,MAAM,IAAI,KAAK,KAAK,KAAK;AAAA,EAC3C,CAAC,YAAY,CAAC,SAAS,WAAW,IAAI,KAAK,MAAM,IAAI,CAAC;AACxD;AAIA,MAAA,oEAAe,iCAAiB;AAAA,EAC9B,MAAM;AAAA,EACN,QAAQ;AACN,eAAW,CAAC,SAAS,EAAE,KAAK,UAAU;AACpC,2BAAqB,SAAS,EAAE;AAAA,IACtC;AAAA,EACA;AACA,CAAC;ACtBD,MAAA,gEAAe,iCAAiB;AAAA,EAC9B,MAAM;AACR,CAAC;ACJM,MAAM,aAAa;ACG1B,MAAM,oBAAoB;AACnB,SAAS,YAAY,MAAM;AAChC,QAAM,UAAU,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,WAAW,KAAK,IAAG,IAAK;AACzE,MAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,SAAK,QAAQ,OAAO;AAAA,EACxB;AACE,QAAM,CAAC,MAAM,IAAI,IAAI;AACrB,MAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrC,UAAM,IAAI,UAAU,6CAA6C,IAAI;AAAA,EACzE;AACE,MAAI,SAAS,UAAU,OAAO,SAAS,YAAY;AACjD,UAAM,IAAI,MAAM,gDAAgD,IAAI;AAAA,EACxE;AACE,QAAM,MAAM,oBAAoB;AAChC,QAAM,UAAU,WAAU;AAC1B,QAAM,QAAQ,MAAM,QAAQ,QAAQ,OAAO,GAAG;AAC9C,MAAI,MAAM,UAAU,UAAU,MAAM;AAClC,UAAM,eAAe,KAAI;AACzB,QAAI,MAAM,YAAY,GAAG;AACvB,cAAQ,QAAQ,MAAM,GAAG,IAAI;AAC7B,aAAO;AAAA,IACb;AACI,UAAM,QAAQ;AAAA,EAClB;AACE,SAAO;AACT;ACfA,MAAA,4DAAe,iCAAiB,CAAC,YAAY;;AAC3C,QAAM,cAAY,aAAQ,eAAR,mBAAoB,iBAAgB,IAAI,CAAA,CAAE,IAAI,SAAS,cAAc,MAAM,SAAS;AAAA,IACpG;AAAA,IACA,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ,CAAG,CAAC,EAAE;AACJ,QAAM,YAAY,CAAA;AAgBN;AAQV,YAAQ,EAAE,WAAW;AAAA,EACzB;AACE,YAAS,EAAG,UAAU,CAAC,OAAO;AAC5B,UAAM,kBAAmF,GAAG,KAAK;AACjG,QAAI,mBAAmB,oBAAoB,UAAU;AACnD,gBAAU,QAAQ,UAAU,wBAAwB,IAAI;AAIxD,gBAAU,SAAS;AAAA,IACzB,WAAe,oBAAoB,UAAU;AACvC,cAAQ,KAAK,6DAA6D;AAAA,IAChF;AAAA,EACA,CAAG;AACD,UAAQ,QAAQ,aAAa,SAAS;AACxC,CAAC;ACtDD,MAAA,UAAe;AAAA,EACb;AAAA,EACAC;AAAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;ACVA,MAAA,UAAe;AAAA,EACb,SAAS,qBAAqB,MAAM,OAAO,6BAAiD,EAAE,KAAK,OAAK,EAAE,WAAW,CAAC,CAAC;AACzH;ACKA,MAAM,eAAe,gBAAgB;AAAA,EACnC,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACE,MAAM,OAAO,SAAS;AACpB,WAAO,MAAM,EAAE,QAAQ,MAAM,IAAI,GAAG,MAAM,aAAa,QAAQ,KAAK;AAAA,EACxE;AACA,CAAC;AACD,MAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,IACJ,MAAM,CAAC,QAAQ,SAAS,MAAM;AAAA,IAC9B,SAAS;AAAA,EACb;AAAA,EACE,UAAU;AAAA,IACR,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,SAAS;AAAA,EACb;AACA;AACA,MAAA,uBAAe,gBAAgB;AAAA,EAC7B,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,EACP,MAAM,OAAO,SAAS;AACpB,UAAM,UAAU,WAAU;AAC1B,UAAM,gBAAgB,OAAO,eAAe;AAC5C,UAAM,sBAAsB,CAAC,iBAAiB,kBAAkB,SAAQ;AACxE,UAAM,QAAQ,sBAAsBC,WAAiB,IAAK;AAC1D,UAAM,SAAS,SAAS,MAAM;AAC5B,UAAI,UAAU,MAAM,MAAM,IAAI,MAAK,+BAAO,KAAK,WAAU;AACzD,UAAI,WAAW,EAAE,WAAW,UAAU;AAIpC,YAAI,MAAM,UAAU;AAClB,oBAAU,MAAM,MAAM,QAAQ;AAAA,QACxC;AAAA,MACA;AACM,aAAO;AAAA,IACb,CAAK;AACD,UAAM,YAAY,WAAU;AAC5B,YAAQ,OAAO,EAAE,WAAW;AAC5B,UAAM,OAAO,QAAQ,eAAc;AAQnC,QAAI;AACJ,WAAO,MAAM;AACX,YAAM,YAAY,OAAO,SAAS,OAAO,SAAS;AAClD,YAAM,mBAAkB,+BAAO,KAAK,qBAAoBC;AACxD,YAAM,2BAA2B;AACjC,mBAAa,OAAO;AACpB,aAAO,kBAAkB,aAAa,iBAAiB;AAAA,QACrD,SAAS,MAAM,EAAE,UAAU,EAAE,aAAa,MAAM,WAAW,MAAM;AAC/D,mBAAS,IAAI;AAAA,QACvB,KAAa;AAAA,UACH,SAAS,MAAM;AAAA,YACb;AAAA,YACA;AAAA,cACE,aAAa,WAAW,QAAQ,OAAO,EAAE,KAAK,WAAW;AAAA,cACzD,KAAK,OAAO,SAAS;AAAA,cACrB,MAAM,OAAO;AAAA,cACb,eAAe,CAAC,MAAM;AAAA,cACtB,sBAAsB,CAAC,SAAS;AAC9B,uBAAO,SAAS,4BAA4B,SAAS,OAAO;AAAA,cAC5E;AAAA,cACc,eAAe,CAAC,CAAC;AAAA,YAC/B;AAAA,YACY,QAAQ;AAAA,UACpB;AAAA,QACA,CAAS;AAAA,MACT,CAAO,EAAE,QAAO;AAAA,IAChB;AAAA,EACA;AACA,CAAC;AACD,MAAM,iBAAiB,gBAAgB;AAAA,EACrC,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM,CAAC,QAAQ,OAAO;AAAA,IAC5B;AAAA,IACI,aAAa;AAAA,MACX,MAAM;AAAA,IACZ;AAAA,IACI,eAAe;AAAA,MACb,MAAM;AAAA,IACZ;AAAA,IACI,eAAe;AAAA,MACb,MAAM;AAAA,IACZ;AAAA,IACI,sBAAsB;AAAA,MACpB,MAAM;AAAA,MACN,UAAU;AAAA,IAChB;AAAA,EACA;AAAA,EACE,MAAM,OAAO,SAAS;AACpB,UAAM,OAAO,MAAM;AACnB,QAAI,MAAM,eAAe;AACvB,cAAQ,kBAAkB;AAAA,QACxB,WAAW,CAAC,UAAU,UAAU,MAAM,KAAK,UAAU;AAAA,MAC7D,CAAO;AAAA,IACP;AACI,UAAM,gBAAgB,OAAO,eAAe;AAC5C,UAAM,sBAAsB,iBAAiB,kBAAkB,SAAQ;AACvE,QAAI,qBAAqB;AACvB,YAAM,iBAAiBD,WAAiB;AACxC,YAAM,qBAAqB,CAAA;AAC3B,iBAAW,QAAQ,gBAAgB;AACjC,cAAM,MAAM;AACZ,eAAO,eAAe,oBAAoB,KAAK;AAAA,UAC7C,YAAY;AAAA,UACZ,KAAK,MAAM;AACT,mBAAO,MAAM,qBAAqB,MAAM,IAAI,IAAI,eAAe,GAAG,IAAI,cAAc,GAAG;AAAA,UACnG;AAAA,QACA,CAAS;AAAA,MACT;AACM,cAAQ,iBAAiB,gBAAgB,kBAAkB,CAAC;AAAA,IAClE;AAeI,WAAO,MAAM;;AACX,UAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,EAAE,QAAQ,UAAU;AAK3D,gBAAO,mBAAQ,OAAM,YAAd;AAAA,MACf;AASM,aAAO;AAAA,QACL;AAAA,QACA,EAAE,KAAK,MAAM,aAAa,MAAM,aAAa,KAAI;AAAA,QACjD,QAAQ;AAAA,MAChB;AAAA,IACA;AAAA,EACA;AACA,CAAC;ACxKM,MAAM,sBAAsB,CAAC,OAAO,oBAAoB,gBAAgB;AAAA,EAC7E;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IAChB;AAAA,IACI,OAAO;AAAA,IACP,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,EACpB;AAAA,EACE,MAAM,OAAO;AACX,UAAM,cAAc,MAAM;AAC1B,UAAM,gBAAgB,MAAM;AAC5B,UAAM,QAAQ,CAAA;AACd,eAAW,OAAO,MAAM,OAAO;AAC7B,aAAO,eAAe,OAAO,KAAK;AAAA,QAChC,KAAK,MAAM,gBAAgB,MAAM,YAAY,MAAM,MAAM,GAAG,IAAI,cAAc,GAAG;AAAA,QACjF,YAAY;AAAA,MACpB,CAAO;AAAA,IACP;AACI,YAAQ,iBAAiB,gBAAgB,KAAK,CAAC;AAY/C,WAAO,MAAM;AACX,UAAI,CAAC,MAAM,OAAO;AAChB,eAAO,MAAM;AAAA,MACrB;AAKM,aAAO,EAAE,MAAM,OAAO,EAAE,KAAK,MAAM,UAAU;AAAA,IACnD;AAAA,EACA;AACA,CAAC;AACM,MAAM,gBAAgB,oBAAmB;ACrChD,MAAA,qBAAe,gBAAgB;AAAA,EAC7B,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,IACZ;AAAA,IACI,YAAY;AAAA,MACV,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACf;AAAA,IACI,WAAW;AAAA,MACT,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACf;AAAA,IACI,OAAO;AAAA,MACL,MAAM;AAAA,IACZ;AAAA,IACI,SAAS;AAAA,MACP,MAAM,CAAC,UAAU,MAAM;AAAA,MACvB,SAAS;AAAA,IACf;AAAA,EACA;AAAA,EACE,MAAM,OAAO,EAAE,OAAO,OAAO,OAAM,GAAI;AACrC,UAAM,UAAU,WAAU;AAC1B,UAAM,UAAU,IAAG;AACD,WAAO,iBAAiB,IAAI;AAE9C,WAAO,EAAE,SAAS;AACE,WAAO,kBAAkB,IAAI;AAEpC,YAAQ,eAAc;AAwBnC,WAAO,MAAM;AACX,aAAO,EAAE,YAAY,EAAE,MAAM,MAAM,MAAM,OAAO,MAAM,OAAO,GAAG,MAAK,GAAI;AAAA,QACvE,SAA8B,CAAC,eAAe;AAC5C,iBAAO,EAAE,UAAU,EAAE,aAAa,KAAI,GAAI;AAAA,YACxC,UAAU;AACR,qBAAO,EAAE,eAAe;AAAA,gBACtB,OAAO,MAAM,UAAU,cAAc,MAAM,SAAS,UAAU,IAAI,WAAW;AAAA,gBAC7E,OAAO,WAAW;AAAA,gBAClB,UAAU;AAAA,cAC1B,CAAe;AAAA,YACf;AAAA,UACA,CAAW;AAAA,QACX;AAAA,MAsFA,CAAO;AAAA,IACP;AAAA,EACA;AACA,CAAC;AA2BD,SAAS,cAAc,MAAM,MAAM;AACjC,QAAM,cAAc,KAAK,IAAI;AAC7B,SAAO,YAAY,WAAW,IAAI,EAAE,YAAY,CAAC,CAAC,IAAI,EAAE,UAAU,QAAQ,WAAW;AACvF;;;;;AC3LA,YAAQ;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,MAAA;AAAA,IACR,CACD;;;;;;yBAZG,CAEa,GAAAE,QAAAC,UAAA,aAAA;;;;;cADXC,YAAY,mBAAA;AAAA,YAAA;AAAA;;;;;;;;;;;;;;ACMlB,MAAM,oBAAoB,IAAI,SAAS,KAAK,KAAK,CAAC,QAAQ,QAAQ,MAAM;AAAA;AAGjE,SAAS,eAAe,SAAS;AACtC,QAAM,gBAAgB,QAAQ,iBAAiB;AAM/C,WAAS,0BAA0B,MAAM;AACvC,WAAoB,OAAO,SAAS,YAAY,KAAK,WAAW,GAAG;AAAA,EAAA;AAErE,WAAS,6BAA6B,IAAI,SAAS,eAAe;AAChE,UAAM,yBAAyB,iBAAiB,QAAQ;AACxD,QAAI,CAAC,MAAM,2BAA2B,YAAY,2BAA2B,UAAU;AACrF,aAAO;AAAA,IAAA;AAET,QAAI,OAAO,OAAO,UAAU;AAC1B,aAAO,2BAA2B,IAAI,sBAAsB;AAAA,IAAA;AAE9D,UAAM,OAAO,UAAU,MAAM,GAAG,SAAS,SAAS,GAAG,OAAO,QAAQ,EAAE,EAAE;AACxE,UAAM,eAAe;AAAA,MACnB,GAAG;AAAA,MACH,MAAM;AAAA;AAAA,MAEN,MAAM,2BAA2B,MAAM,sBAAsB;AAAA,IAAA;AAE/D,WAAO;AAAA,EAAA;AAET,WAAS,YAAY,OAAO;AAC1B,UAAM,SAAS,UAAA;AACf,UAAM,SAAS,iCAAA;AACf,UAAM,YAAY,SAAS,MAAM,CAAC,CAAC,MAAM,UAAU,MAAM,WAAW,OAAO;AAC3E,UAAM,gBAAgB,SAAS,MAAM;AACnC,YAAM,OAAO,MAAM,MAAM,MAAM,QAAQ;AACvC,aAAO,OAAO,SAAS,YAAY,YAAY,MAAM,EAAE,gBAAgB,MAAM;AAAA,IAAA,CAC9E;AACD,UAAM,oBAAoB,iBAAiB,YAAY;AACvD,UAAM,iBAAiB,qBAAqB,OAAO,sBAAsB,WAAW,kBAAkB,UAAU;AAChH,UAAM,aAAa,SAAS,MAAM;AAChC,UAAI,MAAM,UAAU;AAClB,eAAO;AAAA,MAAA;AAET,YAAM,OAAO,MAAM,MAAM,MAAM,QAAQ;AACvC,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO;AAAA,MAAA;AAET,aAAO,SAAS,MAAM,cAAc;AAAA,IAAA,CACrC;AACD,UAAM,KAAK,SAAS,MAAM;AAExB,YAAM,OAAO,MAAM,MAAM,MAAM,QAAQ;AACvC,UAAI,WAAW,OAAO;AACpB,eAAO;AAAA,MAAA;AAET,aAAO,6BAA6B,MAAM,OAAO,SAAS,MAAM,aAAa;AAAA,IAAA,CAC9E;AACD,UAAM,OAAO,WAAW,QAAQ,SAAS,iDAAiB,EAAE,GAAG,OAAO;AACtE,UAAM,OAAO,SAAS,MAAM;;AAC1B,YAAM,yBAAyB,MAAM,iBAAiB,QAAQ;AAC9D,UAAI,CAAC,GAAG,SAAS,cAAc,SAAS,0BAA0B,GAAG,KAAK,GAAG;AAC3E,eAAO,GAAG;AAAA,MAAA;AAEZ,UAAI,WAAW,OAAO;AACpB,cAAM,OAAO,OAAO,GAAG,UAAU,YAAY,UAAU,GAAG,QAAQ,mBAAmB,GAAG,KAAK,IAAI,GAAG;AACpG,cAAM,QAAQ,OAAO,SAAS,WAAW,OAAO,QAAQ,IAAI,EAAE,OAAO;AACrE,eAAO,2BAA2B,OAAO,sBAAsB;AAAA,MAAA;AAEjE,UAAI,OAAO,GAAG,UAAU,UAAU;AAChC,iBAAO,YAAO,QAAQ,GAAG,KAAK,MAAvB,mBAA0B,SAAQ;AAAA,MAAA;AAE3C,aAAO,2BAA2B,QAAQ,OAAO,IAAI,SAAS,GAAG,KAAK,GAAG,sBAAsB;AAAA,IAAA,CAChG;AACD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA,WAAU,6BAAM,aAAY,SAAS,MAAM,GAAG,UAAU,OAAO,aAAa,MAAM,IAAI;AAAA,MACtF,gBAAe,6BAAM,kBAAiB,SAAS,MAAM,GAAG,UAAU,OAAO,aAAa,MAAM,IAAI;AAAA,MAChG,QAAO,6BAAM,UAAS,SAAS,MAAM,OAAO,QAAQ,GAAG,KAAK,CAAC;AAAA,MAC7D,MAAM,SAAS,IAAI;AACjB,cAAM,WAAW,KAAK,OAAO,EAAE,SAAS,MAAM,SAAS,UAAU,WAAW,SAAS,UAAU,MAAA,CAAO;AAAA,MAAA;AAAA,IACxG;AAAA,EACF;AAEF,SAAO,gBAAgB;AAAA,IACrB,MAAM;AAAA,IACN,OAAO;AAAA;AAAA,MAEL,IAAI;AAAA,QACF,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA,MAEZ,MAAM;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA;AAAA,MAGZ,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA,MAEZ,KAAK;AAAA,QACH,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA,MAEZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA;AAAA,MAGZ,UAAU;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA,MAEZ,YAAY;AAAA,QACV,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA,MAEZ,YAAY;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA;AAAA,MAGZ,aAAa;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA,MAEZ,iBAAiB;AAAA,QACf,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA;AAAA,MAGZ,SAAS;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA,MAEZ,kBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA;AAAA,MAGZ,UAAU;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA;AAAA,MAGZ,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA;AAAA,MAGZ,eAAe;AAAA,QACb,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,MAAA;AAAA,IACZ;AAAA,IAEF,SAAS;AAAA,IACT,MAAM,OAAO,EAAE,SAAS;AACP,gBAAA;AACf,YAAM,EAAE,IAAI,MAAM,UAAU,YAAY,WAAW,cAAA,IAAkB,YAAY,KAAK;AACnE,iBAAW,KAAK;AACnC,YAAM,KAA0B;AAChC,YAAM,QAA6B;AASnC,qBAAe,SAAS,UAAU,cAAc;AACtB;AACtB;AAAA,QAAA;AAAA,MAaD;AAuCH,aAAO,MAAM;;AACX,YAAI,CAAC,WAAW,SAAS,CAAC,UAAU,SAAS,CAAC,0BAA0B,GAAG,KAAK,GAAG;AACjF,gBAAM,kBAAkB;AAAA,YACtB,KAAK;AAAA,YACL,IAAI,GAAG;AAAA,YACP,aAAa,MAAM,eAAe,QAAQ;AAAA,YAC1C,kBAAkB,MAAM,oBAAoB,QAAQ;AAAA,YACpD,SAAS,MAAM;AAAA,YACf,kBAAkB,MAAM;AAAA,YACxB,QAAQ,MAAM;AAAA,UAAA;AAEhB,cAAI,CAAC,MAAM,QAAQ;AAUjB,4BAAgB,MAAM,MAAM,OAAO;AAAA,UAAA;AAErC,iBAAO;AAAA,YACL,iBAAiB,YAAY;AAAA,YAC7B;AAAA,YACA,MAAM;AAAA,UAAA;AAAA,QACR;AAEF,cAAM,SAAS,MAAM,UAAU;AAE/B,cAAM,MAAM;AAAA;AAAA,UAEV,MAAM,QAAQ,KAAK,MAAM;AAAA,UACzB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,UAKR,cAAc,SAAS,UAAU,QAAQ,wBAAwB;AAAA,QAAA,KAC9D;AACL,YAAI,MAAM,QAAQ;AAChB,cAAI,CAAC,MAAM,SAAS;AAClB,mBAAO;AAAA,UAAA;AAET,iBAAO,MAAM,QAAQ;AAAA,YACnB,MAAM,KAAK;AAAA,YACX;AAAA,YACA;AAAA,YACA,IAAI,QAAQ;AACV,kBAAI,CAAC,KAAK,OAAO;AACf,uBAAO;AAAA,cAAA;AAET,oBAAM,MAAM,IAAI,IAAI,KAAK,OAAmD,kBAAkB;AAC9F,qBAAO;AAAA,gBACL,MAAM,IAAI;AAAA,gBACV,UAAU,IAAI;AAAA,gBACd,IAAI,QAAQ;AACV,yBAAO,WAAW,IAAI,MAAM;AAAA,gBAAA;AAAA,gBAE9B,MAAM,IAAI;AAAA,gBACV,QAAQ,CAAA;AAAA,gBACR,MAAM;AAAA,gBACN,SAAS,CAAA;AAAA,gBACT,gBAAgB;AAAA,gBAChB,MAAM,CAAA;AAAA,gBACN,MAAM,KAAK;AAAA,cAAA;AAAA,YACb;AAAA,YAEF;AAAA,YACA;AAAA,YACA,YAAY,WAAW,SAAS,UAAU;AAAA,YAC1C,UAAU;AAAA,YACV,eAAe;AAAA,UAAA,CAChB;AAAA,QAAA;AAEH,eAAO,EAAE,KAAK,EAAE,KAAK,IAAI,MAAM,KAAK,SAAS,MAAM,KAAK,OAAA,IAAU,WAAM,YAAN,8BAAiB;AAAA,MAAA;AAAA,IACrF;AAAA;AAAA,EACF,CAED;AACH;AACA,MAAA,oDAA8C,gBAAgB;AAC9D,SAAS,2BAA2B,IAAI,eAAe;AACrD,QAAM,cAAc,kBAAkB,WAAW,oBAAoB;AACrE,QAAM,+BAA+B,YAAY,EAAE,KAAK,CAAC,GAAG,WAAW,MAAM;AAC7E,MAAI,8BAA8B;AAChC,WAAO;AAAA,EAAA;AAET,SAAO,YAAY,IAAI,IAAI;AAC7B;;;;;;;;AC9QA,UAAM,QAAQ;AAGd,UAAM,gBAAgB,QAAQ,IAAI,aAAa;AAG/C,UAAM,gBAAgB,MAAc;AAClC,cAAQ,MAAM,MAAM,YAAA;AAAA,QAClB,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MAAA;AAAA,IACX;AAIF,UAAM,kBAAkB,MAAc;AACpC,UAAI,MAAM,MAAM,eAAe;AAC7B,eAAO,MAAM,MAAM;AAAA,MAAA;AAGrB,cAAQ,MAAM,MAAM,YAAA;AAAA,QAClB,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MAAA;AAAA,IACX;AAIF,UAAM,gBAAgB,MAAc;AAClC,cAAQ,MAAM,MAAM,YAAA;AAAA,QAClB,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MAAA;AAAA,IACX;AA0BF,eAAW;AAAA,MACT,OAAO,GAAG,MAAM,MAAM,UAAU,MAAM,eAAe;AAAA,MACrD,aAAa,gBAAA;AAAA,MACb,QAAQ;AAAA,IAAA,CACT;;;mBAlKMC,eAAAC,WAAA,EAAA,OAAM,kFAA8E,MAAA,CAAA,yrBAuBhFC,eAAAA,KAAAA,MAAM,UAAU,gHAIhB,cAAA,CAAa,yFAIb,iBAAe,iMASb,cAAA,CAAa;;QAIhB,IAAG;AAAA,QACH,OAAM;AAAA,MAAA;yBAFR,CAKW,GAAAL,QAAAC,UAAA,aAAA;;;;;8BAFV,kBAED;AAAA,YAAA;AAAA;;;;;UAKM,iBAAiBI,KAAAA,MAAM,OAAK;AAOuCA,cAAAA,uUAAAA,eAAAA,KAAAA,MAAM,KAAK,CAAA,wBAAA;AAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;ACjC5F,UAAM,iBAAgJ,MAAM;AAC5J,UAAM,UAAU,WAAA;AACE,YAAQ,eAAA;AAKO,YAAQ,WAAW;AACpD,UAAM,iBAAiB;AACvB,YAAQ,iBAAiB,UAAU;AACnB,YAAQ,MAAM,aAAa,CAAC,UAAU,MAAM,IAAI,CAAC,SAAS,KAAA,CAAM,GAAG,WAAW;AAI9F,UAAM,QAAQ,SAAA;AACd,UAAM,cAAoC,MAAM,SAAS,CAAC,QAAQ,WAAW;AAE7E,oBAAgB,CAAC,KAAK,QAAQ,SAAS;AACrC,cAAQ,MAAM,SAAS,aAAa,KAAK,QAAQ,IAAI,EAAE,MAAM,CAAC,cAAc,QAAQ,MAAM,oCAAoC,SAAS,CAAC;AAM5D;AAC1E,cAAM,IAAI,QAAQ,eAAe,MAAM,UAAU,GAAG,CAAC;AACrD,yBAAiB,MAAM,CAAC;AACxB,eAAO;AAAA,MAAA;AAAA,IACT,CACD;AACD,UAAM,gBAAsC,QAAQ,WAAW;;;iBAzD7D,MAeW;cAdEC,MAAA,WAAA,GAAW;;qBAETA,MAAA,KAAA,GAAK;AACf,kBAAAC,mBAAAD,MAAA,cAAA,GAAA,EAAA,OAAOA,MAAA,KAAA,EAAA,GAAK,MAAA,OAAA,CAAA;AAAA,UAAA,WAGFA,MAAA,aAAA,GAAa;AACvB,kBAAAC,mBAAAD,MAAA,cAAA,GAAA,EAAA,SAASA,MAAA,aAAA,EAAA,GAAa,MAAA,OAAA,CAAA;AAAA,UAAA,WAIZA,MAAA,cAAA,GAAc;sEADpBA,MAAA,cAAA,CAAc,GAAA,MAAA,IAAA,GAAA,OAAA;AAAA,UAAA;;;;;;;;;;;;;;;ACHzB,IAAI;AACoB;AACtB,UAAQ,eAAe,oBAAoB,YAAY;;AACrD,UAAM,SAAS,UAAUE,SAAa;AACtC,UAAM,OAAO,cAAc,EAAE,QAAQ,YAAY;AACjD,QAAI;AACF,YAAM,aAAa,MAAM,OAAO;AAChC,YAAM,KAAK,MAAM,SAAS,eAAe,MAAM;AAAA,IAAA,SACxC,OAAO;AACd,YAAM,KAAK,MAAM,SAAS,aAAa,KAAK;AAC5C,iBAAK,SAAQ,UAAb,GAAa,QAAU,YAAY,KAAK;AAAA,IAAA;AAE1C,QAAI,yCAAY,iBAAiB;AAC/B,YAAM,IAAI,MAAM,iBAAiB;AAAA,IAAA;AAEnC,WAAO;AAAA,EAAA;AAEX;AAmDA,MAAA,UAAe,CAAC,eAAe,MAAM,UAAU;", "x_google_ignoreList": [3, 4, 5, 6, 7, 8, 9, 11, 12, 14, 15, 17, 18, 19, 20, 23, 24, 27, 28, 29, 31, 33, 34]}