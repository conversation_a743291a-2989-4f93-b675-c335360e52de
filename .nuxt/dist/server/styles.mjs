const interopDefault = r => r.default || r || []
export default {
  "node_modules/nuxt/dist/app/entry.js": () => import('./_nuxt/entry-styles.C48xXMVk.mjs').then(interopDefault),
  "app.vue": () => import('./_nuxt/app-styles.Dtdrxmb-.mjs').then(interopDefault),
  "error.vue": () => import('./_nuxt/error-styles.B8DQri7Y.mjs').then(interopDefault),
  "pages/[slug].vue": () => import('./_nuxt/_slug_-styles.CL2rhY-Q.mjs').then(interopDefault),
  "pages/index.vue": () => import('./_nuxt/index-styles.M0zKQNNL.mjs').then(interopDefault),
  "error.vue?vue&type=style&index=0&scoped=06dc5cd9&lang.css": () => import('./_nuxt/error-styles.CG-tdzpw.mjs').then(interopDefault),
  "app.vue?vue&type=style&index=0&lang.css": () => import('./_nuxt/app-styles.DVvX4UM-.mjs').then(interopDefault),
  "pages/[slug].vue?vue&type=style&index=0&scoped=236cd69b&lang.css": () => import('./_nuxt/_slug_-styles.BOQSkVuT.mjs').then(interopDefault),
  "pages/index.vue?vue&type=style&index=0&scoped=4cc4d54e&lang.css": () => import('./_nuxt/index-styles.D8Pq83Gg.mjs').then(interopDefault),
  "components/wheel/WheelInteractive.vue": () => import('./_nuxt/WheelInteractive-styles.DdWTKuXH.mjs').then(interopDefault),
  "components/wheel/WheelInteractive.vue?vue&type=style&index=0&scoped=3c24298e&lang.css": () => import('./_nuxt/WheelInteractive-styles.CmX35ge9.mjs').then(interopDefault),
  "layouts/default.vue": () => import('./_nuxt/default-styles.CaqEemJc.mjs').then(interopDefault),
  "layouts/default.vue?vue&type=style&index=0&scoped=b07fc1e6&lang.css": () => import('./_nuxt/default-styles.B_qvsBBZ.mjs').then(interopDefault)
}