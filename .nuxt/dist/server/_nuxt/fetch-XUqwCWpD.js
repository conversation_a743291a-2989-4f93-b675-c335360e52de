import { defineComponent, ref, watch, resolveComponent, mergeProps, unref, useSSRContext, createElementBlock, shallowRef, getCurrentInstance, provide, cloneVNode, h, computed, toValue, onServerPrefetch, toRef, nextTick, reactive } from "vue";
import { ssrRenderAttrs, ssrRenderComponent } from "vue/server-renderer";
import { d as useNuxtApp, _ as _export_sfc, e as asyncDataDefaults, c as createError, f as fetchDefaults } from "../server.mjs";
import { hash } from "/Users/<USER>/Webs/makechoice/node_modules/ohash/dist/index.mjs";
import { isPlainObject } from "@vue/shared";
import { debounce } from "/Users/<USER>/Webs/makechoice/node_modules/perfect-debounce/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/hookable/dist/index.mjs";
function useRequestEvent(nuxtApp) {
  var _a;
  nuxtApp || (nuxtApp = useNuxtApp());
  return (_a = nuxtApp.ssrContext) == null ? void 0 : _a.event;
}
function useRequestFetch() {
  var _a;
  return ((_a = useRequestEvent()) == null ? void 0 : _a.$fetch) || globalThis.$fetch;
}
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "WheelInteractive",
  __ssrInlineRender: true,
  props: {
    initialPrizes: { default: () => [] },
    wheelId: {},
    title: {},
    otherWheels: { default: () => [] }
  },
  setup(__props, { expose: __expose }) {
    const props = __props;
    const prizes = ref([...props.initialPrizes]);
    watch(() => props.initialPrizes, (newPrizes) => {
      prizes.value = [...newPrizes];
    }, { immediate: true });
    const handlePrizesChange = (newPrizes) => {
      prizes.value = [...newPrizes];
    };
    __expose({
      getPrizes: () => prizes.value,
      setPrizes: (newPrizes) => {
        prizes.value = [...newPrizes];
      },
      resetPrizes: () => {
        prizes.value = [...props.initialPrizes];
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_LuckWheel = resolveComponent("LuckWheel");
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "wheel-interactive" }, _attrs))} data-v-3c24298e>`);
      _push(ssrRenderComponent(_component_LuckWheel, {
        prizes: unref(prizes),
        "wheel-id": _ctx.wheelId,
        title: _ctx.title,
        "other-wheels": _ctx.otherWheels,
        onPrizesChange: handlePrizesChange
      }, null, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/wheel/WheelInteractive.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const __nuxt_component_0 = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-3c24298e"]]);
const presetWheels = [
  {
    id: "yes-no",
    name: "Yes or No Decision Maker",
    slug: "yes-no-decision-maker",
    isDefault: true,
    prizes: [
      { text: "Yes", color: "#22c55e" },
      { text: "No", color: "#ef4444" }
    ],
    seo: {
      title: "Yes or No Decision Maker - Quick Binary Choice Wheel",
      description: "Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.",
      keywords: ["yes no decision", "binary choice", "decision maker", "quick decision"]
    },
    articles: [
      {
        title: "The Psychology of Binary Decisions",
        content: `
          <p>
            Binary decisions are fundamental to human psychology. When faced with a simple yes or no choice, 
            our brains often overthink the process, leading to decision paralysis.
          </p>
          <p>
            Using a decision wheel removes the burden of choice from your conscious mind, allowing your 
            subconscious preferences to guide the outcome. This can often lead to more satisfying decisions.
          </p>
        `
      },
      {
        title: "When to Use Yes/No Decision Making",
        content: `
          <p>
            Yes or no decisions are perfect for:
          </p>
          <ul>
            <li>Breaking ties when you're genuinely undecided</li>
            <li>Making quick decisions when time is limited</li>
            <li>Overcoming analysis paralysis</li>
            <li>Adding an element of fun to routine choices</li>
          </ul>
        `
      }
    ]
  },
  {
    id: "what-to-eat",
    name: "What to Eat Decision Wheel",
    slug: "what-to-eat-decision-wheel",
    prizes: [
      { text: "Pizza", color: "#ff6b6b" },
      { text: "Burger", color: "#4ecdc4" },
      { text: "Sushi", color: "#45b7d1" },
      { text: "Pasta", color: "#96ceb4" },
      { text: "Tacos", color: "#feca57" },
      { text: "Salad", color: "#ff9ff3" },
      { text: "Sandwich", color: "#54a0ff" },
      { text: "Chinese", color: "#5f27cd" }
    ],
    seo: {
      title: "What to Eat Decision Wheel - Food Choice Generator",
      description: "Cant decide what to eat? Use our food decision wheel to randomly choose from popular meal options. Perfect for indecisive foodies!",
      keywords: ["what to eat", "food decision", "meal picker", "restaurant choice", "food wheel"]
    },
    articles: [
      {
        title: "Solving the Daily Food Dilemma",
        content: `
          <p>
            "What should I eat?" is one of the most common daily decisions we face. Decision fatigue from 
            constantly choosing meals can be exhausting.
          </p>
          <p>
            Our food decision wheel takes the stress out of meal planning by randomly selecting from 
            popular food options, helping you discover new favorites and break out of eating routines.
          </p>
        `
      }
    ]
  },
  {
    id: "movie-night",
    name: "Movie Night Picker",
    slug: "movie-night-picker",
    prizes: [
      { text: "Action", color: "#e74c3c" },
      { text: "Comedy", color: "#f39c12" },
      { text: "Drama", color: "#9b59b6" },
      { text: "Horror", color: "#2c3e50" },
      { text: "Romance", color: "#e91e63" },
      { text: "Sci-Fi", color: "#3498db" },
      { text: "Thriller", color: "#34495e" },
      { text: "Documentary", color: "#27ae60" }
    ],
    seo: {
      title: "Movie Night Picker - Random Movie Genre Selector",
      description: "End movie night arguments with our genre picker wheel. Randomly select from popular movie genres for your next film night.",
      keywords: ["movie picker", "film genre", "movie night", "what to watch", "movie decision"]
    },
    articles: [
      {
        title: "Making Movie Night Decisions Fair",
        content: `
          <p>
            Movie night with friends or family often leads to lengthy debates about what to watch. 
            Different people have different preferences, and finding a compromise can be challenging.
          </p>
          <p>
            Using a movie genre picker wheel ensures everyone has an equal chance of their preferred 
            genre being selected, making the decision process fair and fun.
          </p>
        `
      }
    ]
  },
  {
    id: "weekend-activity",
    name: "Weekend Activity Planner",
    slug: "weekend-activity-planner",
    prizes: [
      { text: "Hiking", color: "#2ecc71" },
      { text: "Beach", color: "#3498db" },
      { text: "Museum", color: "#9b59b6" },
      { text: "Shopping", color: "#e91e63" },
      { text: "Park", color: "#27ae60" },
      { text: "Movies", color: "#34495e" },
      { text: "Restaurant", color: "#e67e22" },
      { text: "Stay Home", color: "#95a5a6" }
    ],
    seo: {
      title: "Weekend Activity Planner - Random Activity Generator",
      description: "Plan your perfect weekend with our activity picker wheel. Discover new activities and break out of your routine.",
      keywords: ["weekend activities", "activity planner", "what to do", "weekend ideas", "activity picker"]
    },
    articles: [
      {
        title: "Breaking Out of Weekend Routines",
        content: `
          <p>
            Many people fall into predictable weekend routines, doing the same activities week after week. 
            While routine can be comforting, it can also lead to boredom and missed opportunities.
          </p>
          <p>
            Our weekend activity planner introduces an element of randomness to your leisure time, 
            encouraging you to try new experiences and make the most of your free time.
          </p>
        `
      }
    ]
  },
  {
    id: "team-picker",
    name: "Team Member Picker",
    slug: "team-member-picker",
    prizes: [
      { text: "Alice", color: "#ff7675" },
      { text: "Bob", color: "#74b9ff" },
      { text: "Charlie", color: "#00b894" },
      { text: "Diana", color: "#fdcb6e" },
      { text: "Eve", color: "#e17055" },
      { text: "Frank", color: "#a29bfe" }
    ],
    seo: {
      title: "Team Member Picker - Random Team Selection Wheel",
      description: "Fairly select team members, assign tasks, or pick volunteers with our random team picker wheel. Perfect for classrooms and workplaces.",
      keywords: ["team picker", "random selection", "team member selector", "fair selection", "group picker"]
    },
    articles: [
      {
        title: "Fair Team Selection Made Easy",
        content: `
          <p>
            Whether you're a teacher assigning classroom tasks, a manager distributing work, or organizing team activities, 
            fair selection is crucial for maintaining group harmony.
          </p>
          <p>
            Our team picker wheel ensures everyone has an equal chance of being selected, removing bias and 
            making the process transparent and fun.
          </p>
        `
      }
    ]
  }
];
function findWheelBySlug(slug) {
  return presetWheels.find((wheel) => wheel.slug === slug);
}
function getDefaultWheel() {
  return presetWheels.find((wheel) => wheel.isDefault) || presetWheels[0];
}
function getWheelList() {
  return presetWheels.map((wheel) => ({
    id: wheel.id,
    name: wheel.name,
    slug: wheel.slug,
    description: wheel.seo.description
  }));
}
async function getServerWheelPageData(slug) {
  try {
    const wheel = findWheelBySlug(slug);
    if (!wheel) {
      throw new Error(`Wheel with slug "${slug}" not found`);
    }
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter((w) => w.slug !== slug);
    return {
      wheel,
      otherWheels
    };
  } catch (error) {
    console.error("Error fetching server wheel page data:", error);
    throw error;
  }
}
async function getServerDefaultWheel() {
  try {
    const defaultWheel = getDefaultWheel();
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter((w) => w.slug !== defaultWheel.slug);
    return {
      defaultWheel,
      otherWheels
    };
  } catch (error) {
    console.error("Error fetching server default wheel:", error);
    throw error;
  }
}
function validateWheelSlug(slug) {
  return !!findWheelBySlug(slug);
}
function getWheelSeoData(slug) {
  try {
    const wheel = slug ? findWheelBySlug(slug) : getDefaultWheel();
    if (!wheel) {
      return null;
    }
    return {
      title: wheel.seo.title,
      description: wheel.seo.description,
      keywords: wheel.seo.keywords.join(", "),
      canonical: slug ? `https://decisionsmaker.online/${slug}` : "https://decisionsmaker.online",
      ogImage: "https://decisionsmaker.online/og-image.png"
    };
  } catch (error) {
    console.error("Error getting wheel SEO data:", error);
    return null;
  }
}
defineComponent({
  name: "ServerPlaceholder",
  render() {
    return createElementBlock("div");
  }
});
const clientOnlySymbol = Symbol.for("nuxt:client-only");
defineComponent({
  name: "ClientOnly",
  inheritAttrs: false,
  props: ["fallback", "placeholder", "placeholderTag", "fallbackTag"],
  setup(props, { slots, attrs }) {
    const mounted = shallowRef(false);
    const vm = getCurrentInstance();
    if (vm) {
      vm._nuxtClientOnly = true;
    }
    provide(clientOnlySymbol, true);
    return () => {
      var _a;
      if (mounted.value) {
        const vnodes = (_a = slots.default) == null ? void 0 : _a.call(slots);
        if (vnodes && vnodes.length === 1) {
          return [cloneVNode(vnodes[0], attrs)];
        }
        return vnodes;
      }
      const slot = slots.fallback || slots.placeholder;
      if (slot) {
        return h(slot);
      }
      const fallbackStr = props.fallback || props.placeholder || "";
      const fallbackTag = props.fallbackTag || props.placeholderTag || "span";
      return createElementBlock(fallbackTag, attrs, fallbackStr);
    };
  }
});
const isDefer = (dedupe) => dedupe === "defer" || dedupe === false;
function useAsyncData(...args) {
  var _a;
  const autoKey = typeof args[args.length - 1] === "string" ? args.pop() : void 0;
  if (_isAutoKeyNeeded(args[0], args[1])) {
    args.unshift(autoKey);
  }
  let [_key, _handler, options = {}] = args;
  const key = computed(() => toValue(_key));
  if (typeof key.value !== "string") {
    throw new TypeError("[nuxt] [useAsyncData] key must be a string.");
  }
  if (typeof _handler !== "function") {
    throw new TypeError("[nuxt] [useAsyncData] handler must be a function.");
  }
  const nuxtApp = useNuxtApp();
  options.server ?? (options.server = true);
  options.default ?? (options.default = getDefault);
  options.getCachedData ?? (options.getCachedData = getDefaultCachedData);
  options.lazy ?? (options.lazy = false);
  options.immediate ?? (options.immediate = true);
  options.deep ?? (options.deep = asyncDataDefaults.deep);
  options.dedupe ?? (options.dedupe = "cancel");
  options._functionName || "useAsyncData";
  nuxtApp._asyncData[key.value];
  const initialFetchOptions = { cause: "initial", dedupe: options.dedupe };
  if (!((_a = nuxtApp._asyncData[key.value]) == null ? void 0 : _a._init)) {
    initialFetchOptions.cachedData = options.getCachedData(key.value, nuxtApp, { cause: "initial" });
    nuxtApp._asyncData[key.value] = createAsyncData(nuxtApp, key.value, _handler, options, initialFetchOptions.cachedData);
  }
  const asyncData = nuxtApp._asyncData[key.value];
  asyncData._deps++;
  const initialFetch = () => nuxtApp._asyncData[key.value].execute(initialFetchOptions);
  const fetchOnServer = options.server !== false && nuxtApp.payload.serverRendered;
  if (fetchOnServer && options.immediate) {
    const promise = initialFetch();
    if (getCurrentInstance()) {
      onServerPrefetch(() => promise);
    } else {
      nuxtApp.hook("app:created", async () => {
        await promise;
      });
    }
  }
  const asyncReturn = {
    data: writableComputedRef(() => {
      var _a2;
      return (_a2 = nuxtApp._asyncData[key.value]) == null ? void 0 : _a2.data;
    }),
    pending: writableComputedRef(() => {
      var _a2;
      return (_a2 = nuxtApp._asyncData[key.value]) == null ? void 0 : _a2.pending;
    }),
    status: writableComputedRef(() => {
      var _a2;
      return (_a2 = nuxtApp._asyncData[key.value]) == null ? void 0 : _a2.status;
    }),
    error: writableComputedRef(() => {
      var _a2;
      return (_a2 = nuxtApp._asyncData[key.value]) == null ? void 0 : _a2.error;
    }),
    refresh: (...args2) => nuxtApp._asyncData[key.value].execute(...args2),
    execute: (...args2) => nuxtApp._asyncData[key.value].execute(...args2),
    clear: () => clearNuxtDataByKey(nuxtApp, key.value)
  };
  const asyncDataPromise = Promise.resolve(nuxtApp._asyncDataPromises[key.value]).then(() => asyncReturn);
  Object.assign(asyncDataPromise, asyncReturn);
  return asyncDataPromise;
}
function writableComputedRef(getter) {
  return computed({
    get() {
      var _a;
      return (_a = getter()) == null ? void 0 : _a.value;
    },
    set(value) {
      const ref2 = getter();
      if (ref2) {
        ref2.value = value;
      }
    }
  });
}
function _isAutoKeyNeeded(keyOrFetcher, fetcher) {
  if (typeof keyOrFetcher === "string") {
    return false;
  }
  if (typeof keyOrFetcher === "object" && keyOrFetcher !== null) {
    return false;
  }
  if (typeof keyOrFetcher === "function" && typeof fetcher === "function") {
    return false;
  }
  return true;
}
function clearNuxtDataByKey(nuxtApp, key) {
  if (key in nuxtApp.payload.data) {
    nuxtApp.payload.data[key] = void 0;
  }
  if (key in nuxtApp.payload._errors) {
    nuxtApp.payload._errors[key] = asyncDataDefaults.errorValue;
  }
  if (nuxtApp._asyncData[key]) {
    nuxtApp._asyncData[key].data.value = void 0;
    nuxtApp._asyncData[key].error.value = asyncDataDefaults.errorValue;
    {
      nuxtApp._asyncData[key].pending.value = false;
    }
    nuxtApp._asyncData[key].status.value = "idle";
  }
  if (key in nuxtApp._asyncDataPromises) {
    if (nuxtApp._asyncDataPromises[key]) {
      nuxtApp._asyncDataPromises[key].cancelled = true;
    }
    nuxtApp._asyncDataPromises[key] = void 0;
  }
}
function pick(obj, keys) {
  const newObj = {};
  for (const key of keys) {
    newObj[key] = obj[key];
  }
  return newObj;
}
function createAsyncData(nuxtApp, key, _handler, options, initialCachedData) {
  var _a, _b;
  (_a = nuxtApp.payload._errors)[key] ?? (_a[key] = asyncDataDefaults.errorValue);
  const hasCustomGetCachedData = options.getCachedData !== getDefaultCachedData;
  const handler = !import.meta.prerender || !((_b = nuxtApp.ssrContext) == null ? void 0 : _b._sharedPrerenderCache) ? _handler : () => {
    const value = nuxtApp.ssrContext._sharedPrerenderCache.get(key);
    if (value) {
      return value;
    }
    const promise = Promise.resolve().then(() => nuxtApp.runWithContext(() => _handler(nuxtApp)));
    nuxtApp.ssrContext._sharedPrerenderCache.set(key, promise);
    return promise;
  };
  const _ref = options.deep ? ref : shallowRef;
  const hasCachedData = initialCachedData != null;
  const unsubRefreshAsyncData = nuxtApp.hook("app:data:refresh", async (keys) => {
    if (!keys || keys.includes(key)) {
      await asyncData.execute({ cause: "refresh:hook" });
    }
  });
  const asyncData = {
    data: _ref(hasCachedData ? initialCachedData : options.default()),
    pending: shallowRef(!hasCachedData),
    error: toRef(nuxtApp.payload._errors, key),
    status: shallowRef("idle"),
    execute: (opts = {}) => {
      if (nuxtApp._asyncDataPromises[key]) {
        if (isDefer(opts.dedupe ?? options.dedupe)) {
          return nuxtApp._asyncDataPromises[key];
        }
        nuxtApp._asyncDataPromises[key].cancelled = true;
      }
      if (opts.cause === "initial" || nuxtApp.isHydrating) {
        const cachedData = "cachedData" in opts ? opts.cachedData : options.getCachedData(key, nuxtApp, { cause: opts.cause ?? "refresh:manual" });
        if (cachedData != null) {
          nuxtApp.payload.data[key] = asyncData.data.value = cachedData;
          asyncData.error.value = asyncDataDefaults.errorValue;
          asyncData.status.value = "success";
          return Promise.resolve(cachedData);
        }
      }
      {
        asyncData.pending.value = true;
      }
      asyncData.status.value = "pending";
      const promise = new Promise(
        (resolve, reject) => {
          try {
            resolve(handler(nuxtApp));
          } catch (err) {
            reject(err);
          }
        }
      ).then(async (_result) => {
        if (promise.cancelled) {
          return nuxtApp._asyncDataPromises[key];
        }
        let result = _result;
        if (options.transform) {
          result = await options.transform(_result);
        }
        if (options.pick) {
          result = pick(result, options.pick);
        }
        nuxtApp.payload.data[key] = result;
        asyncData.data.value = result;
        asyncData.error.value = asyncDataDefaults.errorValue;
        asyncData.status.value = "success";
      }).catch((error) => {
        if (promise.cancelled) {
          return nuxtApp._asyncDataPromises[key];
        }
        asyncData.error.value = createError(error);
        asyncData.data.value = unref(options.default());
        asyncData.status.value = "error";
      }).finally(() => {
        if (promise.cancelled) {
          return;
        }
        {
          asyncData.pending.value = false;
        }
        delete nuxtApp._asyncDataPromises[key];
      });
      nuxtApp._asyncDataPromises[key] = promise;
      return nuxtApp._asyncDataPromises[key];
    },
    _execute: debounce((...args) => asyncData.execute(...args), 0, { leading: true }),
    _default: options.default,
    _deps: 0,
    _init: true,
    _hash: void 0,
    _off: () => {
      var _a2;
      unsubRefreshAsyncData();
      if ((_a2 = nuxtApp._asyncData[key]) == null ? void 0 : _a2._init) {
        nuxtApp._asyncData[key]._init = false;
      }
      if (!hasCustomGetCachedData) {
        nextTick(() => {
          var _a3;
          if (!((_a3 = nuxtApp._asyncData[key]) == null ? void 0 : _a3._init)) {
            clearNuxtDataByKey(nuxtApp, key);
            asyncData.execute = () => Promise.resolve();
            asyncData.data.value = asyncDataDefaults.value;
          }
        });
      }
    }
  };
  return asyncData;
}
const getDefault = () => asyncDataDefaults.value;
const getDefaultCachedData = (key, nuxtApp, ctx) => {
  if (nuxtApp.isHydrating) {
    return nuxtApp.payload.data[key];
  }
  if (ctx.cause !== "refresh:manual" && ctx.cause !== "refresh:hook") {
    return nuxtApp.static.data[key];
  }
};
function useFetch(request, arg1, arg2) {
  const [opts = {}, autoKey] = typeof arg1 === "string" ? [{}, arg1] : [arg1, arg2];
  const _request = computed(() => toValue(request));
  const key = computed(() => toValue(opts.key) || "$f" + hash([autoKey, typeof _request.value === "string" ? _request.value : "", ...generateOptionSegments(opts)]));
  if (!opts.baseURL && typeof _request.value === "string" && (_request.value[0] === "/" && _request.value[1] === "/")) {
    throw new Error('[nuxt] [useFetch] the request URL must not start with "//".');
  }
  const {
    server,
    lazy,
    default: defaultFn,
    transform,
    pick: pick2,
    watch: watchSources,
    immediate,
    getCachedData,
    deep,
    dedupe,
    ...fetchOptions
  } = opts;
  const _fetchOptions = reactive({
    ...fetchDefaults,
    ...fetchOptions,
    cache: typeof opts.cache === "boolean" ? void 0 : opts.cache
  });
  const _asyncDataOptions = {
    server,
    lazy,
    default: defaultFn,
    transform,
    pick: pick2,
    immediate,
    getCachedData,
    deep,
    dedupe,
    watch: watchSources === false ? [] : [...watchSources || [], _fetchOptions]
  };
  if (!immediate) {
    let setImmediate = function() {
      _asyncDataOptions.immediate = true;
    };
    watch(key, setImmediate, { flush: "sync", once: true });
    watch([...watchSources || [], _fetchOptions], setImmediate, { flush: "sync", once: true });
  }
  let controller;
  const asyncData = useAsyncData(watchSources === false ? key.value : key, () => {
    var _a;
    (_a = controller == null ? void 0 : controller.abort) == null ? void 0 : _a.call(controller, new DOMException("Request aborted as another request to the same endpoint was initiated.", "AbortError"));
    controller = typeof AbortController !== "undefined" ? new AbortController() : {};
    const timeoutLength = toValue(opts.timeout);
    let timeoutId;
    if (timeoutLength) {
      timeoutId = setTimeout(() => controller.abort(new DOMException("Request aborted due to timeout.", "AbortError")), timeoutLength);
      controller.signal.onabort = () => clearTimeout(timeoutId);
    }
    let _$fetch = opts.$fetch || globalThis.$fetch;
    if (!opts.$fetch) {
      const isLocalFetch = typeof _request.value === "string" && _request.value[0] === "/" && (!toValue(opts.baseURL) || toValue(opts.baseURL)[0] === "/");
      if (isLocalFetch) {
        _$fetch = useRequestFetch();
      }
    }
    return _$fetch(_request.value, { signal: controller.signal, ..._fetchOptions }).finally(() => {
      clearTimeout(timeoutId);
    });
  }, _asyncDataOptions);
  return asyncData;
}
function generateOptionSegments(opts) {
  var _a;
  const segments = [
    ((_a = toValue(opts.method)) == null ? void 0 : _a.toUpperCase()) || "GET",
    toValue(opts.baseURL)
  ];
  for (const _obj of [opts.params || opts.query]) {
    const obj = toValue(_obj);
    if (!obj) {
      continue;
    }
    const unwrapped = {};
    for (const [key, value] of Object.entries(obj)) {
      unwrapped[toValue(key)] = toValue(value);
    }
    segments.push(unwrapped);
  }
  if (opts.body) {
    const value = toValue(opts.body);
    if (!value) {
      segments.push(hash(value));
    } else if (value instanceof ArrayBuffer) {
      segments.push(hash(Object.fromEntries([...new Uint8Array(value).entries()].map(([k, v]) => [k, v.toString()]))));
    } else if (value instanceof FormData) {
      const obj = {};
      for (const entry of value.entries()) {
        const [key, val] = entry;
        obj[key] = val instanceof File ? val.name : val;
      }
      segments.push(hash(obj));
    } else if (isPlainObject(value)) {
      segments.push(hash(reactive(value)));
    } else {
      try {
        segments.push(hash(value));
      } catch {
        console.warn("[useFetch] Failed to hash body", value);
      }
    }
  }
  return segments;
}
export {
  __nuxt_component_0 as _,
  getWheelSeoData as a,
  getServerWheelPageData as b,
  getServerDefaultWheel as g,
  useFetch as u,
  validateWheelSlug as v
};
//# sourceMappingURL=fetch-XUqwCWpD.js.map
