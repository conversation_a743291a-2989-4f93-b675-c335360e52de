{"file": "default-DO4bbscw.js", "mappings": ";;;;;;;;;;;;;;;;;;AACO,YAAA,OAAAA,eAAAC,WAAA,EAAA,OAAM,+FAA2F,MAAA,CAAA,CAAA,mBAAA;;;;;;;;;;;;;", "names": ["_ssrRenderAttrs", "_mergeProps"], "sources": ["../../../../layouts/default.vue"], "sourcesContent": ["<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors\">\n    <slot />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\n// 布局级别的配置\n</script>\n\n<style scoped>\n/* 布局特定样式 */\n</style>\n"], "version": 3}