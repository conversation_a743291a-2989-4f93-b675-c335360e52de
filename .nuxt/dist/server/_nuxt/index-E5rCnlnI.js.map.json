{"file": "index-E5rCnlnI.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAoDA,UAAM,EAAE,KAAA,KAAS,CAAA,QAAA,SAAA,IAAAA,iBAAA,MAAM,SAA4B,mBAAmB;AAAA,MACpE,QAAQ;AAAA,MACR,SAAS,OAAO;AAAA,QACd,cAAc;AAAA,QACd,aAAa,CAAA;AAAA,QACb,SAAS;AAAA,MAAA;AAAA,IACX,GACF,aAAC,CAAA;AAGD,QAAI,CAAC,KAAK,MAAM,WAAW,CAAC,KAAK,MAAM,cAAc;AACnD,YAAM,cAAa,CAAA,QAAA,SAAA,IAAAA,iBAAA,MAAM,sBAAA,CAAsB;AAC/C,WAAK,QAAQ;AAAA,QACX,cAAc,WAAW;AAAA,QACzB,aAAa,WAAW;AAAA,QACxB,SAAS;AAAA,QACT,SAAS;AAAA,MAAA;AAAA,IACX;AAIF,UAAM,WAAW,SAAS,OAAO;AAAA,MAC/B,cAAc,KAAK,MAAM;AAAA,MACzB,aAAa,KAAK,MAAM;AAAA,IAAA,EACxB;AAGF,UAAM,UAAU,gBAAA;AAEhB,eAAW;AAAA,MACT,QAAO,mCAAS,UAAS;AAAA,MACzB,cAAa,mCAAS,gBAAe;AAAA,MACrC,WAAU,mCAAS,aAAY;AAAA,MAC/B,UAAS,mCAAS,UAAS;AAAA,MAC3B,gBAAe,mCAAS,gBAAe;AAAA,MACvC,UAAS,mCAAS,YAAW;AAAA,MAC7B,QAAO,mCAAS,cAAa;AAAA,MAC7B,aAAa;AAAA,MACb,eAAc,mCAAS,UAAS;AAAA,MAChC,qBAAoB,mCAAS,gBAAe;AAAA,IAAA,CAC7C;AAED,YAAQ;AAAA,MACN,MAAM;AAAA,QACJ;AAAA,UACE,KAAK;AAAA,UACL,OAAM,mCAAS,cAAa;AAAA,QAAA;AAAA,MAC9B;AAAA,IACF,CACD;AAGD,cAAU;AAAA,MACR,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,MAAM;AAAA,MACN,aAAa;AAAA,MACb,KAAK;AAAA,MACL,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,OAAO;AAAA,QACP,eAAe;AAAA,MAAA;AAAA,MAEjB,SAAS;AAAA,QACP,SAAS;AAAA,QACT,MAAM;AAAA,MAAA;AAAA,IACR,CACD;;;;;;gVAhHYC,MAAA,QAAA,EAAS,aAAa,IAAI,CAAA,gFAAA;;QAK1B,kBAAgBA,MAAA,QAAA,EAAS,aAAa;AAAA,QACtC,YAAUA,MAAA,QAAA,EAAS,aAAa;AAAA,QAChC,OAAOA,MAAA,QAAA,EAAS,aAAa;AAAA,QAC7B,gBAAcA,MAAA,QAAA,EAAS;AAAA,MAAA;;UAOtBA,MAAA,QAAA,EAAS,aAAa,YAAYA,MAAA,QAAA,EAAS,aAAa,SAAS,SAAM,GAAA;;AAK9CC,sBAAAD,MAAA,QAAA,EAAS,aAAa,UAAQ,CAAjD,SAASE,WAAK;2OAKjBC,eAAA,QAAQ,KAAK,0GAIR,QAAQ,WAAO;;;;;;;;;;;;;;;;;", "names": ["_withAsyncContext", "_unref", "_ssrRenderList", "index", "_ssrInterpolate"], "sources": ["../../../../pages/index.vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- 页面头部 -->\n    <AppHeader />\n    \n    <!-- 主要内容 -->\n    <div class=\"min-h-screen py-4 bg-white dark:bg-gray-900 overflow-hidden\" id=\"wheelContainer\">\n      <div class=\"h-full flex flex-col items-center max-w-full\">\n        <h1 class=\"text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100 flex-shrink-0\">\n          {{ pageData.defaultWheel.name }}\n        </h1>\n        <div class=\"flex flex-col items-center flex-grow w-full\">\n          <!-- 交互式转盘组件 -->\n          <WheelInteractive\n            :initial-prizes=\"pageData.defaultWheel.prizes\"\n            :wheel-id=\"pageData.defaultWheel.slug\"\n            :title=\"pageData.defaultWheel.name\"\n            :other-wheels=\"pageData.otherWheels\"\n          />\n        </div>\n      </div>\n      \n      <!-- 文章内容 -->\n      <div\n        v-if=\"pageData.defaultWheel.articles && pageData.defaultWheel.articles.length > 0\"\n        class=\"max-w-4xl mx-auto px-4 mt-12\"\n      >\n        <div class=\"grid gap-8 md:grid-cols-2\">\n          <article\n            v-for=\"(article, index) in pageData.defaultWheel.articles\"\n            :key=\"index\"\n            class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700\"\n          >\n            <h2 class=\"text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100\">\n              {{ article.title }}\n            </h2>\n            <div\n              class=\"prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300\"\n              v-html=\"article.content\"\n            />\n          </article>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { WheelPageResponse } from '~/types'\nimport { getServerDefaultWheel, getWheelSeoData } from '~/utils/server-api'\n\n// 服务端数据获取\nconst { data } = await useFetch<WheelPageResponse>('/api/wheel-page', {\n  server: true,\n  default: () => ({\n    currentWheel: null,\n    otherWheels: [],\n    success: false\n  })\n})\n\n// 如果数据获取失败，使用服务端备用数据\nif (!data.value.success || !data.value.currentWheel) {\n  const serverData = await getServerDefaultWheel()\n  data.value = {\n    currentWheel: serverData.defaultWheel,\n    otherWheels: serverData.otherWheels,\n    success: true,\n    message: 'Data loaded from server'\n  }\n}\n\n// 重新组织数据结构以匹配模板\nconst pageData = computed(() => ({\n  defaultWheel: data.value.currentWheel!,\n  otherWheels: data.value.otherWheels\n}))\n\n// SEO 配置\nconst seoData = getWheelSeoData()\n\nuseSeoMeta({\n  title: seoData?.title || 'Yes or No Decision Maker - Quick Binary Choice Wheel',\n  description: seoData?.description || 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.',\n  keywords: seoData?.keywords || 'yes no decision, binary choice, decision maker, quick decision',\n  ogTitle: seoData?.title || 'Yes or No Decision Maker - Quick Binary Choice Wheel',\n  ogDescription: seoData?.description || 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.',\n  ogImage: seoData?.ogImage || 'https://decisionsmaker.online/og-image.png',\n  ogUrl: seoData?.canonical || 'https://decisionsmaker.online',\n  twitterCard: 'summary_large_image',\n  twitterTitle: seoData?.title || 'Yes or No Decision Maker - Quick Binary Choice Wheel',\n  twitterDescription: seoData?.description || 'Make quick yes or no decisions with our simple decision wheel.',\n})\n\nuseHead({\n  link: [\n    {\n      rel: 'canonical',\n      href: seoData?.canonical || 'https://decisionsmaker.online'\n    }\n  ]\n})\n\n// 结构化数据\nuseJsonld({\n  '@context': 'https://schema.org',\n  '@type': 'WebApplication',\n  name: 'DecisionsMaker Online',\n  description: 'Interactive decision making wheel for quick and fair choices',\n  url: 'https://decisionsmaker.online',\n  applicationCategory: 'UtilityApplication',\n  operatingSystem: 'Any',\n  offers: {\n    '@type': 'Offer',\n    price: '0',\n    priceCurrency: 'USD'\n  },\n  creator: {\n    '@type': 'Organization',\n    name: 'DecisionsMaker Online'\n  }\n})\n\n// 页面配置\ndefinePageMeta({\n  layout: 'default',\n  prerender: true\n})\n</script>\n\n<style scoped>\n/* 页面特定样式 */\n.prose {\n  color: inherit;\n}\n\n.prose p {\n  margin-bottom: 1rem;\n}\n\n.prose ul {\n  margin: 1rem 0;\n  padding-left: 1.5rem;\n}\n\n.prose li {\n  margin-bottom: 0.5rem;\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  h1 {\n    font-size: 1.5rem;\n  }\n  \n  .grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n/* 深色模式下的文章样式 */\n.dark .prose {\n  color: #d1d5db;\n}\n\n.dark .prose h2 {\n  color: #f9fafb;\n}\n\n/* 加载状态 */\n.loading {\n  opacity: 0.7;\n  pointer-events: none;\n}\n\n/* 错误状态 */\n.error {\n  color: #ef4444;\n  text-align: center;\n  padding: 2rem;\n}\n</style>\n"], "version": 3}