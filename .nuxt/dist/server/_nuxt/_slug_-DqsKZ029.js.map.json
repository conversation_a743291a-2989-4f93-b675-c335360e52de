{"file": "_slug_-DqsKZ029.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAoDA,UAAM,QAAQ,SAAA;AACd,UAAM,OAAO,MAAM,OAAO;AAG1B,QAAI,CAAC,kBAAkB,IAAI,GAAG;AAC5B,YAAM,YAAY;AAAA,QAChB,YAAY;AAAA,QACZ,eAAe;AAAA,MAAA,CAChB;AAAA,IAAA;AAIH,UAAM,EAAE,KAAA,KAAS,CAAA,QAAA,SAAA,IAAAA,iBAAA,MAAM,SAA4B,mBAAmB,IAAI,IAAI;AAAA,MAC5E,QAAQ;AAAA,MACR,SAAS,OAAO;AAAA,QACd,cAAc;AAAA,QACd,aAAa,CAAA;AAAA,QACb,SAAS;AAAA,MAAA;AAAA,IACX,GACF,aAAC,CAAA;AAGD,QAAI,CAAC,KAAK,MAAM,WAAW,CAAC,KAAK,MAAM,cAAc;AACnD,UAAI;AACF,cAAM,cAAa,CAAA,QAAA,SAAA,IAAAA,iBAAA,MAAM,uBAAuB,IAAI,CAAA;AACpD,aAAK,QAAQ;AAAA,UACX,cAAc,WAAW;AAAA,UACzB,aAAa,WAAW;AAAA,UACxB,SAAS;AAAA,UACT,SAAS;AAAA,QAAA;AAAA,MACX,SACO,OAAO;AACd,cAAM,YAAY;AAAA,UAChB,YAAY;AAAA,UACZ,eAAe;AAAA,QAAA,CAChB;AAAA,MAAA;AAAA,IACH;AAIF,UAAM,QAAQ,SAAS,MAAM,KAAK,MAAM,YAAa;AACrD,UAAM,cAAc,SAAS,MAAM,KAAK,MAAM,WAAW;AAGzD,UAAM,UAAU,gBAAgB,IAAI;AAEpC,eAAW;AAAA,MACT,QAAO,mCAAS,UAAS,MAAM,MAAM,IAAI;AAAA,MACzC,cAAa,mCAAS,gBAAe,MAAM,MAAM,IAAI;AAAA,MACrD,WAAU,mCAAS,aAAY,MAAM,MAAM,IAAI,SAAS,KAAK,IAAI;AAAA,MACjE,UAAS,mCAAS,UAAS,MAAM,MAAM,IAAI;AAAA,MAC3C,gBAAe,mCAAS,gBAAe,MAAM,MAAM,IAAI;AAAA,MACvD,UAAS,mCAAS,YAAW;AAAA,MAC7B,QAAO,mCAAS,cAAa,iCAAiC,IAAI;AAAA,MAClE,aAAa;AAAA,MACb,eAAc,mCAAS,UAAS,MAAM,MAAM,IAAI;AAAA,MAChD,qBAAoB,mCAAS,gBAAe,MAAM,MAAM,IAAI;AAAA,IAAA,CAC7D;AAED,YAAQ;AAAA,MACN,MAAM;AAAA,QACJ;AAAA,UACE,KAAK;AAAA,UACL,OAAM,mCAAS,cAAa,iCAAiC,IAAI;AAAA,QAAA;AAAA,MACnE;AAAA,IACF,CACD;AAGD,cAAU;AAAA,MACR,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,MAAM,MAAM,MAAM;AAAA,MAClB,aAAa,MAAM,MAAM,IAAI;AAAA,MAC7B,KAAK,iCAAiC,IAAI;AAAA,MAC1C,qBAAqB;AAAA,MACrB,iBAAiB;AAAA,MACjB,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,OAAO;AAAA,QACP,eAAe;AAAA,MAAA;AAAA,MAEjB,SAAS;AAAA,QACP,SAAS;AAAA,QACT,MAAM;AAAA,MAAA;AAAA,IACR,CACD;;;;;;AAjIY,YAAA,qTAAAC,eAAAC,MAAA,KAAA,EAAM,IAAI,CAAA,gFAAA;;QAKV,kBAAgBA,MAAA,KAAA,EAAM;AAAA,QACtB,YAAUA,MAAA,KAAA,EAAM;AAAA,QAChB,OAAOA,MAAA,KAAA,EAAM;AAAA,QACb,gBAAcA,MAAA,WAAA;AAAA,MAAA;;AAOb,UAAAA,MAAA,KAAA,EAAM,YAAYA,aAAM,SAAS,SAAM,GAAA;;AAKdC,sBAAAD,MAAA,KAAA,EAAM,UAAQ,CAAjC,SAAS,UAAK;2OAKjBD,eAAA,QAAQ,KAAK,0GAIR,QAAQ,WAAO;;;;;;;;;;;;;;;;;", "names": ["_withAsyncContext", "_ssrInterpolate", "_unref", "_ssrRenderList"], "sources": ["../../../../pages/[slug].vue"], "sourcesContent": ["<template>\n  <div>\n    <!-- 页面头部 -->\n    <AppHeader />\n    \n    <!-- 主要内容 -->\n    <div class=\"min-h-screen py-4 bg-white dark:bg-gray-900 overflow-hidden\" id=\"wheelContainer\">\n      <div class=\"h-full flex flex-col items-center max-w-full\">\n        <h1 class=\"text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100 flex-shrink-0\">\n          {{ wheel.name }}\n        </h1>\n        <div class=\"flex flex-col items-center flex-grow w-full\">\n          <!-- 交互式转盘组件 -->\n          <WheelInteractive\n            :initial-prizes=\"wheel.prizes\"\n            :wheel-id=\"wheel.slug\"\n            :title=\"wheel.name\"\n            :other-wheels=\"otherWheels\"\n          />\n        </div>\n      </div>\n      \n      <!-- 文章内容 -->\n      <div \n        v-if=\"wheel.articles && wheel.articles.length > 0\"\n        class=\"max-w-4xl mx-auto px-4 mt-12\"\n      >\n        <div class=\"grid gap-8 md:grid-cols-2\">\n          <article\n            v-for=\"(article, index) in wheel.articles\"\n            :key=\"index\"\n            class=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700\"\n          >\n            <h2 class=\"text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100\">\n              {{ article.title }}\n            </h2>\n            <div \n              class=\"prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300\"\n              v-html=\"article.content\"\n            />\n          </article>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { WheelPageResponse } from '~/types'\nimport { getServerWheelPageData, getWheelSeoData, validateWheelSlug } from '~/utils/server-api'\n\n// 获取路由参数\nconst route = useRoute()\nconst slug = route.params.slug as string\n\n// 验证 slug 是否存在\nif (!validateWheelSlug(slug)) {\n  throw createError({\n    statusCode: 404,\n    statusMessage: 'Wheel not found'\n  })\n}\n\n// 服务端数据获取\nconst { data } = await useFetch<WheelPageResponse>(`/api/wheel-page/${slug}`, {\n  server: true,\n  default: () => ({\n    currentWheel: null,\n    otherWheels: [],\n    success: false\n  })\n})\n\n// 如果数据获取失败，使用服务端备用数据\nif (!data.value.success || !data.value.currentWheel) {\n  try {\n    const serverData = await getServerWheelPageData(slug)\n    data.value = {\n      currentWheel: serverData.wheel,\n      otherWheels: serverData.otherWheels,\n      success: true,\n      message: 'Data loaded from server'\n    }\n  } catch (error) {\n    throw createError({\n      statusCode: 404,\n      statusMessage: 'Wheel not found'\n    })\n  }\n}\n\n// 解构数据\nconst wheel = computed(() => data.value.currentWheel!)\nconst otherWheels = computed(() => data.value.otherWheels)\n\n// SEO 配置\nconst seoData = getWheelSeoData(slug)\n\nuseSeoMeta({\n  title: seoData?.title || wheel.value.seo.title,\n  description: seoData?.description || wheel.value.seo.description,\n  keywords: seoData?.keywords || wheel.value.seo.keywords.join(', '),\n  ogTitle: seoData?.title || wheel.value.seo.title,\n  ogDescription: seoData?.description || wheel.value.seo.description,\n  ogImage: seoData?.ogImage || 'https://decisionsmaker.online/og-image.png',\n  ogUrl: seoData?.canonical || `https://decisionsmaker.online/${slug}`,\n  twitterCard: 'summary_large_image',\n  twitterTitle: seoData?.title || wheel.value.seo.title,\n  twitterDescription: seoData?.description || wheel.value.seo.description,\n})\n\nuseHead({\n  link: [\n    {\n      rel: 'canonical',\n      href: seoData?.canonical || `https://decisionsmaker.online/${slug}`\n    }\n  ]\n})\n\n// 结构化数据\nuseJsonld({\n  '@context': 'https://schema.org',\n  '@type': 'WebApplication',\n  name: wheel.value.name,\n  description: wheel.value.seo.description,\n  url: `https://decisionsmaker.online/${slug}`,\n  applicationCategory: 'UtilityApplication',\n  operatingSystem: 'Any',\n  offers: {\n    '@type': 'Offer',\n    price: '0',\n    priceCurrency: 'USD'\n  },\n  creator: {\n    '@type': 'Organization',\n    name: 'DecisionsMaker Online'\n  }\n})\n\n// 页面配置\ndefinePageMeta({\n  layout: 'default',\n  prerender: true\n})\n</script>\n\n<style scoped>\n/* 页面特定样式 */\n.prose {\n  color: inherit;\n}\n\n.prose p {\n  margin-bottom: 1rem;\n}\n\n.prose ul {\n  margin: 1rem 0;\n  padding-left: 1.5rem;\n}\n\n.prose li {\n  margin-bottom: 0.5rem;\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  h1 {\n    font-size: 1.5rem;\n  }\n  \n  .grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n/* 深色模式下的文章样式 */\n.dark .prose {\n  color: #d1d5db;\n}\n\n.dark .prose h2 {\n  color: #f9fafb;\n}\n\n/* 加载状态 */\n.loading {\n  opacity: 0.7;\n  pointer-events: none;\n}\n\n/* 错误状态 */\n.error {\n  color: #ef4444;\n  text-align: center;\n  padding: 2rem;\n}\n</style>\n"], "version": 3}