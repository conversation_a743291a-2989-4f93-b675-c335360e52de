import { u as useFetch, g as getServerDefaultWheel, a as getWheelSeoData, _ as __nuxt_component_0 } from "./fetch-XUqwCWpD.js";
import { defineComponent, withAsyncContext, computed, resolveComponent, unref, useSSRContext } from "vue";
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList } from "vue/server-renderer";
import { u as useSeoMeta, a as useHead, _ as _export_sfc } from "../server.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/hookable/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/ohash/dist/index.mjs";
import "@vue/shared";
import "/Users/<USER>/Webs/makechoice/node_modules/perfect-debounce/dist/index.mjs";
import "ofetch";
import "#internal/nuxt/paths";
import "/Users/<USER>/Webs/makechoice/node_modules/unctx/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/h3/dist/index.mjs";
import "vue-router";
import "/Users/<USER>/Webs/makechoice/node_modules/radix3/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/defu/dist/defu.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/ufo/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/@unhead/vue/dist/index.mjs";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  async setup(__props) {
    let __temp, __restore;
    const { data } = ([__temp, __restore] = withAsyncContext(() => useFetch("/api/wheel-page", {
      server: true,
      default: () => ({
        currentWheel: null,
        otherWheels: [],
        success: false
      })
    }, "$77hSeXQoip")), __temp = await __temp, __restore(), __temp);
    if (!data.value.success || !data.value.currentWheel) {
      const serverData = ([__temp, __restore] = withAsyncContext(() => getServerDefaultWheel()), __temp = await __temp, __restore(), __temp);
      data.value = {
        currentWheel: serverData.defaultWheel,
        otherWheels: serverData.otherWheels,
        success: true,
        message: "Data loaded from server"
      };
    }
    const pageData = computed(() => ({
      defaultWheel: data.value.currentWheel,
      otherWheels: data.value.otherWheels
    }));
    const seoData = getWheelSeoData();
    useSeoMeta({
      title: (seoData == null ? void 0 : seoData.title) || "Yes or No Decision Maker - Quick Binary Choice Wheel",
      description: (seoData == null ? void 0 : seoData.description) || "Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.",
      keywords: (seoData == null ? void 0 : seoData.keywords) || "yes no decision, binary choice, decision maker, quick decision",
      ogTitle: (seoData == null ? void 0 : seoData.title) || "Yes or No Decision Maker - Quick Binary Choice Wheel",
      ogDescription: (seoData == null ? void 0 : seoData.description) || "Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.",
      ogImage: (seoData == null ? void 0 : seoData.ogImage) || "https://decisionsmaker.online/og-image.png",
      ogUrl: (seoData == null ? void 0 : seoData.canonical) || "https://decisionsmaker.online",
      twitterCard: "summary_large_image",
      twitterTitle: (seoData == null ? void 0 : seoData.title) || "Yes or No Decision Maker - Quick Binary Choice Wheel",
      twitterDescription: (seoData == null ? void 0 : seoData.description) || "Make quick yes or no decisions with our simple decision wheel."
    });
    useHead({
      link: [
        {
          rel: "canonical",
          href: (seoData == null ? void 0 : seoData.canonical) || "https://decisionsmaker.online"
        }
      ]
    });
    useJsonld({
      "@context": "https://schema.org",
      "@type": "WebApplication",
      name: "DecisionsMaker Online",
      description: "Interactive decision making wheel for quick and fair choices",
      url: "https://decisionsmaker.online",
      applicationCategory: "UtilityApplication",
      operatingSystem: "Any",
      offers: {
        "@type": "Offer",
        price: "0",
        priceCurrency: "USD"
      },
      creator: {
        "@type": "Organization",
        name: "DecisionsMaker Online"
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_AppHeader = resolveComponent("AppHeader");
      const _component_WheelInteractive = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(_attrs)} data-v-4cc4d54e>`);
      _push(ssrRenderComponent(_component_AppHeader, null, null, _parent));
      _push(`<div class="min-h-screen py-4 bg-white dark:bg-gray-900 overflow-hidden" id="wheelContainer" data-v-4cc4d54e><div class="h-full flex flex-col items-center max-w-full" data-v-4cc4d54e><h1 class="text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100 flex-shrink-0" data-v-4cc4d54e>${ssrInterpolate(unref(pageData).defaultWheel.name)}</h1><div class="flex flex-col items-center flex-grow w-full" data-v-4cc4d54e>`);
      _push(ssrRenderComponent(_component_WheelInteractive, {
        "initial-prizes": unref(pageData).defaultWheel.prizes,
        "wheel-id": unref(pageData).defaultWheel.slug,
        title: unref(pageData).defaultWheel.name,
        "other-wheels": unref(pageData).otherWheels
      }, null, _parent));
      _push(`</div></div>`);
      if (unref(pageData).defaultWheel.articles && unref(pageData).defaultWheel.articles.length > 0) {
        _push(`<div class="max-w-4xl mx-auto px-4 mt-12" data-v-4cc4d54e><div class="grid gap-8 md:grid-cols-2" data-v-4cc4d54e><!--[-->`);
        ssrRenderList(unref(pageData).defaultWheel.articles, (article, index2) => {
          _push(`<article class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700" data-v-4cc4d54e><h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100" data-v-4cc4d54e>${ssrInterpolate(article.title)}</h2><div class="prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300" data-v-4cc4d54e>${article.content ?? ""}</div></article>`);
        });
        _push(`<!--]--></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-4cc4d54e"]]);
export {
  index as default
};
//# sourceMappingURL=index-E5rCnlnI.js.map
