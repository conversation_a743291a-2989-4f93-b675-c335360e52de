import { defineComponent, mergeProps, useSSRContext } from "vue";
import { ssrRenderAttrs, ssrRenderSlot } from "vue/server-renderer";
import { _ as _export_sfc } from "../server.mjs";
import "ofetch";
import "#internal/nuxt/paths";
import "/Users/<USER>/Webs/makechoice/node_modules/hookable/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/unctx/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/h3/dist/index.mjs";
import "vue-router";
import "/Users/<USER>/Webs/makechoice/node_modules/radix3/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/defu/dist/defu.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/ufo/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/@unhead/vue/dist/index.mjs";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "default",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors" }, _attrs))} data-v-b07fc1e6>`);
      ssrRenderSlot(_ctx.$slots, "default", {}, null, _push, _parent);
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("layouts/default.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _default = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-b07fc1e6"]]);
export {
  _default as default
};
//# sourceMappingURL=default-DO4bbscw.js.map
