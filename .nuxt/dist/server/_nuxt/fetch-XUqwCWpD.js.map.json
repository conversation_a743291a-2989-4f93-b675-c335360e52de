{"file": "fetch-XUqwCWpD.js", "mappings": ";;;;;;;AAKO,SAAS,gBAAgB,SAAS;;AAIvC,wBAAY,WAAA;AACZ,UAAO,aAAQ,eAAR,mBAAoB;AAC7B;AA2BO,SAAS,kBAAkB;;AAIhC,WAAO,qBAAA,MAAA,mBAAmB,WAAU,WAAW;AACjD;;;;;;;;;;;ACrBA,UAAM,QAAQ;AAMd,UAAM,SAAS,IAAa,CAAC,GAAG,MAAM,aAAa,CAAC;AAGpD,UAAM,MAAM,MAAM,eAAe,CAAC,cAAc;AAC9C,aAAO,QAAQ,CAAC,GAAG,SAAS;AAAA,IAAA,GAC3B,EAAE,WAAW,MAAM;AAGtB,UAAM,qBAAqB,CAAC,cAAuB;AACjD,aAAO,QAAQ,CAAC,GAAG,SAAS;AAAA,IAAA;AAI9B,aAAa;AAAA,MACX,WAAW,MAAM,OAAO;AAAA,MACxB,WAAW,CAAC,cAAuB;AACjC,eAAO,QAAQ,CAAC,GAAG,SAAS;AAAA,MAAA;AAAA,MAE9B,aAAa,MAAM;AACjB,eAAO,QAAQ,CAAC,GAAG,MAAM,aAAa;AAAA,MAAA;AAAA,IACxC,CACD;;;AAhDM,YAAA,OAAAA,eAAAC,WAAA,EAAA,OAAM,uBAAmB,MAAA,CAAA,CAAA,mBAAA;;QAEzB,QAAQC,MAAA,MAAA;AAAA,QACR,YAAUC,KAAAA;AAAAA,QACV,OAAOC,KAAAA;AAAAA,QACP,gBAAcC,KAAAA;AAAAA,QACd,gBAAe;AAAA,MAAA;;;;;;;;;;;;ACLf,MAAM,eAA8B;AAAA,EACzC;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,QAAQ;AAAA,MACN,EAAE,MAAM,OAAO,OAAO,UAAA;AAAA,MACtB,EAAE,MAAM,MAAM,OAAO,UAAA;AAAA,IAAU;AAAA,IAEjC,KAAK;AAAA,MACH,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU,CAAC,mBAAmB,iBAAiB,kBAAkB,gBAAgB;AAAA,IAAA;AAAA,IAEnF,UAAU;AAAA,MACR;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAA;AAAA,MAWX;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAA;AAAA,IAWX;AAAA,EACF;AAAA,EAEF;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,MACxB,EAAE,MAAM,UAAU,OAAO,UAAA;AAAA,MACzB,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,MACxB,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,MACxB,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,MACxB,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,MACxB,EAAE,MAAM,YAAY,OAAO,UAAA;AAAA,MAC3B,EAAE,MAAM,WAAW,OAAO,UAAA;AAAA,IAAU;AAAA,IAEtC,KAAK;AAAA,MACH,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU,CAAC,eAAe,iBAAiB,eAAe,qBAAqB,YAAY;AAAA,IAAA;AAAA,IAE7F,UAAU;AAAA,MACR;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAA;AAAA,IAUX;AAAA,EACF;AAAA,EAEF;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,EAAE,MAAM,UAAU,OAAO,UAAA;AAAA,MACzB,EAAE,MAAM,UAAU,OAAO,UAAA;AAAA,MACzB,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,MACxB,EAAE,MAAM,UAAU,OAAO,UAAA;AAAA,MACzB,EAAE,MAAM,WAAW,OAAO,UAAA;AAAA,MAC1B,EAAE,MAAM,UAAU,OAAO,UAAA;AAAA,MACzB,EAAE,MAAM,YAAY,OAAO,UAAA;AAAA,MAC3B,EAAE,MAAM,eAAe,OAAO,UAAA;AAAA,IAAU;AAAA,IAE1C,KAAK;AAAA,MACH,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU,CAAC,gBAAgB,cAAc,eAAe,iBAAiB,gBAAgB;AAAA,IAAA;AAAA,IAE3F,UAAU;AAAA,MACR;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAA;AAAA,IAUX;AAAA,EACF;AAAA,EAEF;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,EAAE,MAAM,UAAU,OAAO,UAAA;AAAA,MACzB,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,MACxB,EAAE,MAAM,UAAU,OAAO,UAAA;AAAA,MACzB,EAAE,MAAM,YAAY,OAAO,UAAA;AAAA,MAC3B,EAAE,MAAM,QAAQ,OAAO,UAAA;AAAA,MACvB,EAAE,MAAM,UAAU,OAAO,UAAA;AAAA,MACzB,EAAE,MAAM,cAAc,OAAO,UAAA;AAAA,MAC7B,EAAE,MAAM,aAAa,OAAO,UAAA;AAAA,IAAU;AAAA,IAExC,KAAK;AAAA,MACH,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU,CAAC,sBAAsB,oBAAoB,cAAc,iBAAiB,iBAAiB;AAAA,IAAA;AAAA,IAEvG,UAAU;AAAA,MACR;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAA;AAAA,IAUX;AAAA,EACF;AAAA,EAEF;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,MACxB,EAAE,MAAM,OAAO,OAAO,UAAA;AAAA,MACtB,EAAE,MAAM,WAAW,OAAO,UAAA;AAAA,MAC1B,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,MACxB,EAAE,MAAM,OAAO,OAAO,UAAA;AAAA,MACtB,EAAE,MAAM,SAAS,OAAO,UAAA;AAAA,IAAU;AAAA,IAEpC,KAAK;AAAA,MACH,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU,CAAC,eAAe,oBAAoB,wBAAwB,kBAAkB,cAAc;AAAA,IAAA;AAAA,IAExG,UAAU;AAAA,MACR;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAA;AAAA,IAUX;AAAA,EACF;AAEJ;AAGO,SAAS,gBAAgB,MAAuC;AACrE,SAAO,aAAa,KAAK,CAAA,UAAS,MAAM,SAAS,IAAI;AACvD;AAGO,SAAS,kBAA+B;AAC7C,SAAO,aAAa,KAAK,CAAA,UAAS,MAAM,SAAS,KAAK,aAAa,CAAC;AACtE;AAGO,SAAS,eAAe;AAC7B,SAAO,aAAa,IAAI,CAAA,WAAU;AAAA,IAChC,IAAI,MAAM;AAAA,IACV,MAAM,MAAM;AAAA,IACZ,MAAM,MAAM;AAAA,IACZ,aAAa,MAAM,IAAI;AAAA,EAAA,EACvB;AACJ;ACnMA,eAAsB,uBAAuB,MAG1C;AACD,MAAI;AAEF,UAAM,QAAQ,gBAAgB,IAAI;AAElC,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MAAM,oBAAoB,IAAI,aAAa;AAAA,IAAA;AAGvD,UAAM,YAAY,aAAA;AAClB,UAAM,cAAc,UAAU,OAAO,CAAA,MAAK,EAAE,SAAS,IAAI;AAEzD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IAAA;AAAA,EACF,SACO,OAAO;AACd,YAAQ,MAAM,0CAA0C,KAAK;AAC7D,UAAM;AAAA,EAAA;AAEV;AAKA,eAAsB,wBAGnB;AACD,MAAI;AACF,UAAM,eAAe,gBAAA;AACrB,UAAM,YAAY,aAAA;AAClB,UAAM,cAAc,UAAU,OAAO,OAAK,EAAE,SAAS,aAAa,IAAI;AAEtE,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IAAA;AAAA,EACF,SACO,OAAO;AACd,YAAQ,MAAM,wCAAwC,KAAK;AAC3D,UAAM;AAAA,EAAA;AAEV;AAiBO,SAAS,kBAAkB,MAAuB;AACvD,SAAO,CAAC,CAAC,gBAAgB,IAAI;AAC/B;AAKO,SAAS,gBAAgB,MAAe;AAC7C,MAAI;AACF,UAAM,QAAQ,OAAO,gBAAgB,IAAI,IAAI,gBAAA;AAE7C,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IAAA;AAGT,WAAO;AAAA,MACL,OAAO,MAAM,IAAI;AAAA,MACjB,aAAa,MAAM,IAAI;AAAA,MACvB,UAAU,MAAM,IAAI,SAAS,KAAK,IAAI;AAAA,MACtC,WAAW,OAAO,iCAAiC,IAAI,KAAK;AAAA,MAC5D,SAAS;AAAA,IAAA;AAAA,EACX,SACO,OAAO;AACd,YAAQ,MAAM,iCAAiC,KAAK;AACpD,WAAO;AAAA,EAAA;AAEX;ACjGe,gBAAgB;AAAA,EAC7B,MAAM;AAAA,EACN,SAAS;AACP,WAAO,mBAAmB,KAAK;AAAA,EACnC;AACA,CAAC;ACDM,MAAM,mBAAmB,OAAO,IAAI,kBAAkB;AAE9C,gBAAgB;AAAA,EAC7B,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,CAAC,YAAY,eAAe,kBAAkB,aAAa;AAAA,EAClE,MAAM,OAAO,EAAE,OAAO,MAAK,GAAI;AAC7B,UAAM,UAAU,WAAW,KAAK;AAShC,UAAM,KAAK,mBAAkB;AAC7B,QAAI,IAAI;AACN,SAAG,kBAAkB;AAAA,IAC3B;AACI,YAAQ,kBAAkB,IAAI;AAC9B,WAAO,MAAM;;AACX,UAAI,QAAQ,OAAO;AACjB,cAAM,UAAS,WAAM,YAAN;AACf,YAAI,UAAU,OAAO,WAAW,GAAG;AACjC,iBAAO,CAAC,WAAW,OAAO,CAAC,GAAG,KAAK,CAAC;AAAA,QAC9C;AACQ,eAAO;AAAA,MACf;AACM,YAAM,OAAO,MAAM,YAAY,MAAM;AACrC,UAAI,MAAM;AACR,eAAO,EAAE,IAAI;AAAA,MACrB;AACM,YAAM,cAAc,MAAM,YAAY,MAAM,eAAe;AAC3D,YAAM,cAAc,MAAM,eAAe,MAAM,kBAAkB;AACjE,aAAO,mBAAmB,aAAa,OAAO,WAAW;AAAA,IAC/D;AAAA,EACA;AACA,CAAC;ACjCD,MAAM,UAAU,CAAC,WAAW,WAAW,WAAW,WAAW;AACtD,SAAS,gBAAgB,MAAM;;AACpC,QAAM,UAAU,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,WAAW,KAAK,IAAG,IAAK;AACzE,MAAI,iBAAiB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG;AACtC,SAAK,QAAQ,OAAO;AAAA,EACxB;AACE,MAAI,CAAC,MAAM,UAAU,UAAU,CAAA,CAAE,IAAI;AACrC,QAAM,MAAM,SAAS,MAAM,QAAQ,IAAI,CAAC;AACxC,MAAI,OAAO,IAAI,UAAU,UAAU;AACjC,UAAM,IAAI,UAAU,6CAA6C;AAAA,EACrE;AACE,MAAI,OAAO,aAAa,YAAY;AAClC,UAAM,IAAI,UAAU,mDAAmD;AAAA,EAC3E;AACE,QAAM,UAAU,WAAU;AAC1B,UAAQ,WAAR,QAAQ,SAAW;AACnB,UAAQ,YAAR,QAAQ,UAAY;AACpB,UAAQ,kBAAR,QAAQ,gBAAkB;AAC1B,UAAQ,SAAR,QAAQ,OAAS;AACjB,UAAQ,cAAR,QAAQ,YAAc;AACtB,UAAQ,SAAR,QAAQ,OAAS,kBAAkB;AACnC,UAAQ,WAAR,QAAQ,SAAW;AACE,UAAQ,iBAAiB;AAI1B,UAAQ,WAAW,IAAI,KAAK;AA2BhD,QAAM,sBAAsB,EAAE,OAAO,WAAW,QAAQ,QAAQ,OAAM;AACtE,MAAI,GAAC,aAAQ,WAAW,IAAI,KAAK,MAA5B,mBAA+B,QAAO;AACzC,wBAAoB,aAAa,QAAQ,cAAc,IAAI,OAAO,SAAS,EAAE,OAAO,WAAW;AAC/F,YAAQ,WAAW,IAAI,KAAK,IAAI,gBAAgB,SAAS,IAAI,OAAO,UAAU,SAAS,oBAAoB,UAAU;AAAA,EACzH;AACE,QAAM,YAAY,QAAQ,WAAW,IAAI,KAAK;AAC9C,YAAU;AACV,QAAM,eAAe,MAAM,QAAQ,WAAW,IAAI,KAAK,EAAE,QAAQ,mBAAmB;AACpF,QAAM,gBAAgB,QAAQ,WAAW,SAAS,QAAQ,QAAQ;AAClE,MAA0B,iBAAiB,QAAQ,WAAW;AAC5D,UAAM,UAAU,aAAY;AAC5B,QAAI,mBAAkB,GAAI;AACxB,uBAAiB,MAAM,OAAO;AAAA,IACpC,OAAW;AACL,cAAQ,KAAK,eAAe,YAAY;AACtC,cAAM;AAAA,MACd,CAAO;AAAA,IACP;AAAA,EACA;AAmEE,QAAM,cAAc;AAAA,IAClB,MAAM,oBAAoB;;AAAM,cAAAC,MAAA,QAAQ,WAAW,IAAI,KAAK,MAA5B,gBAAAA,IAA+B;AAAA,KAAI;AAAA,IACnE,SAAS,oBAAoB;;AAAM,cAAAA,MAAA,QAAQ,WAAW,IAAI,KAAK,MAA5B,gBAAAA,IAA+B;AAAA,KAAO;AAAA,IACzE,QAAQ,oBAAoB;;AAAM,cAAAA,MAAA,QAAQ,WAAW,IAAI,KAAK,MAA5B,gBAAAA,IAA+B;AAAA,KAAM;AAAA,IACvE,OAAO,oBAAoB;;AAAM,cAAAA,MAAA,QAAQ,WAAW,IAAI,KAAK,MAA5B,gBAAAA,IAA+B;AAAA,KAAK;AAAA,IACrE,SAAS,IAAI,UAAU,QAAQ,WAAW,IAAI,KAAK,EAAE,QAAQ,GAAG,KAAK;AAAA,IACrE,SAAS,IAAI,UAAU,QAAQ,WAAW,IAAI,KAAK,EAAE,QAAQ,GAAG,KAAK;AAAA,IACrE,OAAO,MAAM,mBAAmB,SAAS,IAAI,KAAK;AAAA,EACtD;AACE,QAAM,mBAAmB,QAAQ,QAAQ,QAAQ,mBAAmB,IAAI,KAAK,CAAC,EAAE,KAAK,MAAM,WAAW;AACtG,SAAO,OAAO,kBAAkB,WAAW;AAC3C,SAAO;AACT;AACA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,SAAS;AAAA,IACd,MAAM;;AACJ,cAAO,YAAM,MAAN,mBAAU;AAAA,IACvB;AAAA,IACI,IAAI,OAAO;AACT,YAAM,OAAO,OAAM;AACnB,UAAI,MAAM;AACR,aAAK,QAAQ;AAAA,MACrB;AAAA,IACA;AAAA,EACA,CAAG;AACH;AAYA,SAAS,iBAAiB,cAAc,SAAS;AAC/C,MAAI,OAAO,iBAAiB,UAAU;AACpC,WAAO;AAAA,EACX;AACE,MAAI,OAAO,iBAAiB,YAAY,iBAAiB,MAAM;AAC7D,WAAO;AAAA,EACX;AACE,MAAI,OAAO,iBAAiB,cAAc,OAAO,YAAY,YAAY;AACvE,WAAO;AAAA,EACX;AACE,SAAO;AACT;AAiDA,SAAS,mBAAmB,SAAS,KAAK;AACxC,MAAI,OAAO,QAAQ,QAAQ,MAAM;AAC/B,YAAQ,QAAQ,KAAK,GAAG,IAAI;AAAA,EAChC;AACE,MAAI,OAAO,QAAQ,QAAQ,SAAS;AAClC,YAAQ,QAAQ,QAAQ,GAAG,IAAI,kBAAkB;AAAA,EACrD;AACE,MAAI,QAAQ,WAAW,GAAG,GAAG;AAC3B,YAAQ,WAAW,GAAG,EAAE,KAAK,QAAoC;AACjE,YAAQ,WAAW,GAAG,EAAE,MAAM,QAAQ,kBAAkB;AACnC;AACnB,cAAQ,WAAW,GAAG,EAAE,QAAQ,QAAQ;AAAA,IAC9C;AACI,YAAQ,WAAW,GAAG,EAAE,OAAO,QAAQ;AAAA,EAC3C;AACE,MAAI,OAAO,QAAQ,oBAAoB;AACrC,QAAI,QAAQ,mBAAmB,GAAG,GAAG;AACnC,cAAQ,mBAAmB,GAAG,EAAE,YAAY;AAAA,IAClD;AACI,YAAQ,mBAAmB,GAAG,IAAI;AAAA,EACtC;AACA;AACA,SAAS,KAAK,KAAK,MAAM;AACvB,QAAM,SAAS,CAAA;AACf,aAAW,OAAO,MAAM;AACtB,WAAO,GAAG,IAAI,IAAI,GAAG;AAAA,EACzB;AACE,SAAO;AACT;AAEA,SAAS,gBAAgB,SAAS,KAAK,UAAU,SAAS,mBAAmB;;AAC3E,gBAAQ,QAAQ,SAAhB,mBAAiC,kBAAkB;AACnD,QAAM,yBAAyB,QAAQ,kBAAkB;AACzD,QAAM,UAAgC,CAAC,YAAY,aAAa,GAAC,aAAQ,eAAR,mBAAoB,yBAAwB,WAAW,MAAM;AAC5H,UAAM,QAAQ,QAAQ,WAAW,sBAAsB,IAAI,GAAG;AAC9D,QAAI,OAAO;AACT,aAAO;AAAA,IACb;AACI,UAAM,UAAU,QAAQ,QAAO,EAAG,KAAK,MAAM,QAAQ,eAAe,MAAM,SAAS,OAAO,CAAC,CAAC;AAC5F,YAAQ,WAAW,sBAAsB,IAAI,KAAK,OAAO;AACzD,WAAO;AAAA,EACX;AACE,QAAM,OAAO,QAAQ,OAAO,MAAM;AAClC,QAAM,gBAAgB,qBAAqB;AAC3C,QAAM,wBAAwB,QAAQ,KAAK,oBAAoB,OAAO,SAAS;AAC7E,QAAI,CAAC,QAAQ,KAAK,SAAS,GAAG,GAAG;AAC/B,YAAM,UAAU,QAAQ,EAAE,OAAO,eAAc,CAAE;AAAA,IACvD;AAAA,EACA,CAAG;AACD,QAAM,YAAY;AAAA,IAChB,MAAM,KAAK,gBAAgB,oBAAoB,QAAQ,QAAO,CAAE;AAAA,IAChE,SAA2B,WAAW,CAAC,aAAa;AAAA,IACpD,OAAO,MAAM,QAAQ,QAAQ,SAAS,GAAG;AAAA,IACzC,QAAQ,WAAW,MAAM;AAAA,IACzB,SAAS,CAAC,OAAO,OAAO;AACtB,UAAI,QAAQ,mBAAmB,GAAG,GAAG;AACnC,YAAI,QAAQ,KAAK,UAAU,QAAQ,MAAM,GAAG;AAC1C,iBAAO,QAAQ,mBAAmB,GAAG;AAAA,QAC/C;AACQ,gBAAQ,mBAAmB,GAAG,EAAE,YAAY;AAAA,MACpD;AACM,UAA0B,KAAK,UAAU,aAAa,QAAQ,aAAa;AACzE,cAAM,aAAa,gBAAgB,OAAO,KAAK,aAAa,QAAQ,cAAc,KAAK,SAAS,EAAE,OAAO,KAAK,SAAS,iBAAgB,CAAE;AACzI,YAAI,cAAc,MAAM;AACtB,kBAAQ,QAAQ,KAAK,GAAG,IAAI,UAAU,KAAK,QAAQ;AACnD,oBAAU,MAAM,QAAQ,kBAAkB;AAC1C,oBAAU,OAAO,QAAQ;AACzB,iBAAO,QAAQ,QAAQ,UAAU;AAAA,QAC3C;AAAA,MACA;AAC2B;AACnB,kBAAU,QAAQ,QAAQ;AAAA,MAClC;AACM,gBAAU,OAAO,QAAQ;AACzB,YAAM,UAAU,IAAI;AAAA,QAClB,CAAC,SAAS,WAAW;AACnB,cAAI;AACF,oBAAQ,QAAQ,OAAO,CAAC;AAAA,UACpC,SAAmB,KAAK;AACZ,mBAAO,GAAG;AAAA,UACtB;AAAA,QACA;AAAA,MACA,EAAQ,KAAK,OAAO,YAAY;AACxB,YAAI,QAAQ,WAAW;AACrB,iBAAO,QAAQ,mBAAmB,GAAG;AAAA,QAC/C;AACQ,YAAI,SAAS;AACb,YAAI,QAAQ,WAAW;AACrB,mBAAS,MAAM,QAAQ,UAAU,OAAO;AAAA,QAClD;AACQ,YAAI,QAAQ,MAAM;AAChB,mBAAS,KAAK,QAAQ,QAAQ,IAAI;AAAA,QAC5C;AAOQ,gBAAQ,QAAQ,KAAK,GAAG,IAAI;AAC5B,kBAAU,KAAK,QAAQ;AACvB,kBAAU,MAAM,QAAQ,kBAAkB;AAC1C,kBAAU,OAAO,QAAQ;AAAA,MACjC,CAAO,EAAE,MAAM,CAAC,UAAU;AAClB,YAAI,QAAQ,WAAW;AACrB,iBAAO,QAAQ,mBAAmB,GAAG;AAAA,QAC/C;AACQ,kBAAU,MAAM,QAAQ,YAAY,KAAK;AACzC,kBAAU,KAAK,QAAQ,MAAM,QAAQ,QAAO,CAAE;AAC9C,kBAAU,OAAO,QAAQ;AAAA,MACjC,CAAO,EAAE,QAAQ,MAAM;AACf,YAAI,QAAQ,WAAW;AACrB;AAAA,QACV;AAC6B;AACnB,oBAAU,QAAQ,QAAQ;AAAA,QACpC;AACQ,eAAO,QAAQ,mBAAmB,GAAG;AAAA,MAC7C,CAAO;AACD,cAAQ,mBAAmB,GAAG,IAAI;AAClC,aAAO,QAAQ,mBAAmB,GAAG;AAAA,IAC3C;AAAA,IACI,UAAU,SAAS,IAAI,SAAS,UAAU,QAAQ,GAAG,IAAI,GAAG,GAAG,EAAE,SAAS,KAAI,CAAE;AAAA,IAChF,UAAU,QAAQ;AAAA,IAClB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAA+C;AAAA,IAC/C,MAAM,MAAM;;AACV,4BAAqB;AACrB,WAAIA,MAAA,QAAQ,WAAW,GAAG,MAAtB,gBAAAA,IAAyB,OAAO;AAClC,gBAAQ,WAAW,GAAG,EAAE,QAAQ;AAAA,MACxC;AACM,UAAuB,CAAC,wBAAwB;AAC9C,iBAAS,MAAM;;AACb,cAAI,GAACA,MAAA,QAAQ,WAAW,GAAG,MAAtB,gBAAAA,IAAyB,QAAO;AACnC,+BAAmB,SAAS,GAAG;AAC/B,sBAAU,UAAU,MAAM,QAAQ,QAAO;AACzC,sBAAU,KAAK,QAAQ,kBAAkB;AAAA,UACrD;AAAA,QACA,CAAS;AAAA,MACT;AAAA,IACA;AAAA,EACA;AACE,SAAO;AACT;AACA,MAAM,aAAa,MAAM,kBAAkB;AAC3C,MAAM,uBAAuB,CAAC,KAAK,SAAS,QAAQ;AAClD,MAAI,QAAQ,aAAa;AACvB,WAAO,QAAQ,QAAQ,KAAK,GAAG;AAAA,EACnC;AACE,MAAI,IAAI,UAAU,oBAAoB,IAAI,UAAU,gBAAgB;AAClE,WAAO,QAAQ,OAAO,KAAK,GAAG;AAAA,EAClC;AACA;ACxYO,SAAS,SAAS,SAAS,MAAM,MAAM;AAC5C,QAAM,CAAC,OAAO,CAAA,GAAI,OAAO,IAAI,OAAO,SAAS,WAAW,CAAC,CAAA,GAAI,IAAI,IAAI,CAAC,MAAM,IAAI;AAChF,QAAM,WAAW,SAAS,MAAM,QAAQ,OAAO,CAAC;AAChD,QAAM,MAAM,SAAS,MAAM,QAAQ,KAAK,GAAG,KAAK,OAAO,KAAK,CAAC,SAAS,OAAO,SAAS,UAAU,WAAW,SAAS,QAAQ,IAAI,GAAG,uBAAuB,IAAI,CAAC,CAAC,CAAC;AACjK,MAAI,CAAC,KAAK,WAAW,OAAO,SAAS,UAAU,aAAa,SAAS,MAAM,CAAC,MAAM,OAAO,SAAS,MAAM,CAAC,MAAM,MAAM;AACnH,UAAM,IAAI,MAAM,6DAA6D;AAAA,EACjF;AACE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA,MAAAC;AAAA,IACA,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACP,IAAM;AACJ,QAAM,gBAAgB,SAAS;AAAA,IAC7B,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,OAAO,KAAK,UAAU,YAAY,SAAS,KAAK;AAAA,EAC3D,CAAG;AACD,QAAM,oBAAoB;AAAA,IACxB;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA,MAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,iBAAiB,QAAQ,CAAA,IAAK,CAAC,GAAG,gBAAgB,CAAA,GAAI,aAAa;AAAA,EAC9E;AAIE,MAAiC,CAAC,WAAW;AAC3C,QAAI,eAAe,WAAW;AAC5B,wBAAkB,YAAY;AAAA,IACpC;AACI,UAAM,KAAK,cAAc,EAAE,OAAO,QAAQ,MAAM,MAAM;AACtD,UAAM,CAAC,GAAG,gBAAgB,CAAA,GAAI,aAAa,GAAG,cAAc,EAAE,OAAO,QAAQ,MAAM,KAAI,CAAE;AAAA,EAC7F;AACE,MAAI;AACJ,QAAM,YAAY,aAAa,iBAAiB,QAAQ,IAAI,QAAQ,KAAK,MAAM;;AAC7E,mDAAY,UAAZ,oCAAoB,IAAI,aAAa,0EAA0E,YAAY;AAC3H,iBAAa,OAAO,oBAAoB,cAAc,IAAI,gBAAe,IAAK,CAAA;AAC9E,UAAM,gBAAgB,QAAQ,KAAK,OAAO;AAC1C,QAAI;AACJ,QAAI,eAAe;AACjB,kBAAY,WAAW,MAAM,WAAW,MAAM,IAAI,aAAa,mCAAmC,YAAY,CAAC,GAAG,aAAa;AAC/H,iBAAW,OAAO,UAAU,MAAM,aAAa,SAAS;AAAA,IAC9D;AACI,QAAI,UAAU,KAAK,UAAU,WAAW;AACxC,QAA0B,CAAC,KAAK,QAAQ;AACtC,YAAM,eAAe,OAAO,SAAS,UAAU,YAAY,SAAS,MAAM,CAAC,MAAM,QAAQ,CAAC,QAAQ,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,EAAE,CAAC,MAAM;AAChJ,UAAI,cAAc;AAChB,kBAAU,gBAAe;AAAA,MACjC;AAAA,IACA;AACI,WAAO,QAAQ,SAAS,OAAO,EAAE,QAAQ,WAAW,QAAQ,GAAG,eAAe,EAAE,QAAQ,MAAM;AAC5F,mBAAa,SAAS;AAAA,IAC5B,CAAK;AAAA,EACL,GAAK,iBAAiB;AACpB,SAAO;AACT;AAgBA,SAAS,uBAAuB,MAAM;;AACpC,QAAM,WAAW;AAAA,MACf,aAAQ,KAAK,MAAM,MAAnB,mBAAsB,kBAAiB;AAAA,IACvC,QAAQ,KAAK,OAAO;AAAA,EACxB;AACE,aAAW,QAAQ,CAAC,KAAK,UAAU,KAAK,KAAK,GAAG;AAC9C,UAAM,MAAM,QAAQ,IAAI;AACxB,QAAI,CAAC,KAAK;AACR;AAAA,IACN;AACI,UAAM,YAAY,CAAA;AAClB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,GAAG;AAC9C,gBAAU,QAAQ,GAAG,CAAC,IAAI,QAAQ,KAAK;AAAA,IAC7C;AACI,aAAS,KAAK,SAAS;AAAA,EAC3B;AACE,MAAI,KAAK,MAAM;AACb,UAAM,QAAQ,QAAQ,KAAK,IAAI;AAC/B,QAAI,CAAC,OAAO;AACV,eAAS,KAAK,KAAK,KAAK,CAAC;AAAA,IAC/B,WAAe,iBAAiB,aAAa;AACvC,eAAS,KAAK,KAAK,OAAO,YAAY,CAAC,GAAG,IAAI,WAAW,KAAK,EAAE,QAAO,CAAE,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,SAAQ,CAAE,CAAC,CAAC,CAAC,CAAC;AAAA,IACrH,WAAe,iBAAiB,UAAU;AACpC,YAAM,MAAM,CAAA;AACZ,iBAAW,SAAS,MAAM,WAAW;AACnC,cAAM,CAAC,KAAK,GAAG,IAAI;AACnB,YAAI,GAAG,IAAI,eAAe,OAAO,IAAI,OAAO;AAAA,MACpD;AACM,eAAS,KAAK,KAAK,GAAG,CAAC;AAAA,IAC7B,WAAe,cAAc,KAAK,GAAG;AAC/B,eAAS,KAAK,KAAK,SAAS,KAAK,CAAC,CAAC;AAAA,IACzC,OAAW;AACL,UAAI;AACF,iBAAS,KAAK,KAAK,KAAK,CAAC;AAAA,MACjC,QAAc;AACN,gBAAQ,KAAK,kCAAkC,KAAK;AAAA,MAC5D;AAAA,IACA;AAAA,EACA;AACE,SAAO;AACT;", "names": ["_ssrRenderAttrs", "_mergeProps", "_unref", "wheelId", "title", "otherWheels", "_a", "pick"], "sources": ["../../../../node_modules/nuxt/dist/app/composables/ssr.js", "../../../../components/wheel/WheelInteractive.vue", "../../../../data/presetWheels.ts", "../../../../utils/server-api.ts", "../../../../node_modules/nuxt/dist/app/components/server-placeholder.js", "../../../../node_modules/nuxt/dist/app/components/client-only.js", "../../../../node_modules/nuxt/dist/app/composables/asyncData.js", "../../../../node_modules/nuxt/dist/app/composables/fetch.js"], "sourcesContent": ["import { setResponseStatus as _setR<PERSON>po<PERSON><PERSON>tatus, appendHeader, getRe<PERSON><PERSON>eader, getRequestHead<PERSON>, getR<PERSON>po<PERSON><PERSON>eader, removeR<PERSON>po<PERSON><PERSON>eader, setResponseHeader } from \"h3\";\nimport { computed, getCurrentInstance, ref } from \"vue\";\nimport { useNuxtApp } from \"../nuxt.js\";\nimport { toArray } from \"../utils.js\";\nimport { useHead } from \"./head.js\";\nexport function useRequestEvent(nuxtApp) {\n  if (import.meta.client) {\n    return;\n  }\n  nuxtApp ||= useNuxtApp();\n  return nuxtApp.ssrContext?.event;\n}\nexport function useRequestHeaders(include) {\n  if (import.meta.client) {\n    return {};\n  }\n  const event = useRequestEvent();\n  const _headers = event ? getRequestHeaders(event) : {};\n  if (!include || !event) {\n    return _headers;\n  }\n  const headers = /* @__PURE__ */ Object.create(null);\n  for (const _key of include) {\n    const key = _key.toLowerCase();\n    const header = _headers[key];\n    if (header) {\n      headers[key] = header;\n    }\n  }\n  return headers;\n}\nexport function useRequestHeader(header) {\n  if (import.meta.client) {\n    return void 0;\n  }\n  const event = useRequestEvent();\n  return event ? getRequestHeader(event, header) : void 0;\n}\nexport function useRequestFetch() {\n  if (import.meta.client) {\n    return globalThis.$fetch;\n  }\n  return useRequestEvent()?.$fetch || globalThis.$fetch;\n}\nexport function setResponseStatus(arg1, arg2, arg3) {\n  if (import.meta.client) {\n    return;\n  }\n  if (arg1 && typeof arg1 !== \"number\") {\n    return _setResponseStatus(arg1, arg2, arg3);\n  }\n  const event = useRequestEvent();\n  if (event) {\n    return _setResponseStatus(event, arg1, arg2);\n  }\n}\nexport function useResponseHeader(header) {\n  if (import.meta.client) {\n    if (import.meta.dev) {\n      return computed({\n        get: () => void 0,\n        set: () => console.warn(\"[nuxt] Setting response headers is not supported in the browser.\")\n      });\n    }\n    return ref();\n  }\n  const event = useRequestEvent();\n  return computed({\n    get() {\n      return getResponseHeader(event, header);\n    },\n    set(newValue) {\n      if (!newValue) {\n        return removeResponseHeader(event, header);\n      }\n      return setResponseHeader(event, header, newValue);\n    }\n  });\n}\nexport function prerenderRoutes(path) {\n  if (!import.meta.server || !import.meta.prerender) {\n    return;\n  }\n  const paths = toArray(path);\n  appendHeader(useRequestEvent(), \"x-nitro-prerender\", paths.map((p) => encodeURIComponent(p)).join(\", \"));\n}\nconst PREHYDRATE_ATTR_KEY = \"data-prehydrate-id\";\nexport function onPrehydrate(callback, key) {\n  if (import.meta.client) {\n    return;\n  }\n  if (typeof callback !== \"string\") {\n    throw new TypeError(\"[nuxt] To transform a callback into a string, `onPrehydrate` must be processed by the Nuxt build pipeline. If it is called in a third-party library, make sure to add the library to `build.transpile`.\");\n  }\n  const vm = getCurrentInstance();\n  if (vm && key) {\n    vm.attrs[PREHYDRATE_ATTR_KEY] ||= \"\";\n    key = \":\" + key + \":\";\n    if (!vm.attrs[PREHYDRATE_ATTR_KEY].includes(key)) {\n      vm.attrs[PREHYDRATE_ATTR_KEY] += key;\n    }\n  }\n  const code = vm && key ? `document.querySelectorAll('[${PREHYDRATE_ATTR_KEY}*=${JSON.stringify(key)}]').forEach` + callback : callback + \"()\";\n  useHead({\n    script: [{\n      key: vm && key ? key : void 0,\n      tagPosition: \"bodyClose\",\n      tagPriority: \"critical\",\n      innerHTML: code\n    }]\n  });\n  return vm && key ? vm.attrs[PREHYDRATE_ATTR_KEY] : void 0;\n}\n", "<template>\n  <div class=\"wheel-interactive\">\n    <LuckWheel\n      :prizes=\"prizes\"\n      :wheel-id=\"wheelId\"\n      :title=\"title\"\n      :other-wheels=\"otherWheels\"\n      @prizes-change=\"handlePrizesChange\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { Prize, WheelListItem } from '~/types'\n\ninterface Props {\n  initialPrizes: Prize[]\n  wheelId?: string\n  title?: string\n  otherWheels?: WheelListItem[]\n}\n\nconst props = withDefaults(defineProps<Props>(), {\n  initialPrizes: () => [],\n  otherWheels: () => []\n})\n\n// 响应式数据\nconst prizes = ref<Prize[]>([...props.initialPrizes])\n\n// 监听初始奖品变化\nwatch(() => props.initialPrizes, (newPrizes) => {\n  prizes.value = [...newPrizes]\n}, { immediate: true })\n\n// 处理奖品变化\nconst handlePrizesChange = (newPrizes: Prize[]) => {\n  prizes.value = [...newPrizes]\n}\n\n// 暴露给父组件的方法\ndefineExpose({\n  getPrizes: () => prizes.value,\n  setPrizes: (newPrizes: Prize[]) => {\n    prizes.value = [...newPrizes]\n  },\n  resetPrizes: () => {\n    prizes.value = [...props.initialPrizes]\n  }\n})\n</script>\n\n<style scoped>\n.wheel-interactive {\n  width: 100%;\n  height: 100%;\n}\n</style>\n", "import type { PresetWheel } from '~/types'\n\nexport const presetWheels: PresetWheel[] = [\n  {\n    id: 'yes-no',\n    name: 'Yes or No Decision Maker',\n    slug: 'yes-no-decision-maker',\n    isDefault: true,\n    prizes: [\n      { text: \"Yes\", color: \"#22c55e\" },\n      { text: \"No\", color: \"#ef4444\" },\n    ],\n    seo: {\n      title: 'Yes or No Decision Maker - Quick Binary Choice Wheel',\n      description: 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.',\n      keywords: ['yes no decision', 'binary choice', 'decision maker', 'quick decision']\n    },\n    articles: [\n      {\n        title: \"The Psychology of Binary Decisions\",\n        content: `\n          <p>\n            Binary decisions are fundamental to human psychology. When faced with a simple yes or no choice, \n            our brains often overthink the process, leading to decision paralysis.\n          </p>\n          <p>\n            Using a decision wheel removes the burden of choice from your conscious mind, allowing your \n            subconscious preferences to guide the outcome. This can often lead to more satisfying decisions.\n          </p>\n        `\n      },\n      {\n        title: \"When to Use Yes/No Decision Making\",\n        content: `\n          <p>\n            Yes or no decisions are perfect for:\n          </p>\n          <ul>\n            <li>Breaking ties when you're genuinely undecided</li>\n            <li>Making quick decisions when time is limited</li>\n            <li>Overcoming analysis paralysis</li>\n            <li>Adding an element of fun to routine choices</li>\n          </ul>\n        `\n      }\n    ]\n  },\n  {\n    id: 'what-to-eat',\n    name: 'What to Eat Decision Wheel',\n    slug: 'what-to-eat-decision-wheel',\n    prizes: [\n      { text: \"Pizza\", color: \"#ff6b6b\" },\n      { text: \"Burger\", color: \"#4ecdc4\" },\n      { text: \"Sushi\", color: \"#45b7d1\" },\n      { text: \"Pasta\", color: \"#96ceb4\" },\n      { text: \"Tacos\", color: \"#feca57\" },\n      { text: \"Salad\", color: \"#ff9ff3\" },\n      { text: \"Sandwich\", color: \"#54a0ff\" },\n      { text: \"Chinese\", color: \"#5f27cd\" },\n    ],\n    seo: {\n      title: 'What to Eat Decision Wheel - Food Choice Generator',\n      description: 'Cant decide what to eat? Use our food decision wheel to randomly choose from popular meal options. Perfect for indecisive foodies!',\n      keywords: ['what to eat', 'food decision', 'meal picker', 'restaurant choice', 'food wheel']\n    },\n    articles: [\n      {\n        title: \"Solving the Daily Food Dilemma\",\n        content: `\n          <p>\n            \"What should I eat?\" is one of the most common daily decisions we face. Decision fatigue from \n            constantly choosing meals can be exhausting.\n          </p>\n          <p>\n            Our food decision wheel takes the stress out of meal planning by randomly selecting from \n            popular food options, helping you discover new favorites and break out of eating routines.\n          </p>\n        `\n      }\n    ]\n  },\n  {\n    id: 'movie-night',\n    name: 'Movie Night Picker',\n    slug: 'movie-night-picker',\n    prizes: [\n      { text: \"Action\", color: \"#e74c3c\" },\n      { text: \"Comedy\", color: \"#f39c12\" },\n      { text: \"Drama\", color: \"#9b59b6\" },\n      { text: \"Horror\", color: \"#2c3e50\" },\n      { text: \"Romance\", color: \"#e91e63\" },\n      { text: \"Sci-Fi\", color: \"#3498db\" },\n      { text: \"Thriller\", color: \"#34495e\" },\n      { text: \"Documentary\", color: \"#27ae60\" },\n    ],\n    seo: {\n      title: 'Movie Night Picker - Random Movie Genre Selector',\n      description: 'End movie night arguments with our genre picker wheel. Randomly select from popular movie genres for your next film night.',\n      keywords: ['movie picker', 'film genre', 'movie night', 'what to watch', 'movie decision']\n    },\n    articles: [\n      {\n        title: \"Making Movie Night Decisions Fair\",\n        content: `\n          <p>\n            Movie night with friends or family often leads to lengthy debates about what to watch. \n            Different people have different preferences, and finding a compromise can be challenging.\n          </p>\n          <p>\n            Using a movie genre picker wheel ensures everyone has an equal chance of their preferred \n            genre being selected, making the decision process fair and fun.\n          </p>\n        `\n      }\n    ]\n  },\n  {\n    id: 'weekend-activity',\n    name: 'Weekend Activity Planner',\n    slug: 'weekend-activity-planner',\n    prizes: [\n      { text: \"Hiking\", color: \"#2ecc71\" },\n      { text: \"Beach\", color: \"#3498db\" },\n      { text: \"Museum\", color: \"#9b59b6\" },\n      { text: \"Shopping\", color: \"#e91e63\" },\n      { text: \"Park\", color: \"#27ae60\" },\n      { text: \"Movies\", color: \"#34495e\" },\n      { text: \"Restaurant\", color: \"#e67e22\" },\n      { text: \"Stay Home\", color: \"#95a5a6\" },\n    ],\n    seo: {\n      title: 'Weekend Activity Planner - Random Activity Generator',\n      description: 'Plan your perfect weekend with our activity picker wheel. Discover new activities and break out of your routine.',\n      keywords: ['weekend activities', 'activity planner', 'what to do', 'weekend ideas', 'activity picker']\n    },\n    articles: [\n      {\n        title: \"Breaking Out of Weekend Routines\",\n        content: `\n          <p>\n            Many people fall into predictable weekend routines, doing the same activities week after week. \n            While routine can be comforting, it can also lead to boredom and missed opportunities.\n          </p>\n          <p>\n            Our weekend activity planner introduces an element of randomness to your leisure time, \n            encouraging you to try new experiences and make the most of your free time.\n          </p>\n        `\n      }\n    ]\n  },\n  {\n    id: 'team-picker',\n    name: 'Team Member Picker',\n    slug: 'team-member-picker',\n    prizes: [\n      { text: \"Alice\", color: \"#ff7675\" },\n      { text: \"Bob\", color: \"#74b9ff\" },\n      { text: \"Charlie\", color: \"#00b894\" },\n      { text: \"Diana\", color: \"#fdcb6e\" },\n      { text: \"Eve\", color: \"#e17055\" },\n      { text: \"Frank\", color: \"#a29bfe\" },\n    ],\n    seo: {\n      title: 'Team Member Picker - Random Team Selection Wheel',\n      description: 'Fairly select team members, assign tasks, or pick volunteers with our random team picker wheel. Perfect for classrooms and workplaces.',\n      keywords: ['team picker', 'random selection', 'team member selector', 'fair selection', 'group picker']\n    },\n    articles: [\n      {\n        title: \"Fair Team Selection Made Easy\",\n        content: `\n          <p>\n            Whether you're a teacher assigning classroom tasks, a manager distributing work, or organizing team activities, \n            fair selection is crucial for maintaining group harmony.\n          </p>\n          <p>\n            Our team picker wheel ensures everyone has an equal chance of being selected, removing bias and \n            making the process transparent and fun.\n          </p>\n        `\n      }\n    ]\n  }\n];\n\n// 根据slug查找转盘\nexport function findWheelBySlug(slug: string): PresetWheel | undefined {\n  return presetWheels.find(wheel => wheel.slug === slug);\n}\n\n// 获取默认转盘\nexport function getDefaultWheel(): PresetWheel {\n  return presetWheels.find(wheel => wheel.isDefault) || presetWheels[0];\n}\n\n// 获取转盘列表\nexport function getWheelList() {\n  return presetWheels.map(wheel => ({\n    id: wheel.id,\n    name: wheel.name,\n    slug: wheel.slug,\n    description: wheel.seo.description\n  }));\n}\n", "/**\n * 服务端API工具函数\n */\n\nimport type { WheelPageResponse, PresetWheel, WheelListItem } from '~/types'\nimport { findWheelBySlug, getDefaultWheel, getWheelList } from '~/data/presetWheels'\n\n/**\n * 获取转盘页面数据（服务端）\n */\nexport async function getServerWheelPageData(slug: string): Promise<{\n  wheel: PresetWheel;\n  otherWheels: WheelListItem[];\n}> {\n  try {\n    // 在服务端直接使用数据，不需要API调用\n    const wheel = findWheelBySlug(slug)\n    \n    if (!wheel) {\n      throw new Error(`Wheel with slug \"${slug}\" not found`)\n    }\n    \n    const allWheels = getWheelList()\n    const otherWheels = allWheels.filter(w => w.slug !== slug)\n    \n    return {\n      wheel,\n      otherWheels\n    }\n  } catch (error) {\n    console.error('Error fetching server wheel page data:', error)\n    throw error\n  }\n}\n\n/**\n * 获取默认转盘数据（服务端）\n */\nexport async function getServerDefaultWheel(): Promise<{\n  defaultWheel: PresetWheel;\n  otherWheels: WheelListItem[];\n}> {\n  try {\n    const defaultWheel = getDefaultWheel()\n    const allWheels = getWheelList()\n    const otherWheels = allWheels.filter(w => w.slug !== defaultWheel.slug)\n    \n    return {\n      defaultWheel,\n      otherWheels\n    }\n  } catch (error) {\n    console.error('Error fetching server default wheel:', error)\n    throw error\n  }\n}\n\n/**\n * 获取所有转盘列表（服务端）\n */\nexport async function getServerWheelList(): Promise<WheelListItem[]> {\n  try {\n    return getWheelList()\n  } catch (error) {\n    console.error('Error fetching server wheel list:', error)\n    throw error\n  }\n}\n\n/**\n * 验证转盘slug是否存在（服务端）\n */\nexport function validateWheelSlug(slug: string): boolean {\n  return !!findWheelBySlug(slug)\n}\n\n/**\n * 获取转盘的SEO数据（服务端）\n */\nexport function getWheelSeoData(slug?: string) {\n  try {\n    const wheel = slug ? findWheelBySlug(slug) : getDefaultWheel()\n    \n    if (!wheel) {\n      return null\n    }\n    \n    return {\n      title: wheel.seo.title,\n      description: wheel.seo.description,\n      keywords: wheel.seo.keywords.join(', '),\n      canonical: slug ? `https://decisionsmaker.online/${slug}` : 'https://decisionsmaker.online',\n      ogImage: 'https://decisionsmaker.online/og-image.png'\n    }\n  } catch (error) {\n    console.error('Error getting wheel SEO data:', error)\n    return null\n  }\n}\n\n/**\n * 生成站点地图数据（服务端）\n */\nexport function generateSitemapData() {\n  try {\n    const wheels = getWheelList()\n    const baseUrl = 'https://decisionsmaker.online'\n    \n    const urls = [\n      {\n        loc: baseUrl,\n        lastmod: new Date().toISOString(),\n        changefreq: 'daily',\n        priority: 1.0\n      },\n      ...wheels.map(wheel => ({\n        loc: `${baseUrl}/${wheel.slug}`,\n        lastmod: new Date().toISOString(),\n        changefreq: 'weekly',\n        priority: 0.8\n      }))\n    ]\n    \n    return urls\n  } catch (error) {\n    console.error('Error generating sitemap data:', error)\n    return []\n  }\n}\n\n/**\n * 生成robots.txt内容（服务端）\n */\nexport function generateRobotsContent(): string {\n  const baseUrl = 'https://decisionsmaker.online'\n  \n  return `User-agent: *\nAllow: /\n\n# Sitemaps\nSitemap: ${baseUrl}/sitemap.xml\n\n# Crawl-delay\nCrawl-delay: 1\n\n# Disallow admin areas\nDisallow: /admin/\nDisallow: /api/\n\n# Allow specific API endpoints for SEO\nAllow: /api/wheel-page/\nAllow: /api/wheel-list/`\n}\n\n/**\n * 处理API错误（服务端）\n */\nexport function handleServerError(error: any, context: string) {\n  console.error(`Server error in ${context}:`, error)\n  \n  // 根据错误类型返回适当的HTTP状态码和消息\n  if (error.message?.includes('not found')) {\n    return {\n      statusCode: 404,\n      message: 'Resource not found'\n    }\n  } else if (error.message?.includes('validation')) {\n    return {\n      statusCode: 400,\n      message: 'Invalid request'\n    }\n  } else {\n    return {\n      statusCode: 500,\n      message: 'Internal server error'\n    }\n  }\n}\n\n/**\n * 创建API响应（服务端）\n */\nexport function createApiResponse<T>(\n  data: T,\n  success: boolean = true,\n  message?: string,\n  statusCode: number = 200\n) {\n  return {\n    data,\n    success,\n    message,\n    statusCode,\n    timestamp: new Date().toISOString()\n  }\n}\n\n/**\n * 验证请求参数（服务端）\n */\nexport function validateRequestParams(\n  params: Record<string, any>,\n  required: string[]\n): { valid: boolean; missing?: string[] } {\n  const missing = required.filter(key => !params[key])\n  \n  return {\n    valid: missing.length === 0,\n    missing: missing.length > 0 ? missing : undefined\n  }\n}\n\n/**\n * 缓存控制头（服务端）\n */\nexport function getCacheHeaders(maxAge: number = 3600) {\n  return {\n    'Cache-Control': `public, max-age=${maxAge}, s-maxage=${maxAge}`,\n    'Vary': 'Accept-Encoding'\n  }\n}\n\n/**\n * CORS头（服务端）\n */\nexport function getCorsHeaders() {\n  return {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n  }\n}\n", "import { createElementBlock, defineComponent } from \"vue\";\nexport default defineComponent({\n  name: \"ServerPlaceholder\",\n  render() {\n    return createElementBlock(\"div\");\n  }\n});\n", "import { cloneVNode, createElementBlock, defineComponent, getCurrentInstance, h, onMounted, provide, shallowRef } from \"vue\";\nimport { isPromise } from \"@vue/shared\";\nimport { useNuxtApp } from \"../nuxt.js\";\nimport ServerPlaceholder from \"./server-placeholder.js\";\nimport { elToStaticVNode } from \"./utils.js\";\nexport const clientOnlySymbol = Symbol.for(\"nuxt:client-only\");\nconst STATIC_DIV = \"<div></div>\";\nexport default defineComponent({\n  name: \"ClientOnly\",\n  inheritAttrs: false,\n  props: [\"fallback\", \"placeholder\", \"placeholderTag\", \"fallbackTag\"],\n  setup(props, { slots, attrs }) {\n    const mounted = shallowRef(false);\n    onMounted(() => {\n      mounted.value = true;\n    });\n    if (import.meta.dev) {\n      const nuxtApp = useNuxtApp();\n      nuxtApp._isNuxtPageUsed = true;\n      nuxtApp._isNuxtLayoutUsed = true;\n    }\n    const vm = getCurrentInstance();\n    if (vm) {\n      vm._nuxtClientOnly = true;\n    }\n    provide(clientOnlySymbol, true);\n    return () => {\n      if (mounted.value) {\n        const vnodes = slots.default?.();\n        if (vnodes && vnodes.length === 1) {\n          return [cloneVNode(vnodes[0], attrs)];\n        }\n        return vnodes;\n      }\n      const slot = slots.fallback || slots.placeholder;\n      if (slot) {\n        return h(slot);\n      }\n      const fallbackStr = props.fallback || props.placeholder || \"\";\n      const fallbackTag = props.fallbackTag || props.placeholderTag || \"span\";\n      return createElementBlock(fallbackTag, attrs, fallbackStr);\n    };\n  }\n});\nconst cache = /* @__PURE__ */ new WeakMap();\n// @__NO_SIDE_EFFECTS__\nexport function createClientOnly(component) {\n  if (import.meta.server) {\n    return ServerPlaceholder;\n  }\n  if (cache.has(component)) {\n    return cache.get(component);\n  }\n  const clone = { ...component };\n  if (clone.render) {\n    clone.render = (ctx, cache2, $props, $setup, $data, $options) => {\n      if ($setup.mounted$ ?? ctx.mounted$) {\n        const res = component.render?.bind(ctx)(ctx, cache2, $props, $setup, $data, $options);\n        return res.children === null || typeof res.children === \"string\" ? cloneVNode(res) : h(res);\n      }\n      return elToStaticVNode(ctx._.vnode.el, STATIC_DIV);\n    };\n  } else {\n    clone.template &&= `\n      <template v-if=\"mounted$\">${component.template}</template>\n      <template v-else>${STATIC_DIV}</template>\n    `;\n  }\n  clone.setup = (props, ctx) => {\n    const nuxtApp = useNuxtApp();\n    const mounted$ = shallowRef(nuxtApp.isHydrating === false);\n    const instance = getCurrentInstance();\n    if (nuxtApp.isHydrating) {\n      const attrs = { ...instance.attrs };\n      const directives = extractDirectives(instance);\n      for (const key in attrs) {\n        delete instance.attrs[key];\n      }\n      onMounted(() => {\n        Object.assign(instance.attrs, attrs);\n        instance.vnode.dirs = directives;\n      });\n    }\n    onMounted(() => {\n      mounted$.value = true;\n    });\n    const setupState = component.setup?.(props, ctx) || {};\n    if (isPromise(setupState)) {\n      return Promise.resolve(setupState).then((setupState2) => {\n        if (typeof setupState2 !== \"function\") {\n          setupState2 ||= {};\n          setupState2.mounted$ = mounted$;\n          return setupState2;\n        }\n        return (...args) => {\n          if (mounted$.value || !nuxtApp.isHydrating) {\n            const res = setupState2(...args);\n            return res.children === null || typeof res.children === \"string\" ? cloneVNode(res) : h(res);\n          }\n          return elToStaticVNode(instance?.vnode.el, STATIC_DIV);\n        };\n      });\n    } else {\n      if (typeof setupState === \"function\") {\n        return (...args) => {\n          if (mounted$.value) {\n            const res = setupState(...args);\n            return res.children === null || typeof res.children === \"string\" ? cloneVNode(res, ctx.attrs) : h(res, ctx.attrs);\n          }\n          return elToStaticVNode(instance?.vnode.el, STATIC_DIV);\n        };\n      }\n      return Object.assign(setupState, { mounted$ });\n    }\n  };\n  cache.set(component, clone);\n  return clone;\n}\nfunction extractDirectives(instance) {\n  if (!instance || !instance.vnode.dirs) {\n    return null;\n  }\n  const directives = instance.vnode.dirs;\n  instance.vnode.dirs = null;\n  return directives;\n}\n", "import { computed, getCurrentInstance, getCurrentScope, inject, isShallow, nextTick, onBeforeMount, onScopeDispose, onServerPrefetch, onUnmounted, ref, shallowRef, toRef, toValue, unref, watch } from \"vue\";\nimport { captureStackTrace } from \"errx\";\nimport { debounce } from \"perfect-debounce\";\nimport { hash } from \"ohash\";\nimport { useNuxtApp } from \"../nuxt.js\";\nimport { toArray } from \"../utils.js\";\nimport { clientOnlySymbol } from \"../components/client-only.js\";\nimport { createError } from \"./error.js\";\nimport { onNuxtReady } from \"./ready.js\";\nimport { asyncDataDefaults, granularCachedData, pendingWhenIdle, purgeCachedData, resetAsyncDataToUndefined } from \"#build/nuxt.config.mjs\";\nconst isDefer = (dedupe) => dedupe === \"defer\" || dedupe === false;\nexport function useAsyncData(...args) {\n  const autoKey = typeof args[args.length - 1] === \"string\" ? args.pop() : void 0;\n  if (_isAutoKeyNeeded(args[0], args[1])) {\n    args.unshift(autoKey);\n  }\n  let [_key, _handler, options = {}] = args;\n  const key = computed(() => toValue(_key));\n  if (typeof key.value !== \"string\") {\n    throw new TypeError(\"[nuxt] [useAsyncData] key must be a string.\");\n  }\n  if (typeof _handler !== \"function\") {\n    throw new TypeError(\"[nuxt] [useAsyncData] handler must be a function.\");\n  }\n  const nuxtApp = useNuxtApp();\n  options.server ??= true;\n  options.default ??= getDefault;\n  options.getCachedData ??= getDefaultCachedData;\n  options.lazy ??= false;\n  options.immediate ??= true;\n  options.deep ??= asyncDataDefaults.deep;\n  options.dedupe ??= \"cancel\";\n  const functionName = options._functionName || \"useAsyncData\";\n  if (import.meta.dev && typeof options.dedupe === \"boolean\") {\n    console.warn(`[nuxt] \\`boolean\\` values are deprecated for the \\`dedupe\\` option of \\`${functionName}\\` and will be removed in the future. Use 'cancel' or 'defer' instead.`);\n  }\n  const currentData = nuxtApp._asyncData[key.value];\n  if (isDev && currentData) {\n    const warnings = [];\n    const values = createHash(_handler, options);\n    if (values.handler !== currentData._hash?.handler) {\n      warnings.push(`different handler`);\n    }\n    for (const opt of [\"transform\", \"pick\", \"getCachedData\"]) {\n      if (values[opt] !== currentData._hash[opt]) {\n        warnings.push(`different \\`${opt}\\` option`);\n      }\n    }\n    if (currentData._default.toString() !== options.default.toString()) {\n      warnings.push(`different \\`default\\` value`);\n    }\n    if (options.deep && isShallow(currentData.data)) {\n      warnings.push(`mismatching \\`deep\\` option`);\n    }\n    if (warnings.length) {\n      const distURL = import.meta.url.replace(/\\/app\\/.*$/, \"/app\");\n      const { source, line, column } = captureStackTrace().find((entry) => !entry.source.startsWith(distURL)) ?? {};\n      const explanation = source ? ` (used at ${source.replace(/^file:\\/\\//, \"\")}:${line}:${column})` : \"\";\n      console.warn(`[nuxt] [${functionName}] Incompatible options detected for \"${key.value}\"${explanation}:\n${warnings.map((w) => `- ${w}`).join(\"\\n\")}\nYou can use a different key or move the call to a composable to ensure the options are shared across calls.`);\n    }\n  }\n  const initialFetchOptions = { cause: \"initial\", dedupe: options.dedupe };\n  if (!nuxtApp._asyncData[key.value]?._init) {\n    initialFetchOptions.cachedData = options.getCachedData(key.value, nuxtApp, { cause: \"initial\" });\n    nuxtApp._asyncData[key.value] = createAsyncData(nuxtApp, key.value, _handler, options, initialFetchOptions.cachedData);\n  }\n  const asyncData = nuxtApp._asyncData[key.value];\n  asyncData._deps++;\n  const initialFetch = () => nuxtApp._asyncData[key.value].execute(initialFetchOptions);\n  const fetchOnServer = options.server !== false && nuxtApp.payload.serverRendered;\n  if (import.meta.server && fetchOnServer && options.immediate) {\n    const promise = initialFetch();\n    if (getCurrentInstance()) {\n      onServerPrefetch(() => promise);\n    } else {\n      nuxtApp.hook(\"app:created\", async () => {\n        await promise;\n      });\n    }\n  }\n  if (import.meta.client) {\n    let unregister = function(key2) {\n      const data = nuxtApp._asyncData[key2];\n      if (data?._deps) {\n        data._deps--;\n        if (data._deps === 0) {\n          data?._off();\n        }\n      }\n    };\n    const instance = getCurrentInstance();\n    if (instance && fetchOnServer && options.immediate && !instance.sp) {\n      instance.sp = [];\n    }\n    if (import.meta.dev && !nuxtApp.isHydrating && !nuxtApp._processingMiddleware && (!instance || instance?.isMounted)) {\n      console.warn(`[nuxt] [${functionName}] Component is already mounted, please use $fetch instead. See https://nuxt.com/docs/getting-started/data-fetching`);\n    }\n    if (instance && !instance._nuxtOnBeforeMountCbs) {\n      instance._nuxtOnBeforeMountCbs = [];\n      const cbs = instance._nuxtOnBeforeMountCbs;\n      onBeforeMount(() => {\n        cbs.forEach((cb) => {\n          cb();\n        });\n        cbs.splice(0, cbs.length);\n      });\n      onUnmounted(() => cbs.splice(0, cbs.length));\n    }\n    const isWithinClientOnly = instance && (instance._nuxtClientOnly || inject(clientOnlySymbol, false));\n    if (fetchOnServer && nuxtApp.isHydrating && (asyncData.error.value || asyncData.data.value != null)) {\n      if (pendingWhenIdle) {\n        asyncData.pending.value = false;\n      }\n      asyncData.status.value = asyncData.error.value ? \"error\" : \"success\";\n    } else if (instance && (!isWithinClientOnly && nuxtApp.payload.serverRendered && nuxtApp.isHydrating || options.lazy) && options.immediate) {\n      instance._nuxtOnBeforeMountCbs.push(initialFetch);\n    } else if (options.immediate) {\n      initialFetch();\n    }\n    const hasScope = getCurrentScope();\n    const unsubExecute = watch([key, ...options.watch || []], ([newKey], [oldKey]) => {\n      if ((newKey || oldKey) && newKey !== oldKey) {\n        const hasRun = nuxtApp._asyncData[oldKey]?.data.value !== asyncDataDefaults.value;\n        if (oldKey) {\n          unregister(oldKey);\n        }\n        const initialFetchOptions2 = { cause: \"initial\", dedupe: options.dedupe };\n        if (!nuxtApp._asyncData[newKey]?._init) {\n          initialFetchOptions2.cachedData = options.getCachedData(newKey, nuxtApp, { cause: \"initial\" });\n          nuxtApp._asyncData[newKey] = createAsyncData(nuxtApp, newKey, _handler, options, initialFetchOptions2.cachedData);\n        }\n        nuxtApp._asyncData[newKey]._deps++;\n        if (options.immediate || hasRun) {\n          nuxtApp._asyncData[newKey].execute(initialFetchOptions2);\n        }\n      } else {\n        asyncData._execute({ cause: \"watch\", dedupe: options.dedupe });\n      }\n    }, { flush: \"sync\" });\n    if (hasScope) {\n      onScopeDispose(() => {\n        unsubExecute();\n        unregister(key.value);\n      });\n    }\n  }\n  const asyncReturn = {\n    data: writableComputedRef(() => nuxtApp._asyncData[key.value]?.data),\n    pending: writableComputedRef(() => nuxtApp._asyncData[key.value]?.pending),\n    status: writableComputedRef(() => nuxtApp._asyncData[key.value]?.status),\n    error: writableComputedRef(() => nuxtApp._asyncData[key.value]?.error),\n    refresh: (...args2) => nuxtApp._asyncData[key.value].execute(...args2),\n    execute: (...args2) => nuxtApp._asyncData[key.value].execute(...args2),\n    clear: () => clearNuxtDataByKey(nuxtApp, key.value)\n  };\n  const asyncDataPromise = Promise.resolve(nuxtApp._asyncDataPromises[key.value]).then(() => asyncReturn);\n  Object.assign(asyncDataPromise, asyncReturn);\n  return asyncDataPromise;\n}\nfunction writableComputedRef(getter) {\n  return computed({\n    get() {\n      return getter()?.value;\n    },\n    set(value) {\n      const ref2 = getter();\n      if (ref2) {\n        ref2.value = value;\n      }\n    }\n  });\n}\nexport function useLazyAsyncData(...args) {\n  const autoKey = typeof args[args.length - 1] === \"string\" ? args.pop() : void 0;\n  if (_isAutoKeyNeeded(args[0], args[1])) {\n    args.unshift(autoKey);\n  }\n  const [key, handler, options = {}] = args;\n  if (import.meta.dev) {\n    options._functionName ||= \"useLazyAsyncData\";\n  }\n  return useAsyncData(key, handler, { ...options, lazy: true }, null);\n}\nfunction _isAutoKeyNeeded(keyOrFetcher, fetcher) {\n  if (typeof keyOrFetcher === \"string\") {\n    return false;\n  }\n  if (typeof keyOrFetcher === \"object\" && keyOrFetcher !== null) {\n    return false;\n  }\n  if (typeof keyOrFetcher === \"function\" && typeof fetcher === \"function\") {\n    return false;\n  }\n  return true;\n}\nexport function useNuxtData(key) {\n  const nuxtApp = useNuxtApp();\n  if (!(key in nuxtApp.payload.data)) {\n    nuxtApp.payload.data[key] = asyncDataDefaults.value;\n  }\n  if (nuxtApp._asyncData[key]) {\n    const data = nuxtApp._asyncData[key];\n    data._deps++;\n    if (getCurrentScope()) {\n      onScopeDispose(() => {\n        data._deps--;\n        if (data._deps === 0) {\n          data?._off();\n        }\n      });\n    }\n  }\n  return {\n    data: computed({\n      get() {\n        return nuxtApp._asyncData[key]?.data.value ?? nuxtApp.payload.data[key];\n      },\n      set(value) {\n        if (nuxtApp._asyncData[key]) {\n          nuxtApp._asyncData[key].data.value = value;\n        } else {\n          nuxtApp.payload.data[key] = value;\n        }\n      }\n    })\n  };\n}\nexport async function refreshNuxtData(keys) {\n  if (import.meta.server) {\n    return Promise.resolve();\n  }\n  await new Promise((resolve) => onNuxtReady(resolve));\n  const _keys = keys ? toArray(keys) : void 0;\n  await useNuxtApp().hooks.callHookParallel(\"app:data:refresh\", _keys);\n}\nexport function clearNuxtData(keys) {\n  const nuxtApp = useNuxtApp();\n  const _allKeys = Object.keys(nuxtApp.payload.data);\n  const _keys = !keys ? _allKeys : typeof keys === \"function\" ? _allKeys.filter(keys) : toArray(keys);\n  for (const key of _keys) {\n    clearNuxtDataByKey(nuxtApp, key);\n  }\n}\nfunction clearNuxtDataByKey(nuxtApp, key) {\n  if (key in nuxtApp.payload.data) {\n    nuxtApp.payload.data[key] = void 0;\n  }\n  if (key in nuxtApp.payload._errors) {\n    nuxtApp.payload._errors[key] = asyncDataDefaults.errorValue;\n  }\n  if (nuxtApp._asyncData[key]) {\n    nuxtApp._asyncData[key].data.value = resetAsyncDataToUndefined ? void 0 : unref(nuxtApp._asyncData[key]._default());\n    nuxtApp._asyncData[key].error.value = asyncDataDefaults.errorValue;\n    if (pendingWhenIdle) {\n      nuxtApp._asyncData[key].pending.value = false;\n    }\n    nuxtApp._asyncData[key].status.value = \"idle\";\n  }\n  if (key in nuxtApp._asyncDataPromises) {\n    if (nuxtApp._asyncDataPromises[key]) {\n      nuxtApp._asyncDataPromises[key].cancelled = true;\n    }\n    nuxtApp._asyncDataPromises[key] = void 0;\n  }\n}\nfunction pick(obj, keys) {\n  const newObj = {};\n  for (const key of keys) {\n    newObj[key] = obj[key];\n  }\n  return newObj;\n}\nconst isDev = import.meta.dev;\nfunction createAsyncData(nuxtApp, key, _handler, options, initialCachedData) {\n  nuxtApp.payload._errors[key] ??= asyncDataDefaults.errorValue;\n  const hasCustomGetCachedData = options.getCachedData !== getDefaultCachedData;\n  const handler = import.meta.client || !import.meta.prerender || !nuxtApp.ssrContext?._sharedPrerenderCache ? _handler : () => {\n    const value = nuxtApp.ssrContext._sharedPrerenderCache.get(key);\n    if (value) {\n      return value;\n    }\n    const promise = Promise.resolve().then(() => nuxtApp.runWithContext(() => _handler(nuxtApp)));\n    nuxtApp.ssrContext._sharedPrerenderCache.set(key, promise);\n    return promise;\n  };\n  const _ref = options.deep ? ref : shallowRef;\n  const hasCachedData = initialCachedData != null;\n  const unsubRefreshAsyncData = nuxtApp.hook(\"app:data:refresh\", async (keys) => {\n    if (!keys || keys.includes(key)) {\n      await asyncData.execute({ cause: \"refresh:hook\" });\n    }\n  });\n  const asyncData = {\n    data: _ref(hasCachedData ? initialCachedData : options.default()),\n    pending: pendingWhenIdle ? shallowRef(!hasCachedData) : computed(() => asyncData.status.value === \"pending\"),\n    error: toRef(nuxtApp.payload._errors, key),\n    status: shallowRef(\"idle\"),\n    execute: (opts = {}) => {\n      if (nuxtApp._asyncDataPromises[key]) {\n        if (isDefer(opts.dedupe ?? options.dedupe)) {\n          return nuxtApp._asyncDataPromises[key];\n        }\n        nuxtApp._asyncDataPromises[key].cancelled = true;\n      }\n      if (granularCachedData || opts.cause === \"initial\" || nuxtApp.isHydrating) {\n        const cachedData = \"cachedData\" in opts ? opts.cachedData : options.getCachedData(key, nuxtApp, { cause: opts.cause ?? \"refresh:manual\" });\n        if (cachedData != null) {\n          nuxtApp.payload.data[key] = asyncData.data.value = cachedData;\n          asyncData.error.value = asyncDataDefaults.errorValue;\n          asyncData.status.value = \"success\";\n          return Promise.resolve(cachedData);\n        }\n      }\n      if (pendingWhenIdle) {\n        asyncData.pending.value = true;\n      }\n      asyncData.status.value = \"pending\";\n      const promise = new Promise(\n        (resolve, reject) => {\n          try {\n            resolve(handler(nuxtApp));\n          } catch (err) {\n            reject(err);\n          }\n        }\n      ).then(async (_result) => {\n        if (promise.cancelled) {\n          return nuxtApp._asyncDataPromises[key];\n        }\n        let result = _result;\n        if (options.transform) {\n          result = await options.transform(_result);\n        }\n        if (options.pick) {\n          result = pick(result, options.pick);\n        }\n        if (import.meta.dev && import.meta.server && typeof result === \"undefined\") {\n          const stack = captureStackTrace();\n          const { source, line, column } = stack[stack.length - 1] ?? {};\n          const explanation = source ? ` (used at ${source.replace(/^file:\\/\\//, \"\")}:${line}:${column})` : \"\";\n          console.warn(`[nuxt] \\`${options._functionName || \"useAsyncData\"}${explanation}\\` must return a value (it should not be \\`undefined\\`) or the request may be duplicated on the client side.`);\n        }\n        nuxtApp.payload.data[key] = result;\n        asyncData.data.value = result;\n        asyncData.error.value = asyncDataDefaults.errorValue;\n        asyncData.status.value = \"success\";\n      }).catch((error) => {\n        if (promise.cancelled) {\n          return nuxtApp._asyncDataPromises[key];\n        }\n        asyncData.error.value = createError(error);\n        asyncData.data.value = unref(options.default());\n        asyncData.status.value = \"error\";\n      }).finally(() => {\n        if (promise.cancelled) {\n          return;\n        }\n        if (pendingWhenIdle) {\n          asyncData.pending.value = false;\n        }\n        delete nuxtApp._asyncDataPromises[key];\n      });\n      nuxtApp._asyncDataPromises[key] = promise;\n      return nuxtApp._asyncDataPromises[key];\n    },\n    _execute: debounce((...args) => asyncData.execute(...args), 0, { leading: true }),\n    _default: options.default,\n    _deps: 0,\n    _init: true,\n    _hash: isDev ? createHash(_handler, options) : void 0,\n    _off: () => {\n      unsubRefreshAsyncData();\n      if (nuxtApp._asyncData[key]?._init) {\n        nuxtApp._asyncData[key]._init = false;\n      }\n      if (purgeCachedData && !hasCustomGetCachedData) {\n        nextTick(() => {\n          if (!nuxtApp._asyncData[key]?._init) {\n            clearNuxtDataByKey(nuxtApp, key);\n            asyncData.execute = () => Promise.resolve();\n            asyncData.data.value = asyncDataDefaults.value;\n          }\n        });\n      }\n    }\n  };\n  return asyncData;\n}\nconst getDefault = () => asyncDataDefaults.value;\nconst getDefaultCachedData = (key, nuxtApp, ctx) => {\n  if (nuxtApp.isHydrating) {\n    return nuxtApp.payload.data[key];\n  }\n  if (ctx.cause !== \"refresh:manual\" && ctx.cause !== \"refresh:hook\") {\n    return nuxtApp.static.data[key];\n  }\n};\nfunction createHash(_handler, options) {\n  return {\n    handler: hash(_handler),\n    transform: options.transform ? hash(options.transform) : void 0,\n    pick: options.pick ? hash(options.pick) : void 0,\n    getCachedData: options.getCachedData ? hash(options.getCachedData) : void 0\n  };\n}\n", "import { computed, reactive, toValue, watch } from \"vue\";\nimport { hash } from \"ohash\";\nimport { isPlainObject } from \"@vue/shared\";\nimport { useRequestFetch } from \"./ssr.js\";\nimport { useAsyncData } from \"./asyncData.js\";\nimport { alwaysRunFetchOnKeyChange, fetchDefaults } from \"#build/nuxt.config.mjs\";\nexport function useFetch(request, arg1, arg2) {\n  const [opts = {}, autoKey] = typeof arg1 === \"string\" ? [{}, arg1] : [arg1, arg2];\n  const _request = computed(() => toValue(request));\n  const key = computed(() => toValue(opts.key) || \"$f\" + hash([autoKey, typeof _request.value === \"string\" ? _request.value : \"\", ...generateOptionSegments(opts)]));\n  if (!opts.baseURL && typeof _request.value === \"string\" && (_request.value[0] === \"/\" && _request.value[1] === \"/\")) {\n    throw new Error('[nuxt] [useFetch] the request URL must not start with \"//\".');\n  }\n  const {\n    server,\n    lazy,\n    default: defaultFn,\n    transform,\n    pick,\n    watch: watchSources,\n    immediate,\n    getCachedData,\n    deep,\n    dedupe,\n    ...fetchOptions\n  } = opts;\n  const _fetchOptions = reactive({\n    ...fetchDefaults,\n    ...fetchOptions,\n    cache: typeof opts.cache === \"boolean\" ? void 0 : opts.cache\n  });\n  const _asyncDataOptions = {\n    server,\n    lazy,\n    default: defaultFn,\n    transform,\n    pick,\n    immediate,\n    getCachedData,\n    deep,\n    dedupe,\n    watch: watchSources === false ? [] : [...watchSources || [], _fetchOptions]\n  };\n  if (import.meta.dev) {\n    _asyncDataOptions._functionName ||= \"useFetch\";\n  }\n  if (alwaysRunFetchOnKeyChange && !immediate) {\n    let setImmediate = function() {\n      _asyncDataOptions.immediate = true;\n    };\n    watch(key, setImmediate, { flush: \"sync\", once: true });\n    watch([...watchSources || [], _fetchOptions], setImmediate, { flush: \"sync\", once: true });\n  }\n  let controller;\n  const asyncData = useAsyncData(watchSources === false ? key.value : key, () => {\n    controller?.abort?.(new DOMException(\"Request aborted as another request to the same endpoint was initiated.\", \"AbortError\"));\n    controller = typeof AbortController !== \"undefined\" ? new AbortController() : {};\n    const timeoutLength = toValue(opts.timeout);\n    let timeoutId;\n    if (timeoutLength) {\n      timeoutId = setTimeout(() => controller.abort(new DOMException(\"Request aborted due to timeout.\", \"AbortError\")), timeoutLength);\n      controller.signal.onabort = () => clearTimeout(timeoutId);\n    }\n    let _$fetch = opts.$fetch || globalThis.$fetch;\n    if (import.meta.server && !opts.$fetch) {\n      const isLocalFetch = typeof _request.value === \"string\" && _request.value[0] === \"/\" && (!toValue(opts.baseURL) || toValue(opts.baseURL)[0] === \"/\");\n      if (isLocalFetch) {\n        _$fetch = useRequestFetch();\n      }\n    }\n    return _$fetch(_request.value, { signal: controller.signal, ..._fetchOptions }).finally(() => {\n      clearTimeout(timeoutId);\n    });\n  }, _asyncDataOptions);\n  return asyncData;\n}\nexport function useLazyFetch(request, arg1, arg2) {\n  const [opts = {}, autoKey] = typeof arg1 === \"string\" ? [{}, arg1] : [arg1, arg2];\n  if (import.meta.dev) {\n    opts._functionName ||= \"useLazyFetch\";\n  }\n  return useFetch(\n    request,\n    {\n      ...opts,\n      lazy: true\n    },\n    // @ts-expect-error we pass an extra argument with the resolved auto-key to prevent another from being injected\n    autoKey\n  );\n}\nfunction generateOptionSegments(opts) {\n  const segments = [\n    toValue(opts.method)?.toUpperCase() || \"GET\",\n    toValue(opts.baseURL)\n  ];\n  for (const _obj of [opts.params || opts.query]) {\n    const obj = toValue(_obj);\n    if (!obj) {\n      continue;\n    }\n    const unwrapped = {};\n    for (const [key, value] of Object.entries(obj)) {\n      unwrapped[toValue(key)] = toValue(value);\n    }\n    segments.push(unwrapped);\n  }\n  if (opts.body) {\n    const value = toValue(opts.body);\n    if (!value) {\n      segments.push(hash(value));\n    } else if (value instanceof ArrayBuffer) {\n      segments.push(hash(Object.fromEntries([...new Uint8Array(value).entries()].map(([k, v]) => [k, v.toString()]))));\n    } else if (value instanceof FormData) {\n      const obj = {};\n      for (const entry of value.entries()) {\n        const [key, val] = entry;\n        obj[key] = val instanceof File ? val.name : val;\n      }\n      segments.push(hash(obj));\n    } else if (isPlainObject(value)) {\n      segments.push(hash(reactive(value)));\n    } else {\n      try {\n        segments.push(hash(value));\n      } catch {\n        console.warn(\"[useFetch] Failed to hash body\", value);\n      }\n    }\n  }\n  return segments;\n}\n"], "version": 3}