const main = `*,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/*! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com*/*,:after,:before{border:0 solid #e5e7eb;box-sizing:border-box}:after,:before{--tw-content:""}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}body{line-height:inherit;margin:0}hr{border-top-width:1px;color:inherit;height:0}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-size:1em;font-variation-settings:normal}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{border-collapse:collapse;border-color:inherit;text-indent:0}button,input,optgroup,select,textarea{color:inherit;font-family:inherit;font-feature-settings:inherit;font-size:100%;font-variation-settings:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0}fieldset,legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{color:#9ca3af;opacity:1}input::placeholder,textarea::placeholder{color:#9ca3af;opacity:1}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{height:auto;max-width:100%}[hidden]:where(:not([hidden=until-found])){display:none}:root:has(input.theme-controller[value=light]:checked),:where(:root),[data-theme=light]{color-scheme:light;--color-base-100:oklch(100% 0 0);--color-base-200:oklch(98% 0 0);--color-base-300:oklch(95% 0 0);--color-base-content:oklch(21% .006 285.885);--color-primary:oklch(45% .24 277.023);--color-primary-content:oklch(93% .034 272.788);--color-secondary:oklch(65% .241 354.308);--color-secondary-content:oklch(94% .028 342.258);--color-accent:oklch(77% .152 181.912);--color-accent-content:oklch(38% .063 188.416);--color-neutral:oklch(14% .005 285.823);--color-neutral-content:oklch(92% .004 286.32);--color-info:oklch(74% .16 232.661);--color-info-content:oklch(29% .066 243.157);--color-success:oklch(76% .177 163.223);--color-success-content:oklch(37% .077 168.94);--color-warning:oklch(82% .189 84.429);--color-warning-content:oklch(41% .112 45.904);--color-error:oklch(71% .194 13.428);--color-error-content:oklch(27% .105 12.094);--radius-selector:.5rem;--radius-field:.25rem;--radius-box:.5rem;--size-selector:.25rem;--size-field:.25rem;--border:1px;--depth:1;--noise:0}@media (prefers-color-scheme:dark){:root{color-scheme:dark;--color-base-100:oklch(25.33% .016 252.42);--color-base-200:oklch(23.26% .014 253.1);--color-base-300:oklch(21.15% .012 254.09);--color-base-content:oklch(97.807% .029 256.847);--color-primary:oklch(58% .233 277.117);--color-primary-content:oklch(96% .018 272.314);--color-secondary:oklch(65% .241 354.308);--color-secondary-content:oklch(94% .028 342.258);--color-accent:oklch(77% .152 181.912);--color-accent-content:oklch(38% .063 188.416);--color-neutral:oklch(14% .005 285.823);--color-neutral-content:oklch(92% .004 286.32);--color-info:oklch(74% .16 232.661);--color-info-content:oklch(29% .066 243.157);--color-success:oklch(76% .177 163.223);--color-success-content:oklch(37% .077 168.94);--color-warning:oklch(82% .189 84.429);--color-warning-content:oklch(41% .112 45.904);--color-error:oklch(71% .194 13.428);--color-error-content:oklch(27% .105 12.094);--radius-selector:.5rem;--radius-field:.25rem;--radius-box:.5rem;--size-selector:.25rem;--size-field:.25rem;--border:1px;--depth:1;--noise:0}}:root:has(input.theme-controller[value=light]:checked),[data-theme=light]{color-scheme:light;--color-base-100:oklch(100% 0 0);--color-base-200:oklch(98% 0 0);--color-base-300:oklch(95% 0 0);--color-base-content:oklch(21% .006 285.885);--color-primary:oklch(45% .24 277.023);--color-primary-content:oklch(93% .034 272.788);--color-secondary:oklch(65% .241 354.308);--color-secondary-content:oklch(94% .028 342.258);--color-accent:oklch(77% .152 181.912);--color-accent-content:oklch(38% .063 188.416);--color-neutral:oklch(14% .005 285.823);--color-neutral-content:oklch(92% .004 286.32);--color-info:oklch(74% .16 232.661);--color-info-content:oklch(29% .066 243.157);--color-success:oklch(76% .177 163.223);--color-success-content:oklch(37% .077 168.94);--color-warning:oklch(82% .189 84.429);--color-warning-content:oklch(41% .112 45.904);--color-error:oklch(71% .194 13.428);--color-error-content:oklch(27% .105 12.094);--radius-selector:.5rem;--radius-field:.25rem;--radius-box:.5rem;--size-selector:.25rem;--size-field:.25rem;--border:1px;--depth:1;--noise:0}:root:has(input.theme-controller[value=dark]:checked),[data-theme=dark]{color-scheme:dark;--color-base-100:oklch(25.33% .016 252.42);--color-base-200:oklch(23.26% .014 253.1);--color-base-300:oklch(21.15% .012 254.09);--color-base-content:oklch(97.807% .029 256.847);--color-primary:oklch(58% .233 277.117);--color-primary-content:oklch(96% .018 272.314);--color-secondary:oklch(65% .241 354.308);--color-secondary-content:oklch(94% .028 342.258);--color-accent:oklch(77% .152 181.912);--color-accent-content:oklch(38% .063 188.416);--color-neutral:oklch(14% .005 285.823);--color-neutral-content:oklch(92% .004 286.32);--color-info:oklch(74% .16 232.661);--color-info-content:oklch(29% .066 243.157);--color-success:oklch(76% .177 163.223);--color-success-content:oklch(37% .077 168.94);--color-warning:oklch(82% .189 84.429);--color-warning-content:oklch(41% .112 45.904);--color-error:oklch(71% .194 13.428);--color-error-content:oklch(27% .105 12.094);--radius-selector:.5rem;--radius-field:.25rem;--radius-box:.5rem;--size-selector:.25rem;--size-field:.25rem;--border:1px;--depth:1;--noise:0}:root{--fx-noise:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence baseFrequency='1.34' numOctaves='4' stitchTiles='stitch' type='fractalNoise'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='.2'/%3E%3C/svg%3E")}:root,[data-theme]{background-color:var(--root-bg,var(--color-base-100));color:var(--color-base-content)}@property --radialprogress{syntax:"<percentage>";inherits:true;initial-value:0%}:root{scrollbar-color:color-mix(in oklch,currentColor 35%,#0000) #0000}html{font-family:var(--font-geist-sans)}body{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-color:var(--color-background);color:var(--color-text);transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition:var(--transition-colors)}.theme-transitioning *{transition:var(--transition-colors)!important}.container{width:100%}@media (min-width:640px){.container{max-width:640px}}@media (min-width:768px){.container{max-width:768px}}@media (min-width:1024px){.container{max-width:1024px}}@media (min-width:1280px){.container{max-width:1280px}}@media (min-width:1536px){.container{max-width:1536px}}.\\!dropdown{display:inline-block!important;position:relative!important;position-area:var(--anchor-v,bottom) var(--anchor-h,span-right)!important}.dropdown{display:inline-block;position:relative;position-area:var(--anchor-v,bottom) var(--anchor-h,span-right)}.\\!dropdown>:not(summary):focus{outline-style:none!important}.dropdown>:not(summary):focus{outline-style:none}@media (forced-colors:active){.\\!dropdown>:not(summary):focus{outline:2px solid transparent!important;outline-offset:2px!important}.dropdown>:not(summary):focus{outline:2px solid transparent;outline-offset:2px}}.\\!dropdown .dropdown-content{position:absolute!important}.dropdown .dropdown-content{position:absolute}.\\!dropdown:not(details,.dropdown-open,.dropdown-hover:hover,:focus-within) .dropdown-content{display:none!important;opacity:0!important;scale:95%!important;transform-origin:top!important}.dropdown:not(details,.dropdown-open,.dropdown-hover:hover,:focus-within) .dropdown-content{display:none;opacity:0;scale:95%;transform-origin:top}@starting-style{.dropdown .dropdown-content,.dropdown[popover]{scale:95%}.\\!dropdown .dropdown-content,.\\!dropdown[popover]{scale:95%!important}.dropdown .dropdown-content,.dropdown[popover]{opacity:0}.\\!dropdown .dropdown-content,.\\!dropdown[popover]{opacity:0!important}}.dropdown .dropdown-content,.dropdown[popover]{animation:dropdown .2s;transition-behavior:allow-discrete;transition-duration:.2s;transition-property:opacity,scale,display;transition-timing-function:cubic-bezier(.4,0,.2,1);z-index:999}.\\!dropdown .dropdown-content,.\\!dropdown[popover]{animation:dropdown .2s!important;transition-behavior:allow-discrete!important;transition-duration:.2s!important;transition-property:opacity,scale,display!important;transition-timing-function:cubic-bezier(.4,0,.2,1)!important;z-index:999!important}.dropdown.dropdown-open>[tabindex]:first-child,.dropdown:focus-within>[tabindex]:first-child,.dropdown:not(.dropdown-hover):focus>[tabindex]:first-child{pointer-events:none}.\\!dropdown.dropdown-open>[tabindex]:first-child,.\\!dropdown:focus-within>[tabindex]:first-child,.\\!dropdown:not(.dropdown-hover):focus>[tabindex]:first-child{pointer-events:none!important}.dropdown.dropdown-open .dropdown-content,.dropdown:focus-within .dropdown-content,.dropdown:not(.dropdown-hover):focus .dropdown-content{opacity:100%}.\\!dropdown.dropdown-open .dropdown-content,.\\!dropdown:focus-within .dropdown-content,.\\!dropdown:not(.dropdown-hover):focus .dropdown-content{opacity:100%!important}.\\!dropdown.dropdown-hover:hover .dropdown-content{opacity:100%!important;scale:100%!important}.dropdown.dropdown-hover:hover .dropdown-content{opacity:100%;scale:100%}.\\!dropdown:is(details) summary::-webkit-details-marker{display:none!important}.dropdown:is(details) summary::-webkit-details-marker{display:none}.dropdown.dropdown-open .dropdown-content,.dropdown:focus .dropdown-content,.dropdown:focus-within .dropdown-content{scale:100%}.\\!dropdown.dropdown-open .dropdown-content,.\\!dropdown:focus .dropdown-content,.\\!dropdown:focus-within .dropdown-content{scale:100%!important}.\\!dropdown:where([popover]){background:#0000!important}.dropdown:where([popover]){background:#0000}.\\!dropdown[popover]{color:inherit!important;position:fixed!important}.dropdown[popover]{color:inherit;position:fixed}@supports not (position-area:bottom){.dropdown[popover]{margin:auto}.dropdown[popover].dropdown-open:not(:popover-open){display:none;opacity:0;scale:95%;transform-origin:top}.dropdown[popover]::backdrop{background-color:color-mix(in oklab,#000 30%,#0000)}.\\!dropdown[popover]{margin:auto!important}.\\!dropdown[popover].dropdown-open:not(:popover-open){display:none!important;opacity:0!important;scale:95%!important;transform-origin:top!important}.\\!dropdown[popover]::backdrop{background-color:color-mix(in oklab,#000 30%,#0000)!important}}.\\!dropdown[popover]:not(.dropdown-open,:popover-open){display:none!important;opacity:0!important;scale:95%!important;transform-origin:top!important}.dropdown[popover]:not(.dropdown-open,:popover-open){display:none;opacity:0;scale:95%;transform-origin:top}@keyframes dropdown{0%{opacity:0}}@keyframes rating{0%,40%{filter:brightness(1.05) contrast(1.05);scale:1.1}}@keyframes radio{0%{padding:5px}50%{padding:3px}}.loading{aspect-ratio:1/1;background-color:currentColor;display:inline-block;-webkit-mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' stroke='%23000' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-linecap='round' stroke-width='3'%3E%3CanimateTransform attributeName='transform' dur='2s' from='0 12 12' repeatCount='indefinite' to='360 12 12' type='rotate'/%3E%3Canimate attributeName='stroke-dasharray' dur='1.5s' keyTimes='0;0.475;1' repeatCount='indefinite' values='0,150;42,150;42,150'/%3E%3Canimate attributeName='stroke-dashoffset' dur='1.5s' keyTimes='0;0.475;1' repeatCount='indefinite' values='0;-16;-59'/%3E%3C/circle%3E%3C/svg%3E");mask-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' stroke='%23000' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-linecap='round' stroke-width='3'%3E%3CanimateTransform attributeName='transform' dur='2s' from='0 12 12' repeatCount='indefinite' to='360 12 12' type='rotate'/%3E%3Canimate attributeName='stroke-dasharray' dur='1.5s' keyTimes='0;0.475;1' repeatCount='indefinite' values='0,150;42,150;42,150'/%3E%3Canimate attributeName='stroke-dashoffset' dur='1.5s' keyTimes='0;0.475;1' repeatCount='indefinite' values='0;-16;-59'/%3E%3C/circle%3E%3C/svg%3E");-webkit-mask-position:center;mask-position:center;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:100%;mask-size:100%;pointer-events:none;vertical-align:middle;width:calc(var(--size-selector, .25rem)*6)}.indicator{display:inline-flex;position:relative;width:-moz-max-content;width:max-content}.indicator :where(.indicator-item){bottom:var(--inidicator-b,auto);left:var(--inidicator-s,auto);position:absolute;right:var(--inidicator-e,0);top:var(--inidicator-t,0);translate:var(--inidicator-x,50%) var(--indicator-y,-50%);white-space:nowrap;z-index:1}.progress{-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:color-mix(in oklab,currentColor 20%,transparent);border-radius:var(--radius-box);color:var(--color-base-content);height:.5rem;overflow:hidden;position:relative;width:100%}.progress:indeterminate{animation:progress 5s ease-in-out infinite;background-image:repeating-linear-gradient(90deg,currentColor -1%,currentColor 10%,#0000 0,#0000 90%);background-position-x:15%;background-size:200%}@supports (-moz-appearance:none){.progress:indeterminate::-moz-progress-bar{animation:progress 5s ease-in-out infinite;background-color:transparent;background-image:repeating-linear-gradient(90deg,currentColor -1%,currentColor 10%,#0000 0,#0000 90%);background-position-x:15%;background-size:200%}.progress::-moz-progress-bar{background-color:currentColor;border-radius:var(--radius-box)}}@supports (-webkit-appearance:none){.progress::-webkit-progress-bar{background-color:transparent;border-radius:var(--radius-box)}.progress::-webkit-progress-value{background-color:currentColor;border-radius:var(--radius-box)}}@keyframes progress{50%{background-position-x:-115%}}.input{align-items:center;-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:var(--color-base-100);border:var(--border) solid #0000;border-color:var(--input-color);border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-start-start-radius:var(--join-ss,var(--radius-field));box-shadow:0 1px color-mix(in oklab,var(--input-color) calc(var(--depth)*10%),#0000) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;cursor:text;display:inline-flex;flex-shrink:1;font-size:.875rem;gap:.5rem;height:var(--size);padding-inline:.75rem;position:relative;vertical-align:middle;white-space:nowrap;width:clamp(3rem,20rem,100%);--size:calc(var(--size-field, .25rem)*10);--input-color:color-mix(in oklab,var(--color-base-content) 20%,#0000)}.input:where(input){display:inline-flex}.input :where(input){-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:transparent;border:none;display:inline-flex;height:100%;width:100%}.input :where(input):focus,.input :where(input):focus-within{outline-style:none}@media (forced-colors:active){.input :where(input):focus,.input :where(input):focus-within{outline:2px solid transparent;outline-offset:2px}}.input:focus,.input:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color) calc(var(--depth)*10%),#0000);isolation:isolate;outline:2px solid var(--input-color);outline-offset:2px}.input:has(>input[disabled]),.input:is(:disabled,[disabled]){background-color:var(--color-base-200);border-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content) 40%,transparent);cursor:not-allowed}.input:has(>input[disabled])::-moz-placeholder,.input:is(:disabled,[disabled])::-moz-placeholder{color:color-mix(in oklab,var(--color-base-content) 20%,transparent)}.input:has(>input[disabled])::placeholder,.input:is(:disabled,[disabled])::placeholder{color:color-mix(in oklab,var(--color-base-content) 20%,transparent)}.input:has(>input[disabled]),.input:is(:disabled,[disabled]){box-shadow:none}.input:has(>input[disabled])>input[disabled]{cursor:not-allowed}.input::-webkit-date-and-time-value{text-align:inherit}.input[type=number]::-webkit-inner-spin-button{margin-block:-.75rem;margin-inline-end:-.75rem}.input::-webkit-calendar-picker-indicator{inset-inline-end:.75em;position:absolute}@keyframes skeleton{0%{background-position:150%}to{background-position:-50%}}.toast{background-color:transparent;bottom:0;display:flex;flex-direction:column;gap:.5rem;inset-inline-end:0;inset-inline-start:auto;margin:1rem;min-width:-moz-fit-content;min-width:fit-content;position:fixed;top:auto;translate:var(--toast-x,0) var(--toast-y,0);white-space:nowrap}.toast>*{animation:toast .25s ease-out}.toast:where(.toast-start){inset-inline-end:auto;inset-inline-start:0;--toast-x:0}.toast:where(.toast-center){inset-inline-end:50%;inset-inline-start:50%;--toast-x:-50%}.toast:where(.toast-end){inset-inline-end:0;inset-inline-start:auto;--toast-x:0}.toast:where(.toast-bottom){bottom:0;top:auto;--toast-y:0}.toast:where(.toast-middle){bottom:auto;top:50%;--toast-y:-50%}.toast:where(.toast-top){bottom:auto;top:0;--toast-y:0}@keyframes toast{0%{opacity:0;scale:.9}to{opacity:1;scale:1}}.label{align-items:center;color:color-mix(in oklab,currentColor 60%,transparent);display:inline-flex;gap:.375rem;white-space:nowrap}.label:has(input){cursor:pointer}.label:is(.input>*,.select>*){align-items:center;display:flex;font-size:inherit;height:calc(100% - .5rem);padding-inline:.75rem;white-space:nowrap}.label:is(.input>*,.select>*):first-child{border-inline-end:var(--border) solid color-mix(in oklab,currentColor 10%,#0000);margin-inline-end:.75rem;margin-inline-start:-.75rem}.label:is(.input>*,.select>*):last-child{border-inline-start:var(--border) solid color-mix(in oklab,currentColor 10%,#0000);margin-inline-end:-.75rem;margin-inline-start:.75rem}.steps .step{display:grid;grid-template-columns:repeat(1,minmax(0,1fr));grid-template-columns:auto;grid-template-rows:repeat(2,minmax(0,1fr));grid-template-rows:40px 1fr;min-width:4rem;place-items:center;text-align:center;--step-bg:var(--color-base-300);--step-fg:var(--color-base-content)}.steps .step:before{background-color:var(--step-bg);border:1px solid;color:var(--step-bg);grid-column-start:1;grid-row-start:1;height:.5rem;top:0;width:100%;--tw-content:"";content:var(--tw-content);margin-inline-start:-100%}.steps .step:not(:has(.step-icon)):after,.steps .step>.step-icon{background-color:var(--step-bg);border:1px solid var(--step-bg);border-radius:calc(infinity*1px);color:var(--step-fg);content:counter(step);counter-increment:step;display:grid;grid-column-start:1;grid-row-start:1;height:2rem;place-items:center;place-self:center;position:relative;width:2rem;z-index:1}.steps .step:first-child:before{content:none}.steps .step[data-content]:after{content:attr(data-content)}.steps-horizontal .step{display:grid;grid-template-columns:repeat(1,minmax(0,1fr));grid-template-columns:auto;grid-template-rows:repeat(2,minmax(0,1fr));grid-template-rows:40px 1fr;min-width:4rem;place-items:center;text-align:center}.steps-horizontal .step:before{content:"";height:.5rem;margin-inline-start:-100%;translate:0;width:100%}[dir=rtl] .steps-horizontal .step:before{translate:0}.steps-vertical .step{display:grid;gap:.5rem;grid-template-columns:repeat(2,minmax(0,1fr));grid-template-columns:40px 1fr;grid-template-rows:repeat(1,minmax(0,1fr));grid-template-rows:auto;justify-items:start;min-height:4rem}.steps-vertical .step:before{height:100%;margin-inline-start:50%;translate:-50% -50%;width:.5rem}[dir=rtl] .steps-vertical .step:before{translate:50% -50%}.link{cursor:pointer;text-decoration-line:underline}.link:focus{outline-style:none}@media (forced-colors:active){.link:focus{outline:2px solid transparent;outline-offset:2px}}.link:focus-visible{outline:2px solid currentColor;outline-offset:2px}.list{display:flex;flex-direction:column;font-size:.875rem}.list :where(.list-row){--list-grid-cols:minmax(0,auto) 1fr;border-radius:var(--radius-box);display:grid;gap:1rem;grid-auto-flow:column;grid-template-columns:var(--list-grid-cols);padding:1rem;position:relative;word-break:break-word}.list :where(.list-row):has(.list-col-grow:first-child){--list-grid-cols:1fr}.list :where(.list-row):has(.list-col-grow:nth-child(2)){--list-grid-cols:minmax(0,auto) 1fr}.list :where(.list-row):has(.list-col-grow:nth-child(3)){--list-grid-cols:minmax(0,auto) minmax(0,auto) 1fr}.list :where(.list-row):has(.list-col-grow:nth-child(4)){--list-grid-cols:minmax(0,auto) minmax(0,auto) minmax(0,auto) 1fr}.list :where(.list-row):has(.list-col-grow:nth-child(5)){--list-grid-cols:minmax(0,auto) minmax(0,auto) minmax(0,auto) minmax(0,auto) 1fr}.list :where(.list-row):has(.list-col-grow:nth-child(6)){--list-grid-cols:minmax(0,auto) minmax(0,auto) minmax(0,auto) minmax(0,auto) minmax(0,auto) 1fr}.list :where(.list-row) :not(.list-col-wrap){grid-row-start:1}.list>:not(:last-child) .list-row:after,.list>:not(:last-child).list-row:after{border-bottom:var(--border) solid;border-color:color-mix(in oklab,var(--color-base-content) 5%,transparent);content:"";inset-inline:var(--radius-box);bottom:0;position:absolute}.menu{display:flex;flex-direction:column;flex-wrap:wrap;padding:.5rem;width:-moz-fit-content;width:fit-content;--menu-active-fg:var(--color-neutral-content);--menu-active-bg:var(--color-neutral);font-size:.875rem}.menu :where(li ul){margin-inline-start:1rem;padding-inline-start:.5rem;position:relative;white-space:nowrap}.menu :where(li ul):before{background-color:var(--color-base-content);bottom:.75rem;content:"";inset-inline-start:0;opacity:10%;position:absolute;top:.75rem;width:var(--border)}.menu :where(li>.menu-dropdown:not(.menu-dropdown-show)){display:none}.menu :where(li:not(.menu-title)>:not(ul,details,.menu-title,.btn)),.menu :where(li:not(.menu-title)>details>summary:not(.menu-title)){align-content:flex-start;align-items:center;border-radius:var(--radius-field);display:grid;gap:.5rem;grid-auto-columns:minmax(auto,max-content) auto max-content;grid-auto-flow:column;padding-block:.375rem;padding-inline:.75rem;text-align:start;text-wrap:balance;transition-duration:.2s;transition-property:color,background-color,box-shadow;transition-timing-function:cubic-bezier(0,0,.2,1);-webkit-user-select:none;-moz-user-select:none;user-select:none}.menu :where(li>details>summary){outline-style:none}@media (forced-colors:active){.menu :where(li>details>summary){outline:2px solid transparent;outline-offset:2px}}.menu :where(li>details>summary)::-webkit-details-marker{display:none}.menu :where(li>.menu-dropdown-toggle):after,.menu :where(li>details>summary):after{box-shadow:inset 2px 2px;content:"";display:block;height:.375rem;justify-self:flex-end;pointer-events:none;rotate:-135deg;transform-origin:50% 50%;transition-duration:.2s;transition-property:rotate,translate;translate:0 -1px;width:.375rem}.menu :where(li>.menu-dropdown-toggle.menu-dropdown-show):after,.menu :where(li>details[open]>summary):after{rotate:45deg;translate:0 1px}.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn).menu-focus,.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn):focus-visible{background-color:color-mix(in oklab,var(--color-base-content) 10%,transparent);color:var(--color-base-content);cursor:pointer;outline-style:none}@media (forced-colors:active){.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn).menu-focus,.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title),li:not(.menu-title,.disabled)>details>summary:not(.menu-title)):not(.menu-active,:active,.btn):focus-visible{outline:2px solid transparent;outline-offset:2px}}.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){background-color:color-mix(in oklab,var(--color-base-content) 10%,transparent);cursor:pointer;outline-style:none}@media (forced-colors:active){.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){outline:2px solid transparent;outline-offset:2px}}.menu :where(li:not(.menu-title,.disabled)>:not(ul,details,.menu-title):not(.menu-active,:active,.btn):hover,li:not(.menu-title,.disabled)>details>summary:not(.menu-title):not(.menu-active,:active,.btn):hover){box-shadow:inset 0 1px #00000003,inset 0 -1px #ffffff03}.menu :where(li:empty){background-color:var(--color-base-content);height:1px;margin:.5rem 1rem;opacity:10%}.menu :where(li){align-items:stretch;display:flex;flex-direction:column;flex-shrink:0;flex-wrap:wrap;position:relative}.menu :where(li) .badge{justify-self:flex-end}.menu :where(li)>:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>:not(ul,.menu-title,details,.btn):active,.menu :where(li)>details>summary:active{outline-style:none}@media (forced-colors:active){.menu :where(li)>:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>:not(ul,.menu-title,details,.btn):active,.menu :where(li)>details>summary:active{outline:2px solid transparent;outline-offset:2px}}.menu :where(li)>:not(ul,.menu-title,details,.btn).menu-active,.menu :where(li)>:not(ul,.menu-title,details,.btn):active,.menu :where(li)>details>summary:active{background-color:var(--menu-active-bg);background-image:none,var(--fx-noise);background-size:auto,calc(var(--noise)*100%);color:var(--menu-active-fg)}.menu :where(li)>:not(ul,.menu-title,details,.btn).menu-active:not(.menu :where(li)>:not(ul,.menu-title,details,.btn).menu-active:active),.menu :where(li)>:not(ul,.menu-title,details,.btn):active:not(.menu :where(li)>:not(ul,.menu-title,details,.btn):active:active),.menu :where(li)>details>summary:active:not(.menu :where(li)>details>summary:active:active){box-shadow:0 2px calc(var(--depth)*3px) -2px var(--menu-active-bg)}.menu :where(li).menu-disabled{color:color-mix(in oklab,var(--color-base-content) 20%,transparent);pointer-events:none}.menu .dropdown:focus-within .menu-dropdown-toggle:after{rotate:45deg;translate:0 1px}.menu .\\!dropdown:focus-within .menu-dropdown-toggle:after{rotate:45deg!important;translate:0 1px!important}.menu .dropdown-content{margin-top:.5rem;padding:.5rem}.menu .dropdown-content:before{display:none}.stack{display:inline-grid;grid-template-columns:3px 4px 1fr 4px 3px;grid-template-rows:3px 4px 1fr 4px 3px}.stack>*{height:100%;width:100%}.stack>:nth-child(n+2){opacity:70%;width:100%}.stack>:nth-child(2){opacity:90%;z-index:2}.stack>:first-child{width:100%;z-index:3}.stack.stack-bottom>*,.stack>*{grid-column:3/4;grid-row:3/6}.stack.stack-bottom>:nth-child(2),.stack>:nth-child(2){grid-column:2/5;grid-row:2/5}.stack.stack-bottom>:first-child,.stack>:first-child{grid-column:1/6;grid-row:1/4}.stack.stack-top>*{grid-column:3/4;grid-row:1/4}.stack.stack-top>:nth-child(2){grid-column:2/5;grid-row:2/5}.stack.stack-top>:first-child{grid-column:1/6;grid-row:3/6}.stack.stack-start>*{grid-column:1/4;grid-row:3/4}.stack.stack-start>:nth-child(2){grid-column:2/5;grid-row:2/5}.stack.stack-start>:first-child{grid-column:3/6;grid-row:1/6}.stack.stack-end>*{grid-column:3/6;grid-row:3/4}.stack.stack-end>:nth-child(2){grid-column:2/5;grid-row:2/5}.stack.stack-end>:first-child{grid-column:1/4;grid-row:1/6}.filter{display:flex;flex-wrap:wrap}.filter input[type=radio]{width:auto}.filter input{opacity:100%;overflow:hidden;scale:1;transition:margin .1s,opacity .3s,padding .3s,border-width .1s}.filter input:not(:last-child){margin-inline-end:.25rem}.filter input.filter-reset{aspect-ratio:1/1}.filter input.filter-reset:after{content:"×"}.filter:not(:has(input:checked:not(.filter-reset))) .filter-reset,.filter:not(:has(input:checked:not(.filter-reset))) input[type=reset]{border-width:0;margin-inline:0;opacity:0;padding-inline:0;scale:0;width:0}.filter:has(input:checked:not(.filter-reset)) input:not(:checked,.filter-reset,input[type=reset]){border-width:0;margin-inline:0;opacity:0;padding-inline:0;scale:0;width:0}.mockup-browser .mockup-browser-toolbar .input{align-items:center;background-color:var(--color-base-200);direction:ltr;display:flex;font-size:.75rem;gap:.5rem;height:100%;margin-inline:auto;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mockup-browser .mockup-browser-toolbar .input:before{background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='currentColor' class='size-4' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06zM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0' clip-rule='evenodd'/%3E%3C/svg%3E");content:"";height:1rem;opacity:30%;width:1rem}.range{-webkit-appearance:none;-moz-appearance:none;appearance:none;webkit-appearance:none;--range-thumb:var(--color-base-100);--range-thumb-size:calc(var(--size-selector, .25rem)*6);--range-progress:currentColor;--range-fill:1;--range-p:.25rem;--range-bg:color-mix(in oklab,currentColor 10%,#0000);background-color:transparent;cursor:pointer;overflow:hidden;vertical-align:middle;width:clamp(3rem,20rem,100%);--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector));border:none;border-radius:calc(var(--radius-selector) + min(var(--range-p),var(--radius-selector-max)));height:var(--range-thumb-size)}[dir=rtl] .range{--range-dir:-1}.range:focus{outline:none}.range:focus-visible{outline:2px solid;outline-offset:2px}.range::-webkit-slider-runnable-track{background-color:var(--range-bg);border-radius:var(--radius-selector);height:calc(var(--range-thumb-size)*.5);width:100%}@media (forced-colors:active){.range::-webkit-slider-runnable-track{border:1px solid}}.range::-webkit-slider-thumb{-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:currentColor;border:var(--range-p) solid;border-radius:calc(var(--radius-selector) + min(var(--range-p),var(--radius-selector-max)));box-sizing:border-box;height:var(--range-thumb-size);position:relative;width:var(--range-thumb-size);webkit-appearance:none;box-shadow:0 -1px oklch(0 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000),0 0 0 2rem var(--range-thumb) inset,calc(var(--range-dir, 1)*-100rem - var(--range-dir, 1)*var(--range-thumb-size)/2) 0 0 calc(100rem*var(--range-fill));color:var(--range-progress);top:50%;transform:translateY(-50%)}.range::-moz-range-track{background-color:var(--range-bg);border-radius:var(--radius-selector);height:calc(var(--range-thumb-size)*.5);width:100%}@media (forced-colors:active){.range::-moz-range-track{border:1px solid}}.range::-moz-range-thumb{background-color:currentColor;border:var(--range-p) solid;border-radius:calc(var(--radius-selector) + min(var(--range-p),var(--radius-selector-max)));box-shadow:0 -1px oklch(0 0 0/calc(var(--depth)*.1)) inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1)) inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000),0 0 0 2rem var(--range-thumb) inset,calc(var(--range-dir, 1)*-100rem - var(--range-dir, 1)*var(--range-thumb-size)/2) 0 0 calc(100rem*var(--range-fill));box-sizing:border-box;color:var(--range-progress);height:var(--range-thumb-size);position:relative;top:50%;width:var(--range-thumb-size)}.range:disabled{cursor:not-allowed;opacity:30%}.input{background-color:var(--color-background);border:1px solid var(--color-border);border-radius:.375rem;color:var(--color-text);padding:.75rem;transition:var(--transition-colors);width:100%}.input:focus{border-color:var(--color-primary);box-shadow:0 0 0 3px #3b82f61a;outline:none}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{top:0;right:0;bottom:0;left:0}.right-0{right:0}.z-40{z-index:40}.z-50{z-index:50}.mx-auto{margin-left:auto;margin-right:auto}.mb-2{margin-bottom:.5rem}.mb-3{margin-bottom:.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.mr-2{margin-right:.5rem}.mr-20{margin-right:5rem}.mt-12{margin-top:3rem}.mt-2{margin-top:.5rem}.mt-8{margin-top:2rem}.line-clamp-2{display:-webkit-box;overflow:hidden;-webkit-box-orient:vertical;-webkit-line-clamp:2}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.h-12{height:3rem}.h-24{height:6rem}.h-5{height:1.25rem}.h-8{height:2rem}.h-full{height:100%}.max-h-60{max-height:15rem}.min-h-screen{min-height:100vh}.w-12{width:3rem}.w-24{width:6rem}.w-32{width:8rem}.w-5{width:1.25rem}.w-8{width:2rem}.w-full{width:100%}.max-w-4xl{max-width:56rem}.max-w-full{max-width:100%}.max-w-md{max-width:28rem}.max-w-none{max-width:none}.flex-1{flex:1 1 0%}.flex-shrink-0{flex-shrink:0}.flex-grow{flex-grow:1}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.cursor-pointer{cursor:pointer}.resize{resize:both}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-4{gap:1rem}.gap-8{gap:2rem}.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(.5rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(.5rem*var(--tw-space-x-reverse))}.space-x-4>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(1rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(1rem*var(--tw-space-x-reverse))}.space-x-6>:not([hidden])~:not([hidden]){--tw-space-x-reverse:0;margin-left:calc(1.5rem*(1 - var(--tw-space-x-reverse)));margin-right:calc(1.5rem*var(--tw-space-x-reverse))}.space-y-1>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(.25rem*var(--tw-space-y-reverse));margin-top:calc(.25rem*(1 - var(--tw-space-y-reverse)))}.space-y-2>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(.5rem*var(--tw-space-y-reverse));margin-top:calc(.5rem*(1 - var(--tw-space-y-reverse)))}.space-y-4>:not([hidden])~:not([hidden]){--tw-space-y-reverse:0;margin-bottom:calc(1rem*var(--tw-space-y-reverse));margin-top:calc(1rem*(1 - var(--tw-space-y-reverse)))}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-y-auto{overflow-y:auto}.rounded{border-radius:.25rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:.5rem}.rounded-md{border-radius:.375rem}.border{border-width:1px}.border-b{border-bottom-width:1px}.border-t{border-top-width:1px}.border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235/var(--tw-border-opacity,1))}.border-gray-300{--tw-border-opacity:1;border-color:rgb(209 213 219/var(--tw-border-opacity,1))}.border-gray-700{--tw-border-opacity:1;border-color:rgb(55 65 81/var(--tw-border-opacity,1))}.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity,1))}.bg-blue-50{--tw-bg-opacity:1;background-color:rgb(239 246 255/var(--tw-bg-opacity,1))}.bg-blue-600{--tw-bg-opacity:1;background-color:rgb(37 99 235/var(--tw-bg-opacity,1))}.bg-blue-900\\/20{background-color:#1e3a8a33}.bg-gray-100{--tw-bg-opacity:1;background-color:rgb(243 244 246/var(--tw-bg-opacity,1))}.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235/var(--tw-bg-opacity,1))}.bg-gray-600{--tw-bg-opacity:1;background-color:rgb(75 85 99/var(--tw-bg-opacity,1))}.bg-green-600{--tw-bg-opacity:1;background-color:rgb(22 163 74/var(--tw-bg-opacity,1))}.bg-red-100{--tw-bg-opacity:1;background-color:rgb(254 226 226/var(--tw-bg-opacity,1))}.bg-red-900{--tw-bg-opacity:1;background-color:rgb(127 29 29/var(--tw-bg-opacity,1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}.bg-opacity-50{--tw-bg-opacity:.5}.p-2{padding:.5rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.px-2{padding-left:.5rem;padding-right:.5rem}.px-3{padding-left:.75rem;padding-right:.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.py-1{padding-bottom:.25rem;padding-top:.25rem}.py-2{padding-bottom:.5rem;padding-top:.5rem}.py-3{padding-bottom:.75rem;padding-top:.75rem}.py-4{padding-bottom:1rem;padding-top:1rem}.text-left{text-align:left}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.text-blue-400{--tw-text-opacity:1;color:rgb(96 165 250/var(--tw-text-opacity,1))}.text-blue-600{--tw-text-opacity:1;color:rgb(37 99 235/var(--tw-text-opacity,1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity,1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99/var(--tw-text-opacity,1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81/var(--tw-text-opacity,1))}.text-gray-900{--tw-text-opacity:1;color:rgb(17 24 39/var(--tw-text-opacity,1))}.text-red-500{--tw-text-opacity:1;color:rgb(239 68 68/var(--tw-text-opacity,1))}.text-red-600{--tw-text-opacity:1;color:rgb(220 38 38/var(--tw-text-opacity,1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity,1))}.shadow{--tw-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1);--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color)}.shadow,.shadow-lg{box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -4px rgba(0,0,0,.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color),0 4px 6px -4px var(--tw-shadow-color)}.shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1)}.transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.transition-shadow{transition-property:box-shadow;transition-timing-function:cubic-bezier(.4,0,.2,1)}.duration-150,.transition-shadow{transition-duration:.15s}.duration-200{transition-duration:.2s}.join{align-items:stretch;display:inline-flex;--join-ss:0;--join-se:0;--join-es:0;--join-ee:0}.join :where(.join-item){border-end-end-radius:var(--join-ee,0);border-end-start-radius:var(--join-es,0);border-start-end-radius:var(--join-se,0);border-start-start-radius:var(--join-ss,0)}.join :where(.join-item) *{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}.join>.join-item:where(:first-child){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}.join :first-child:not(:last-child) :where(.join-item){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}.join>.join-item:where(:last-child){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}.join :last-child:not(:first-child) :where(.join-item){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}:root .prose{--tw-prose-body:color-mix(in oklab,var(--color-base-content) 80%,#0000);--tw-prose-headings:var(--color-base-content);--tw-prose-lead:var(--color-base-content);--tw-prose-links:var(--color-base-content);--tw-prose-bold:var(--color-base-content);--tw-prose-counters:var(--color-base-content);--tw-prose-bullets:color-mix(in oklab,var(--color-base-content) 50%,#0000);--tw-prose-hr:color-mix(in oklab,var(--color-base-content) 20%,#0000);--tw-prose-quotes:var(--color-base-content);--tw-prose-quote-borders:color-mix(in oklab,var(--color-base-content) 20%,#0000);--tw-prose-captions:color-mix(in oklab,var(--color-base-content) 50%,#0000);--tw-prose-code:var(--color-base-content);--tw-prose-pre-code:var(--color-neutral-content);--tw-prose-pre-bg:var(--color-neutral);--tw-prose-th-borders:color-mix(in oklab,var(--color-base-content) 50%,#0000);--tw-prose-td-borders:color-mix(in oklab,var(--color-base-content) 20%,#0000);--tw-prose-kbd:color-mix(in oklab,var(--color-base-content) 80%,#0000)}:root{--font-geist-sans:"Geist Sans",system-ui,sans-serif;--font-geist-mono:"Geist Mono",monospace;--color-primary:#3b82f6;--color-primary-hover:#2563eb;--color-secondary:#6b7280;--color-background:#fff;--color-surface:#f9fafb;--color-text:#111827;--color-text-muted:#6b7280;--color-border:#e5e7eb;--color-success:#059669;--color-warning:#d97706;--color-error:#dc2626;--shadow-sm:0 1px 2px 0 rgba(0,0,0,.05);--shadow-md:0 4px 6px -1px rgba(0,0,0,.1);--shadow-lg:0 10px 15px -3px rgba(0,0,0,.1);--transition-colors:color .3s ease,background-color .3s ease,border-color .3s ease}.dark{--color-primary:#3b82f6;--color-primary-hover:#2563eb;--color-secondary:#9ca3af;--color-background:#111827;--color-surface:#1f2937;--color-text:#f9fafb;--color-text-muted:#9ca3af;--color-border:#374151;--color-success:#10b981;--color-warning:#f59e0b;--color-error:#ef4444;--shadow-sm:0 1px 2px 0 rgba(0,0,0,.3);--shadow-md:0 4px 6px -1px rgba(0,0,0,.3);--shadow-lg:0 10px 15px -3px rgba(0,0,0,.3)}.wheel-container{display:inline-block;position:relative}.wheel-spin{transition:transform 3s cubic-bezier(.23,1,.32,1)}.theme-transition{transition:background-color .3s ease,color .3s ease,border-color .3s ease}@media (max-width:768px){.mobile-optimized{font-size:.875rem;line-height:1.25rem}}.dark{color-scheme:dark}.light{color-scheme:light}::-webkit-scrollbar{width:8px}::-webkit-scrollbar-track{--tw-bg-opacity:1;background-color:rgb(243 244 246/var(--tw-bg-opacity,1))}:is(.dark *)::-webkit-scrollbar-track{--tw-bg-opacity:1;background-color:rgb(31 41 55/var(--tw-bg-opacity,1))}::-webkit-scrollbar-thumb{border-radius:9999px;--tw-bg-opacity:1;background-color:rgb(209 213 219/var(--tw-bg-opacity,1))}:is(.dark *)::-webkit-scrollbar-thumb{--tw-bg-opacity:1;background-color:rgb(75 85 99/var(--tw-bg-opacity,1))}::-webkit-scrollbar-thumb:hover{--tw-bg-opacity:1;background-color:rgb(156 163 175/var(--tw-bg-opacity,1))}:is(.dark *)::-webkit-scrollbar-thumb:hover{--tw-bg-opacity:1;background-color:rgb(107 114 128/var(--tw-bg-opacity,1))}.focus-visible{outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000);--tw-ring-opacity:1;--tw-ring-color:rgb(59 130 246/var(--tw-ring-opacity,1));--tw-ring-offset-width:2px;--tw-ring-offset-color:#fff}.focus-visible:is(.dark *){--tw-ring-offset-color:#111827}.loading-spinner{height:2rem;width:2rem}@keyframes spin{to{transform:rotate(1turn)}}.loading-spinner{animation:spin 1s linear infinite;border-bottom-width:2px;border-radius:9999px;--tw-border-opacity:1;border-color:rgb(37 99 235/var(--tw-border-opacity,1))}.card{border-radius:.5rem;border-width:1px;--tw-border-opacity:1;border-color:rgb(229 231 235/var(--tw-border-opacity,1));--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1));padding:1.5rem;--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.card:is(.dark *){--tw-border-opacity:1;border-color:rgb(55 65 81/var(--tw-border-opacity,1));--tw-bg-opacity:1;background-color:rgb(31 41 55/var(--tw-bg-opacity,1))}.input{border-radius:.375rem;border-width:1px;width:100%;--tw-border-opacity:1;border-color:rgb(209 213 219/var(--tw-border-opacity,1));--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1));padding:.5rem .75rem;--tw-text-opacity:1;color:rgb(17 24 39/var(--tw-text-opacity,1));--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.input:focus{--tw-border-opacity:1;border-color:rgb(59 130 246/var(--tw-border-opacity,1));outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000);--tw-ring-opacity:1;--tw-ring-color:rgb(59 130 246/var(--tw-ring-opacity,1))}.input:is(.dark *){--tw-border-opacity:1;border-color:rgb(75 85 99/var(--tw-border-opacity,1));--tw-bg-opacity:1;background-color:rgb(55 65 81/var(--tw-bg-opacity,1));--tw-text-opacity:1;color:rgb(243 244 246/var(--tw-text-opacity,1))}.btn:disabled{cursor:not-allowed;opacity:.5}.link{--tw-text-opacity:1;color:rgb(37 99 235/var(--tw-text-opacity,1));text-decoration-line:underline;transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.link:hover{--tw-text-opacity:1;color:rgb(30 64 175/var(--tw-text-opacity,1))}.link:is(.dark *){--tw-text-opacity:1;color:rgb(96 165 250/var(--tw-text-opacity,1))}.link:hover:is(.dark *){--tw-text-opacity:1;color:rgb(147 197 253/var(--tw-text-opacity,1))}.heading-1{font-size:1.875rem;font-weight:700;line-height:2.25rem;--tw-text-opacity:1;color:rgb(17 24 39/var(--tw-text-opacity,1))}.heading-1:is(.dark *){--tw-text-opacity:1;color:rgb(243 244 246/var(--tw-text-opacity,1))}@media (min-width:768px){.heading-1{font-size:2.25rem;line-height:2.5rem}}.heading-2{font-size:1.5rem;font-weight:600;line-height:2rem;--tw-text-opacity:1;color:rgb(17 24 39/var(--tw-text-opacity,1))}.heading-2:is(.dark *){--tw-text-opacity:1;color:rgb(243 244 246/var(--tw-text-opacity,1))}@media (min-width:768px){.heading-2{font-size:1.875rem;line-height:2.25rem}}.heading-3{font-size:1.25rem;font-weight:500;line-height:1.75rem;--tw-text-opacity:1;color:rgb(17 24 39/var(--tw-text-opacity,1))}.heading-3:is(.dark *){--tw-text-opacity:1;color:rgb(243 244 246/var(--tw-text-opacity,1))}@media (min-width:768px){.heading-3{font-size:1.5rem;line-height:2rem}}.text-muted{--tw-text-opacity:1;color:rgb(75 85 99/var(--tw-text-opacity,1))}.text-muted:is(.dark *){--tw-text-opacity:1;color:rgb(156 163 175/var(--tw-text-opacity,1))}.text-accent{--tw-text-opacity:1;color:rgb(37 99 235/var(--tw-text-opacity,1))}.text-accent:is(.dark *){--tw-text-opacity:1;color:rgb(96 165 250/var(--tw-text-opacity,1))}.container-custom{margin-left:auto;margin-right:auto;max-width:80rem;padding-left:1rem;padding-right:1rem}@media (min-width:640px){.container-custom{padding-left:1.5rem;padding-right:1.5rem}}@media (min-width:1024px){.container-custom{padding-left:2rem;padding-right:2rem}}.grid-auto-fit{grid-template-columns:repeat(auto-fit,minmax(300px,1fr))}.shadow-custom{box-shadow:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f}.dark .shadow-custom{box-shadow:0 4px 6px -1px #0000004d,0 2px 4px -1px #0003}.transition-custom{transition:all .3s cubic-bezier(.4,0,.2,1)}.hover-lift:hover{transform:translateY(-2px)}.gradient-bg{background:linear-gradient(135deg,#667eea,#764ba2)}.dark .gradient-bg{background:linear-gradient(135deg,#4c63d2,#5a4fcf)}.first\\:rounded-t-lg:first-child{border-top-left-radius:.5rem;border-top-right-radius:.5rem}.last\\:rounded-b-lg:last-child{border-bottom-left-radius:.5rem;border-bottom-right-radius:.5rem}.hover\\:border-blue-300:hover{--tw-border-opacity:1;border-color:rgb(147 197 253/var(--tw-border-opacity,1))}.hover\\:bg-blue-700:hover{--tw-bg-opacity:1;background-color:rgb(29 78 216/var(--tw-bg-opacity,1))}.hover\\:bg-gray-100:hover{--tw-bg-opacity:1;background-color:rgb(243 244 246/var(--tw-bg-opacity,1))}.hover\\:bg-gray-300:hover{--tw-bg-opacity:1;background-color:rgb(209 213 219/var(--tw-bg-opacity,1))}.hover\\:bg-gray-700:hover{--tw-bg-opacity:1;background-color:rgb(55 65 81/var(--tw-bg-opacity,1))}.hover\\:bg-green-700:hover{--tw-bg-opacity:1;background-color:rgb(21 128 61/var(--tw-bg-opacity,1))}.hover\\:text-gray-700:hover{--tw-text-opacity:1;color:rgb(55 65 81/var(--tw-text-opacity,1))}.hover\\:text-gray-900:hover{--tw-text-opacity:1;color:rgb(17 24 39/var(--tw-text-opacity,1))}.hover\\:text-red-700:hover{--tw-text-opacity:1;color:rgb(185 28 28/var(--tw-text-opacity,1))}.hover\\:opacity-80:hover{opacity:.8}.hover\\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -4px rgba(0,0,0,.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color),0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.focus\\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)}.focus\\:ring-blue-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(59 130 246/var(--tw-ring-opacity,1))}.focus\\:ring-offset-2:focus{--tw-ring-offset-width:2px}.focus\\:ring-offset-white:focus{--tw-ring-offset-color:#fff}.dark\\:border-gray-600:is(.dark *){--tw-border-opacity:1;border-color:rgb(75 85 99/var(--tw-border-opacity,1))}.dark\\:border-gray-700:is(.dark *){--tw-border-opacity:1;border-color:rgb(55 65 81/var(--tw-border-opacity,1))}.dark\\:bg-blue-900\\/20:is(.dark *){background-color:#1e3a8a33}.dark\\:bg-gray-700:is(.dark *){--tw-bg-opacity:1;background-color:rgb(55 65 81/var(--tw-bg-opacity,1))}.dark\\:bg-gray-800:is(.dark *){--tw-bg-opacity:1;background-color:rgb(31 41 55/var(--tw-bg-opacity,1))}.dark\\:bg-gray-900:is(.dark *){--tw-bg-opacity:1;background-color:rgb(17 24 39/var(--tw-bg-opacity,1))}.dark\\:bg-red-900\\/20:is(.dark *){background-color:#7f1d1d33}.dark\\:text-blue-400:is(.dark *){--tw-text-opacity:1;color:rgb(96 165 250/var(--tw-text-opacity,1))}.dark\\:text-gray-100:is(.dark *){--tw-text-opacity:1;color:rgb(243 244 246/var(--tw-text-opacity,1))}.dark\\:text-gray-300:is(.dark *){--tw-text-opacity:1;color:rgb(209 213 219/var(--tw-text-opacity,1))}.dark\\:text-gray-400:is(.dark *){--tw-text-opacity:1;color:rgb(156 163 175/var(--tw-text-opacity,1))}.dark\\:text-red-400:is(.dark *){--tw-text-opacity:1;color:rgb(248 113 113/var(--tw-text-opacity,1))}.dark\\:hover\\:border-blue-600:hover:is(.dark *){--tw-border-opacity:1;border-color:rgb(37 99 235/var(--tw-border-opacity,1))}.dark\\:hover\\:bg-gray-600:hover:is(.dark *){--tw-bg-opacity:1;background-color:rgb(75 85 99/var(--tw-bg-opacity,1))}.dark\\:hover\\:bg-gray-700:hover:is(.dark *){--tw-bg-opacity:1;background-color:rgb(55 65 81/var(--tw-bg-opacity,1))}.dark\\:hover\\:text-gray-100:hover:is(.dark *){--tw-text-opacity:1;color:rgb(243 244 246/var(--tw-text-opacity,1))}.dark\\:hover\\:text-gray-200:hover:is(.dark *){--tw-text-opacity:1;color:rgb(229 231 235/var(--tw-text-opacity,1))}.dark\\:focus\\:ring-offset-gray-900:focus:is(.dark *){--tw-ring-offset-color:#111827}@media (min-width:768px){.md\\:flex{display:flex}.md\\:hidden{display:none}.md\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\\:text-3xl{font-size:1.875rem;line-height:2.25rem}}@media (min-width:1024px){.lg\\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}`;
export {
  main as default
};
//# sourceMappingURL=entry-styles-1.mjs-BT4JzVOk.js.map
