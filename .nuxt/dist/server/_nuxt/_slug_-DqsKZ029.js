import { v as validate<PERSON>heelSlug, u as useFetch, b as getServerWheelPageData, a as getWheelSeoData, _ as __nuxt_component_0 } from "./fetch-XUqwCWpD.js";
import { defineComponent, withAsyncContext, computed, resolveComponent, unref, useSSRContext } from "vue";
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList } from "vue/server-renderer";
import { b as useRoute, c as createError, u as useSeoMeta, a as useHead, _ as _export_sfc } from "../server.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/hookable/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/ohash/dist/index.mjs";
import "@vue/shared";
import "/Users/<USER>/Webs/makechoice/node_modules/perfect-debounce/dist/index.mjs";
import "ofetch";
import "#internal/nuxt/paths";
import "/Users/<USER>/Webs/makechoice/node_modules/unctx/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/h3/dist/index.mjs";
import "vue-router";
import "/Users/<USER>/Webs/makechoice/node_modules/radix3/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/defu/dist/defu.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/ufo/dist/index.mjs";
import "/Users/<USER>/Webs/makechoice/node_modules/@unhead/vue/dist/index.mjs";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[slug]",
  __ssrInlineRender: true,
  async setup(__props) {
    let __temp, __restore;
    const route = useRoute();
    const slug = route.params.slug;
    if (!validateWheelSlug(slug)) {
      throw createError({
        statusCode: 404,
        statusMessage: "Wheel not found"
      });
    }
    const { data } = ([__temp, __restore] = withAsyncContext(() => useFetch(`/api/wheel-page/${slug}`, {
      server: true,
      default: () => ({
        currentWheel: null,
        otherWheels: [],
        success: false
      })
    }, "$NLvOxhyXYY")), __temp = await __temp, __restore(), __temp);
    if (!data.value.success || !data.value.currentWheel) {
      try {
        const serverData = ([__temp, __restore] = withAsyncContext(() => getServerWheelPageData(slug)), __temp = await __temp, __restore(), __temp);
        data.value = {
          currentWheel: serverData.wheel,
          otherWheels: serverData.otherWheels,
          success: true,
          message: "Data loaded from server"
        };
      } catch (error) {
        throw createError({
          statusCode: 404,
          statusMessage: "Wheel not found"
        });
      }
    }
    const wheel = computed(() => data.value.currentWheel);
    const otherWheels = computed(() => data.value.otherWheels);
    const seoData = getWheelSeoData(slug);
    useSeoMeta({
      title: (seoData == null ? void 0 : seoData.title) || wheel.value.seo.title,
      description: (seoData == null ? void 0 : seoData.description) || wheel.value.seo.description,
      keywords: (seoData == null ? void 0 : seoData.keywords) || wheel.value.seo.keywords.join(", "),
      ogTitle: (seoData == null ? void 0 : seoData.title) || wheel.value.seo.title,
      ogDescription: (seoData == null ? void 0 : seoData.description) || wheel.value.seo.description,
      ogImage: (seoData == null ? void 0 : seoData.ogImage) || "https://decisionsmaker.online/og-image.png",
      ogUrl: (seoData == null ? void 0 : seoData.canonical) || `https://decisionsmaker.online/${slug}`,
      twitterCard: "summary_large_image",
      twitterTitle: (seoData == null ? void 0 : seoData.title) || wheel.value.seo.title,
      twitterDescription: (seoData == null ? void 0 : seoData.description) || wheel.value.seo.description
    });
    useHead({
      link: [
        {
          rel: "canonical",
          href: (seoData == null ? void 0 : seoData.canonical) || `https://decisionsmaker.online/${slug}`
        }
      ]
    });
    useJsonld({
      "@context": "https://schema.org",
      "@type": "WebApplication",
      name: wheel.value.name,
      description: wheel.value.seo.description,
      url: `https://decisionsmaker.online/${slug}`,
      applicationCategory: "UtilityApplication",
      operatingSystem: "Any",
      offers: {
        "@type": "Offer",
        price: "0",
        priceCurrency: "USD"
      },
      creator: {
        "@type": "Organization",
        name: "DecisionsMaker Online"
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_AppHeader = resolveComponent("AppHeader");
      const _component_WheelInteractive = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(_attrs)} data-v-236cd69b>`);
      _push(ssrRenderComponent(_component_AppHeader, null, null, _parent));
      _push(`<div class="min-h-screen py-4 bg-white dark:bg-gray-900 overflow-hidden" id="wheelContainer" data-v-236cd69b><div class="h-full flex flex-col items-center max-w-full" data-v-236cd69b><h1 class="text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100 flex-shrink-0" data-v-236cd69b>${ssrInterpolate(unref(wheel).name)}</h1><div class="flex flex-col items-center flex-grow w-full" data-v-236cd69b>`);
      _push(ssrRenderComponent(_component_WheelInteractive, {
        "initial-prizes": unref(wheel).prizes,
        "wheel-id": unref(wheel).slug,
        title: unref(wheel).name,
        "other-wheels": unref(otherWheels)
      }, null, _parent));
      _push(`</div></div>`);
      if (unref(wheel).articles && unref(wheel).articles.length > 0) {
        _push(`<div class="max-w-4xl mx-auto px-4 mt-12" data-v-236cd69b><div class="grid gap-8 md:grid-cols-2" data-v-236cd69b><!--[-->`);
        ssrRenderList(unref(wheel).articles, (article, index) => {
          _push(`<article class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700" data-v-236cd69b><h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100" data-v-236cd69b>${ssrInterpolate(article.title)}</h2><div class="prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300" data-v-236cd69b>${article.content ?? ""}</div></article>`);
        });
        _push(`<!--]--></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/[slug].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _slug_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-236cd69b"]]);
export {
  _slug_ as default
};
//# sourceMappingURL=_slug_-DqsKZ029.js.map
