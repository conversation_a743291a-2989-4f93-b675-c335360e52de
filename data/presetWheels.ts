import type { PresetWheel, WheelListItem } from '~/types';

export const presetWheels: PresetWheel[] = [
  {
    id: 'yes-no',
    name: 'Yes or No Decision Maker',
    slug: 'yes-no-decision-maker',
    isDefault: true,
    prizes: [
      { text: "Yes", color: "#22c55e" },
      { text: "No", color: "#ef4444" },
    ],
    seo: {
      title: 'Yes or No Decision Maker - Quick Binary Choice Wheel',
      description: 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.',
      keywords: ['yes no decision', 'binary choice', 'decision maker', 'quick decision']
    },
    articles: [
      {
        title: "When to Use a Yes/No Decision Wheel",
        content: `
          <p>
            Sometimes the simplest decisions are the hardest to make. Our Yes/No decision wheel is perfect for those moments when you're stuck between two options and need a quick, unbiased choice.
          </p>
          <p>
            Whether you're deciding whether to go out tonight, try a new restaurant, or make a purchase, let the wheel help you break through decision paralysis.
          </p>
        `
      },
      {
        title: "The Psychology of Binary Decisions",
        content: `
          <p>
            Binary decisions (yes/no, this/that) are fundamental to human decision-making. Research shows that when we're truly torn between two options, random selection can be surprisingly satisfying.
          </p>
          <p>
            The key insight is that if you're genuinely indifferent between options, the decision method matters less than actually making a choice and moving forward.
          </p>
        `
      }
    ]
  },
  {
    id: 'what-to-eat',
    name: 'What to Eat Decision Wheel',
    slug: 'what-to-eat-decision-wheel',
    prizes: [
      { text: "Pizza", color: "#ff6b6b" },
      { text: "Chinese Food", color: "#4ecdc4" },
      { text: "Burger", color: "#45b7d1" },
      { text: "Sushi", color: "#96ceb4" },
      { text: "Mexican Food", color: "#feca57" },
      { text: "Italian", color: "#ff9ff3" },
      { text: "Thai Food", color: "#54a0ff" },
      { text: "Cook at Home", color: "#5f27cd" },
    ],
    seo: {
      title: 'What to Eat Decision Wheel - Food Choice Spinner',
      description: 'Cant decide what to eat? Use our food decision wheel to randomly choose from popular meal options. Perfect for dinner decisions!',
      keywords: ['what to eat', 'food decision', 'meal choice', 'dinner decision', 'restaurant picker']
    },
    articles: [
      {
        title: "Solving the Daily 'What to Eat' Dilemma",
        content: `
          <p>
            "What should we eat?" is one of the most common daily decisions we face. Decision fatigue around food choices is real, especially after a long day when your mental energy is depleted.
          </p>
          <p>
            Our food decision wheel takes the stress out of meal planning by randomly selecting from popular food options. It's perfect for couples, families, or groups who can't agree on where to eat.
          </p>
        `
      },
      {
        title: "Tips for Better Food Decisions",
        content: `
          <ul class="list-disc list-inside space-y-2">
            <li>Consider your budget before spinning</li>
            <li>Think about dietary restrictions in your group</li>
            <li>Factor in delivery time and restaurant hours</li>
            <li>Have backup options ready</li>
            <li>Don't overthink it - the goal is to make a decision and enjoy your meal!</li>
          </ul>
        `
      }
    ]
  },
  {
    id: 'weekend-activity',
    name: 'Weekend Activity Planner',
    slug: 'weekend-activity-planner',
    prizes: [
      { text: "Go to Movies", color: "#e74c3c" },
      { text: "Visit Museum", color: "#3498db" },
      { text: "Go Hiking", color: "#27ae60" },
      { text: "Stay Home & Relax", color: "#9b59b6" },
      { text: "Meet Friends", color: "#f39c12" },
      { text: "Try New Restaurant", color: "#e67e22" },
      { text: "Go Shopping", color: "#1abc9c" },
      { text: "Outdoor Sports", color: "#34495e" },
    ],
    seo: {
      title: 'Weekend Activity Planner - Random Activity Generator',
      description: 'Discover fun weekend activities with our activity decision wheel. Get random suggestions for movies, outdoor activities, and more!',
      keywords: ['weekend activities', 'activity planner', 'weekend ideas', 'what to do', 'activity generator']
    },
    articles: [
      {
        title: "Making the Most of Your Weekends",
        content: `
          <p>
            Weekends are precious time for rest, recreation, and personal fulfillment. Yet many people struggle with decision paralysis when it comes to choosing how to spend their free time.
          </p>
          <p>
            Our weekend activity planner helps you break out of routine and try new experiences. Sometimes the best adventures come from unexpected choices!
          </p>
        `
      }
    ]
  },
  {
    id: 'team-picker',
    name: 'Team Member Picker',
    slug: 'team-member-picker',
    prizes: [
      { text: "Alice", color: "#ff7675" },
      { text: "Bob", color: "#74b9ff" },
      { text: "Charlie", color: "#00b894" },
      { text: "Diana", color: "#fdcb6e" },
      { text: "Eve", color: "#e17055" },
      { text: "Frank", color: "#a29bfe" },
    ],
    seo: {
      title: 'Team Member Picker - Random Team Selection Wheel',
      description: 'Fairly select team members, assign tasks, or pick volunteers with our random team picker wheel. Perfect for classrooms and workplaces.',
      keywords: ['team picker', 'random selection', 'team member selector', 'fair selection', 'group picker']
    },
    articles: [
      {
        title: "Fair Team Selection Made Easy",
        content: `
          <p>
            Whether you're a teacher assigning classroom tasks, a manager distributing work, or organizing team activities, fair selection is crucial for maintaining group harmony.
          </p>
          <p>
            Our team picker wheel ensures everyone has an equal chance of being selected, removing bias and making the process transparent and fun.
          </p>
        `
      }
    ]
  }
];

// 根据slug查找转盘
export function findWheelBySlug(slug: string): PresetWheel | undefined {
  return presetWheels.find(wheel => wheel.slug === slug);
}

// 获取默认转盘
export function getDefaultWheel(): PresetWheel {
  return presetWheels.find(wheel => wheel.isDefault) || presetWheels[0];
}

// 获取转盘列表（用于导航）
export function getWheelList(): WheelListItem[] {
  return presetWheels.map(wheel => ({
    id: wheel.id,
    name: wheel.name,
    slug: wheel.slug,
    description: wheel.seo.description
  }));
}
