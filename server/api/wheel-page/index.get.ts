import { getDefaultWheel, getWheelList } from '~/data/presetWheels';
import type { WheelPageResponse } from '~/types';

/**
 * 处理首页请求（slug为空）
 * GET /api/wheel-page
 */
export default defineEventHandler(async (event): Promise<WheelPageResponse> => {
  try {
    // 获取默认转盘（首页）
    const defaultWheel = getDefaultWheel();
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter(w => w.slug !== defaultWheel.slug);

    const response: WheelPageResponse = {
      currentWheel: defaultWheel,
      otherWheels: otherWheels,
      success: true,
      message: 'Default wheel data retrieved successfully'
    };

    return response;
  } catch (error) {
    console.error('Error in wheel-page API:', error);
    
    const response: WheelPageResponse = {
      currentWheel: null,
      otherWheels: [],
      success: false,
      message: 'Internal server error'
    };

    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    });
  }
});
