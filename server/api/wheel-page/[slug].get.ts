import { findWheelBySlug, getWheelList } from '~/data/presetWheels';
import type { WheelPageResponse } from '~/types';

/**
 * 统一的转盘页面接口
 * 返回当前转盘信息和其他转盘列表
 * 
 * @param slug - 转盘的URL slug
 * @returns WheelPageResponse - 包含当前转盘和其他转盘列表的完整响应
 */
export default defineEventHandler(async (event): Promise<WheelPageResponse> => {
  try {
    const slug = getRouterParam(event, 'slug');
    
    if (!slug) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Slug parameter is required'
      });
    }
    
    // 获取当前转盘信息
    const currentWheel = findWheelBySlug(slug);
    
    if (!currentWheel) {
      const response: WheelPageResponse = {
        currentWheel: null,
        otherWheels: [],
        success: false,
        message: `Wheel with slug "${slug}" not found`
      };
      
      throw createError({
        statusCode: 404,
        statusMessage: `Wheel with slug "${slug}" not found`
      });
    }
    
    // 获取所有转盘列表，并过滤掉当前转盘
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter(wheel => wheel.slug !== slug);
    
    const response: WheelPageResponse = {
      currentWheel,
      otherWheels,
      success: true,
      message: 'Wheel data retrieved successfully'
    };

    return response;
  } catch (error) {
    console.error('Error in wheel-page API:', error);
    
    // 如果是已知错误，重新抛出
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error;
    }
    
    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    });
  }
});
