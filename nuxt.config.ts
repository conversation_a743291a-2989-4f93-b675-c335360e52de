// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  // 兼容性日期
  compatibilityDate: '2025-06-26',
  
  // 启用SSR模式以获得更好的SEO
  ssr: true,
  
  // 启用ISR (Incremental Static Regeneration)
  nitro: {
    prerender: {
      routes: ['/']
    },
    storage: {
      redis: {
        driver: 'redis',
        // 生产环境中配置Redis连接
      }
    }
  },
  
  // 模块配置
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxtjs/color-mode',
    '@pinia/nuxt',
    '@nuxt/icon'
  ],
  
  // 颜色模式配置
  colorMode: {
    preference: 'system', // 默认跟随系统
    fallback: 'light', // 回退到浅色模式
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '',
    storageKey: 'nuxt-color-mode'
  },
  
  // CSS配置
  css: [
    '~/assets/css/main.css'
  ],
  
  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅在服务端可用）
    revalidateSecret: process.env.REVALIDATE_SECRET || 'dev-secret-key-123',
    
    // 公共配置（客户端和服务端都可用）
    public: {
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || '/api',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3001'
    }
  },
  
  // 应用配置
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'Decision Maker Online',
      meta: [
        { name: 'description', content: 'Make decisions easily with our online decision wheel tools.' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  },
  
  // 开发服务器配置
  devServer: {
    port: 3001
  },
  
  // TypeScript配置
  typescript: {
    strict: true
  }
})
