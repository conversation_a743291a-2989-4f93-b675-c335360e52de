import type { WheelPageResponse, ApiError } from '~/types';

/**
 * API 工具函数
 */

// 获取运行时配置
const config = useRuntimeConfig();

/**
 * 通用API请求函数
 */
async function apiRequest<T>(endpoint: string): Promise<T> {
  try {
    const response = await $fetch<T>(`${config.public.apiBaseUrl}${endpoint}`);
    return response;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}

/**
 * 获取转盘页面数据
 * @param slug - 转盘slug，为空时获取默认转盘
 */
export async function getWheelPageData(slug?: string): Promise<WheelPageResponse> {
  const endpoint = slug ? `/wheel-page/${slug}` : '/wheel-page';
  return apiRequest<WheelPageResponse>(endpoint);
}

/**
 * 客户端获取转盘页面数据（带错误处理）
 */
export async function fetchWheelPageData(slug?: string): Promise<WheelPageResponse> {
  try {
    return await getWheelPageData(slug);
  } catch (error: any) {
    console.error('Failed to fetch wheel page data:', error);
    
    // 返回错误响应格式
    const errorResponse: WheelPageResponse = {
      currentWheel: null,
      otherWheels: [],
      success: false,
      message: error?.message || 'Failed to fetch wheel data'
    };
    
    return errorResponse;
  }
}

/**
 * 服务端获取转盘页面数据
 */
export async function getServerWheelPageData(slug?: string): Promise<WheelPageResponse> {
  try {
    // 在服务端直接调用API函数，避免HTTP请求
    if (process.server) {
      const { findWheelBySlug, getDefaultWheel, getWheelList } = await import('~/data/presetWheels');
      
      let currentWheel;
      if (slug) {
        currentWheel = findWheelBySlug(slug);
        if (!currentWheel) {
          return {
            currentWheel: null,
            otherWheels: [],
            success: false,
            message: `Wheel with slug "${slug}" not found`
          };
        }
      } else {
        currentWheel = getDefaultWheel();
      }
      
      const allWheels = getWheelList();
      const otherWheels = allWheels.filter(w => w.slug !== currentWheel.slug);
      
      return {
        currentWheel,
        otherWheels,
        success: true,
        message: 'Wheel data retrieved successfully'
      };
    }
    
    // 在客户端使用API请求
    return await getWheelPageData(slug);
  } catch (error: any) {
    console.error('Failed to get server wheel page data:', error);
    
    return {
      currentWheel: null,
      otherWheels: [],
      success: false,
      message: error?.message || 'Failed to fetch wheel data'
    };
  }
}
