@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式 */
@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
  
  body {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}

/* 组件样式 */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
}

/* 转盘相关样式 */
.wheel-container {
  position: relative;
  display: inline-block;
}

.wheel-indicator {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 20px solid #ef4444;
  z-index: 10;
}

.wheel-canvas {
  border-radius: 50%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.1s ease;
}

.wheel-start-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 50%;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
  z-index: 5;
}

.wheel-start-button:hover {
  transform: translate(-50%, -50%) scale(1.05);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.wheel-start-button:active {
  transform: translate(-50%, -50%) scale(0.95);
}

.wheel-start-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: translate(-50%, -50%);
  box-shadow: 0 2px 8px rgba(156, 163, 175, 0.3);
}

/* 富文本内容样式 */
.rich-text-content {
  @apply prose dark:prose-invert max-w-none;
}

.rich-text-content h1,
.rich-text-content h2,
.rich-text-content h3,
.rich-text-content h4,
.rich-text-content h5,
.rich-text-content h6 {
  @apply text-gray-900 dark:text-gray-100;
}

.rich-text-content p {
  @apply text-gray-700 dark:text-gray-300;
}

.rich-text-content a {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wheel-container {
    max-width: 90vw;
  }
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinning {
  animation: spin 3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 暗色模式特定样式 */
.dark .wheel-canvas {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
