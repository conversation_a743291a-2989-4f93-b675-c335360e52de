<template>
  <div>
    <!-- 转盘区域 -->
    <div class="min-h-screen py-4 bg-white dark:bg-gray-900 overflow-hidden" id="wheelContainer">
      <div class="h-full flex flex-col items-center max-w-full">
        <h1 class="text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100 flex-shrink-0">
          {{ wheel?.name || 'Decision Maker' }}
        </h1>
        
        <div class="flex flex-col items-center flex-grow w-full">
          <!-- 转盘组件 -->
          <LuckWheel
            v-if="wheel"
            :prizes="wheel.prizes"
            :wheel-id="wheel.slug"
            :title="wheel.name"
            @prizes-change="handlePrizesChange"
          />
          
          <!-- 加载状态 -->
          <div v-else class="flex items-center justify-center h-64">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文章内容区域 -->
    <div v-if="wheel?.articles?.length" class="bg-gray-50 dark:bg-gray-800 py-16">
      <div class="container mx-auto px-4 max-w-4xl">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <article 
            v-for="article in wheel.articles" 
            :key="article.title"
            class="card"
          >
            <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              {{ article.title }}
            </h2>
            <RichTextContent :content="article.content" />
          </article>
        </div>
      </div>
    </div>

    <!-- 其他转盘推荐 -->
    <div v-if="otherWheels?.length" class="bg-white dark:bg-gray-900 py-16">
      <div class="container mx-auto px-4 max-w-6xl">
        <h2 class="text-2xl font-bold text-center text-gray-900 dark:text-gray-100 mb-8">
          Try Other Decision Wheels
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <NuxtLink
            v-for="otherWheel in otherWheels"
            :key="otherWheel.id"
            :to="`/${otherWheel.slug}`"
            class="card hover:shadow-lg transition-shadow duration-200 group"
          >
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400">
              {{ otherWheel.name }}
            </h3>
            <p class="text-gray-600 dark:text-gray-400 text-sm">
              {{ otherWheel.description }}
            </p>
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PresetWheel, WheelListItem, Prize } from '~/types';

// SEO 元数据
useSeoMeta({
  title: 'Yes or No Decision Maker - Quick Binary Choice Wheel | DecisionsMaker Online',
  description: 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties. Free online decision maker tool.',
  keywords: 'yes no decision, binary choice, decision maker, quick decision, decision wheel, random choice',
  ogTitle: 'Yes or No Decision Maker - Quick Binary Choice Wheel',
  ogDescription: 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices and quick decisions.',
  ogType: 'website',
  twitterCard: 'summary',
  twitterTitle: 'Yes or No Decision Maker - Quick Binary Choice Wheel',
  twitterDescription: 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices and quick decisions.',
});

// 响应式数据
const wheel = ref<PresetWheel | null>(null);
const otherWheels = ref<WheelListItem[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);

// 获取数据
const { data, pending, error: fetchError } = await useLazyAsyncData('home-wheel-data', async () => {
  return await getServerWheelPageData();
});

// 监听数据变化
watch(data, (newData) => {
  if (newData?.success && newData.currentWheel) {
    wheel.value = newData.currentWheel;
    otherWheels.value = newData.otherWheels;
    
    // 更新SEO信息
    if (newData.currentWheel.seo) {
      useSeoMeta({
        title: newData.currentWheel.seo.title,
        description: newData.currentWheel.seo.description,
        keywords: newData.currentWheel.seo.keywords?.join(', '),
        ogTitle: newData.currentWheel.seo.title,
        ogDescription: newData.currentWheel.seo.description,
        twitterTitle: newData.currentWheel.seo.title,
        twitterDescription: newData.currentWheel.seo.description,
      });
    }
  } else {
    error.value = newData?.message || 'Failed to load wheel data';
  }
}, { immediate: true });

// 监听加载状态
watch(pending, (isPending) => {
  loading.value = isPending;
});

// 监听错误
watch(fetchError, (err) => {
  if (err) {
    error.value = err.message || 'An error occurred';
  }
});

// 处理奖品变化
function handlePrizesChange(newPrizes: Prize[]) {
  if (wheel.value) {
    wheel.value.prizes = newPrizes;
  }
}

// 错误处理
if (error.value) {
  throw createError({
    statusCode: 500,
    statusMessage: error.value
  });
}
</script>
