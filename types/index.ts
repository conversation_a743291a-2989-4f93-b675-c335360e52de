// 奖品接口
export interface Prize {
  text: string;
  color: string;
  image?: string;
}

// 转盘组件属性
export interface LuckWheelProps {
  prizes: Prize[];
  size?: number;
  startButtonSize?: number;
  onPrizesChange?: (prizes: Prize[]) => void;
  wheelId?: string; // 用于区分不同页面的localStorage
  title?: string; // 转盘标题，用于全屏模式显示
}

// 预设转盘的SEO信息
export interface WheelSEO {
  title: string;
  description: string;
  keywords?: string[];
}

// 预设转盘的文章内容
export interface WheelArticle {
  title: string;
  content: string;
}

// 预设转盘数据结构
export interface PresetWheel {
  id: string;
  name: string;
  slug: string; // SEO友好的URL路径
  prizes: Prize[];
  seo: WheelSEO;
  articles: WheelArticle[];
  isDefault?: boolean; // 是否为默认转盘
}

// 转盘列表项（用于其他转盘展示）
export interface WheelListItem {
  id: string;
  name: string;
  slug: string;
  description: string;
  image?: string;
}

// API响应接口
export interface WheelPageResponse {
  currentWheel: PresetWheel | null;
  otherWheels: WheelListItem[];
  success: boolean;
  message?: string;
}

// API错误响应
export interface ApiError {
  success: false;
  message: string;
  code?: string;
}

// 主题类型
export type Theme = 'light' | 'dark' | 'system';

// 颜色模式上下文
export interface ColorModeContext {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme: 'light' | 'dark';
}

// 转盘状态
export interface WheelState {
  isSpinning: boolean;
  selectedPrize: Prize | null;
  rotation: number;
}

// 页面元数据
export interface PageMeta {
  title: string;
  description: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonical?: string;
}
