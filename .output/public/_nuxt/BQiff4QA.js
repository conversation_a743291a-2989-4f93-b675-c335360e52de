import{u as _,g as k,a as y,_ as x}from"./HFvYc7_N.js";import{d as w,w as h,c as W,u as v,a as b,r as C,b as l,o as c,e as u,f as s,g as M,t as p,h as r,F as q,i as S,_ as A}from"./DedumHzx.js";const B={class:"min-h-screen py-4 bg-white dark:bg-gray-900 overflow-hidden",id:"wheelContainer"},N={class:"h-full flex flex-col items-center max-w-full"},H={class:"text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100 flex-shrink-0"},I={class:"flex flex-col items-center flex-grow w-full"},z={key:0,class:"max-w-4xl mx-auto px-4 mt-12"},O={class:"grid gap-8 md:grid-cols-2"},Q={class:"text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100"},T=["innerHTML"],F=w({__name:"index",async setup(L){let i,o;const{data:n}=([i,o]=h(()=>_("/api/wheel-page",{server:!0,default:()=>({currentWheel:null,otherWheels:[],success:!1})},"$77hSeXQoip")),i=await i,o(),i);if(!n.value.success||!n.value.currentWheel){const a=([i,o]=h(()=>k()),i=await i,o(),i);n.value={currentWheel:a.defaultWheel,otherWheels:a.otherWheels,success:!0,message:"Data loaded from server"}}const t=W(()=>({defaultWheel:n.value.currentWheel,otherWheels:n.value.otherWheels})),e=y();return v({title:(e==null?void 0:e.title)||"Yes or No Decision Maker - Quick Binary Choice Wheel",description:(e==null?void 0:e.description)||"Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.",keywords:(e==null?void 0:e.keywords)||"yes no decision, binary choice, decision maker, quick decision",ogTitle:(e==null?void 0:e.title)||"Yes or No Decision Maker - Quick Binary Choice Wheel",ogDescription:(e==null?void 0:e.description)||"Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.",ogImage:(e==null?void 0:e.ogImage)||"https://decisionsmaker.online/og-image.png",ogUrl:(e==null?void 0:e.canonical)||"https://decisionsmaker.online",twitterCard:"summary_large_image",twitterTitle:(e==null?void 0:e.title)||"Yes or No Decision Maker - Quick Binary Choice Wheel",twitterDescription:(e==null?void 0:e.description)||"Make quick yes or no decisions with our simple decision wheel."}),b({link:[{rel:"canonical",href:(e==null?void 0:e.canonical)||"https://decisionsmaker.online"}]}),useJsonld({"@context":"https://schema.org","@type":"WebApplication",name:"DecisionsMaker Online",description:"Interactive decision making wheel for quick and fair choices",url:"https://decisionsmaker.online",applicationCategory:"UtilityApplication",operatingSystem:"Any",offers:{"@type":"Offer",price:"0",priceCurrency:"USD"},creator:{"@type":"Organization",name:"DecisionsMaker Online"}}),(a,U)=>{const m=C("AppHeader"),g=x;return c(),l("div",null,[u(m),s("div",B,[s("div",N,[s("h1",H,p(r(t).defaultWheel.name),1),s("div",I,[u(g,{"initial-prizes":r(t).defaultWheel.prizes,"wheel-id":r(t).defaultWheel.slug,title:r(t).defaultWheel.name,"other-wheels":r(t).otherWheels},null,8,["initial-prizes","wheel-id","title","other-wheels"])])]),r(t).defaultWheel.articles&&r(t).defaultWheel.articles.length>0?(c(),l("div",z,[s("div",O,[(c(!0),l(q,null,S(r(t).defaultWheel.articles,(d,f)=>(c(),l("article",{key:f,class:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700"},[s("h2",Q,p(d.title),1),s("div",{class:"prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300",innerHTML:d.content},null,8,T)]))),128))])])):M("",!0)])])}}}),P=A(F,[["__scopeId","data-v-4cc4d54e"]]);export{P as default};
