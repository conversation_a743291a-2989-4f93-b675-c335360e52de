var J=Object.defineProperty;var F=a=>{throw TypeError(a)};var Q=(a,t,s)=>t in a?J(a,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[t]=s;var k=(a,t,s)=>Q(a,typeof t!="symbol"?t+"":t,s),X=(a,t,s)=>t.has(a)||F("Cannot "+s);var O=(a,t,s)=>(X(a,t,"read from private field"),s?s.call(a):t.get(a)),I=(a,t,s)=>t.has(a)?F("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(a):t.set(a,s);import{d as Z,l as q,m as x,b as K,e as ee,h as A,r as te,o as ae,_ as ie,c as M,n as w,p as se,q as D,s as re,v as R,x as ne,y as oe,k as ce,z as le,A as ue,B as fe,C as de,D as he,E as L,G as me,H as ye}from"./DedumHzx.js";const ge={class:"wheel-interactive"},pe=Z({__name:"WheelInteractive",props:{initialPrizes:{default:()=>[]},wheelId:{},title:{},otherWheels:{default:()=>[]}},setup(a,{expose:t}){const s=a,e=q([...s.initialPrizes]);x(()=>s.initialPrizes,r=>{e.value=[...r]},{immediate:!0});const i=r=>{e.value=[...r]};return t({getPrizes:()=>e.value,setPrizes:r=>{e.value=[...r]},resetPrizes:()=>{e.value=[...s.initialPrizes]}}),(r,n)=>{const o=te("LuckWheel");return ae(),K("div",ge,[ee(o,{prizes:A(e),"wheel-id":r.wheelId,title:r.title,"other-wheels":r.otherWheels,onPrizesChange:i},null,8,["prizes","wheel-id","title","other-wheels"])])}}}),Te=ie(pe,[["__scopeId","data-v-3c24298e"]]),j=[{id:"yes-no",name:"Yes or No Decision Maker",slug:"yes-no-decision-maker",isDefault:!0,prizes:[{text:"Yes",color:"#22c55e"},{text:"No",color:"#ef4444"}],seo:{title:"Yes or No Decision Maker - Quick Binary Choice Wheel",description:"Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.",keywords:["yes no decision","binary choice","decision maker","quick decision"]},articles:[{title:"The Psychology of Binary Decisions",content:`
          <p>
            Binary decisions are fundamental to human psychology. When faced with a simple yes or no choice, 
            our brains often overthink the process, leading to decision paralysis.
          </p>
          <p>
            Using a decision wheel removes the burden of choice from your conscious mind, allowing your 
            subconscious preferences to guide the outcome. This can often lead to more satisfying decisions.
          </p>
        `},{title:"When to Use Yes/No Decision Making",content:`
          <p>
            Yes or no decisions are perfect for:
          </p>
          <ul>
            <li>Breaking ties when you're genuinely undecided</li>
            <li>Making quick decisions when time is limited</li>
            <li>Overcoming analysis paralysis</li>
            <li>Adding an element of fun to routine choices</li>
          </ul>
        `}]},{id:"what-to-eat",name:"What to Eat Decision Wheel",slug:"what-to-eat-decision-wheel",prizes:[{text:"Pizza",color:"#ff6b6b"},{text:"Burger",color:"#4ecdc4"},{text:"Sushi",color:"#45b7d1"},{text:"Pasta",color:"#96ceb4"},{text:"Tacos",color:"#feca57"},{text:"Salad",color:"#ff9ff3"},{text:"Sandwich",color:"#54a0ff"},{text:"Chinese",color:"#5f27cd"}],seo:{title:"What to Eat Decision Wheel - Food Choice Generator",description:"Cant decide what to eat? Use our food decision wheel to randomly choose from popular meal options. Perfect for indecisive foodies!",keywords:["what to eat","food decision","meal picker","restaurant choice","food wheel"]},articles:[{title:"Solving the Daily Food Dilemma",content:`
          <p>
            "What should I eat?" is one of the most common daily decisions we face. Decision fatigue from 
            constantly choosing meals can be exhausting.
          </p>
          <p>
            Our food decision wheel takes the stress out of meal planning by randomly selecting from 
            popular food options, helping you discover new favorites and break out of eating routines.
          </p>
        `}]},{id:"movie-night",name:"Movie Night Picker",slug:"movie-night-picker",prizes:[{text:"Action",color:"#e74c3c"},{text:"Comedy",color:"#f39c12"},{text:"Drama",color:"#9b59b6"},{text:"Horror",color:"#2c3e50"},{text:"Romance",color:"#e91e63"},{text:"Sci-Fi",color:"#3498db"},{text:"Thriller",color:"#34495e"},{text:"Documentary",color:"#27ae60"}],seo:{title:"Movie Night Picker - Random Movie Genre Selector",description:"End movie night arguments with our genre picker wheel. Randomly select from popular movie genres for your next film night.",keywords:["movie picker","film genre","movie night","what to watch","movie decision"]},articles:[{title:"Making Movie Night Decisions Fair",content:`
          <p>
            Movie night with friends or family often leads to lengthy debates about what to watch. 
            Different people have different preferences, and finding a compromise can be challenging.
          </p>
          <p>
            Using a movie genre picker wheel ensures everyone has an equal chance of their preferred 
            genre being selected, making the decision process fair and fun.
          </p>
        `}]},{id:"weekend-activity",name:"Weekend Activity Planner",slug:"weekend-activity-planner",prizes:[{text:"Hiking",color:"#2ecc71"},{text:"Beach",color:"#3498db"},{text:"Museum",color:"#9b59b6"},{text:"Shopping",color:"#e91e63"},{text:"Park",color:"#27ae60"},{text:"Movies",color:"#34495e"},{text:"Restaurant",color:"#e67e22"},{text:"Stay Home",color:"#95a5a6"}],seo:{title:"Weekend Activity Planner - Random Activity Generator",description:"Plan your perfect weekend with our activity picker wheel. Discover new activities and break out of your routine.",keywords:["weekend activities","activity planner","what to do","weekend ideas","activity picker"]},articles:[{title:"Breaking Out of Weekend Routines",content:`
          <p>
            Many people fall into predictable weekend routines, doing the same activities week after week. 
            While routine can be comforting, it can also lead to boredom and missed opportunities.
          </p>
          <p>
            Our weekend activity planner introduces an element of randomness to your leisure time, 
            encouraging you to try new experiences and make the most of your free time.
          </p>
        `}]},{id:"team-picker",name:"Team Member Picker",slug:"team-member-picker",prizes:[{text:"Alice",color:"#ff7675"},{text:"Bob",color:"#74b9ff"},{text:"Charlie",color:"#00b894"},{text:"Diana",color:"#fdcb6e"},{text:"Eve",color:"#e17055"},{text:"Frank",color:"#a29bfe"}],seo:{title:"Team Member Picker - Random Team Selection Wheel",description:"Fairly select team members, assign tasks, or pick volunteers with our random team picker wheel. Perfect for classrooms and workplaces.",keywords:["team picker","random selection","team member selector","fair selection","group picker"]},articles:[{title:"Fair Team Selection Made Easy",content:`
          <p>
            Whether you're a teacher assigning classroom tasks, a manager distributing work, or organizing team activities, 
            fair selection is crucial for maintaining group harmony.
          </p>
          <p>
            Our team picker wheel ensures everyone has an equal chance of being selected, removing bias and 
            making the process transparent and fun.
          </p>
        `}]}];function T(a){return j.find(t=>t.slug===a)}function H(){return j.find(a=>a.isDefault)||j[0]}function V(){return j.map(a=>({id:a.id,name:a.name,slug:a.slug,description:a.seo.description}))}async function Ue(a){try{const t=T(a);if(!t)throw new Error(`Wheel with slug "${a}" not found`);const e=V().filter(i=>i.slug!==a);return{wheel:t,otherWheels:e}}catch(t){throw console.error("Error fetching server wheel page data:",t),t}}async function Fe(){try{const a=H(),s=V().filter(e=>e.slug!==a.slug);return{defaultWheel:a,otherWheels:s}}catch(a){throw console.error("Error fetching server default wheel:",a),a}}function Ie(a){return!!T(a)}function Ne(a){try{const t=a?T(a):H();return t?{title:t.seo.title,description:t.seo.description,keywords:t.seo.keywords.join(", "),canonical:a?`https://decisionsmaker.online/${a}`:"https://decisionsmaker.online",ogImage:"https://decisionsmaker.online/og-image.png"}:null}catch(t){return console.error("Error getting wheel SEO data:",t),null}}function ve(a){return typeof a=="string"?`'${a}'`:new we().serialize(a)}const we=function(){var t;class a{constructor(){I(this,t,new Map)}compare(e,i){const r=typeof e,n=typeof i;return r==="string"&&n==="string"?e.localeCompare(i):r==="number"&&n==="number"?e-i:String.prototype.localeCompare.call(this.serialize(e,!0),this.serialize(i,!0))}serialize(e,i){if(e===null)return"null";switch(typeof e){case"string":return i?e:`'${e}'`;case"bigint":return`${e}n`;case"object":return this.$object(e);case"function":return this.$function(e)}return String(e)}serializeObject(e){const i=Object.prototype.toString.call(e);if(i!=="[object Object]")return this.serializeBuiltInType(i.length<10?`unknown:${i}`:i.slice(8,-1),e);const r=e.constructor,n=r===Object||r===void 0?"":r.name;if(n!==""&&globalThis[n]===r)return this.serializeBuiltInType(n,e);if(typeof e.toJSON=="function"){const o=e.toJSON();return n+(o!==null&&typeof o=="object"?this.$object(o):`(${this.serialize(o)})`)}return this.serializeObjectEntries(n,Object.entries(e))}serializeBuiltInType(e,i){const r=this["$"+e];if(r)return r.call(this,i);if(typeof(i==null?void 0:i.entries)=="function")return this.serializeObjectEntries(e,i.entries());throw new Error(`Cannot serialize ${e}`)}serializeObjectEntries(e,i){const r=Array.from(i).sort((o,u)=>this.compare(o[0],u[0]));let n=`${e}{`;for(let o=0;o<r.length;o++){const[u,y]=r[o];n+=`${this.serialize(u,!0)}:${this.serialize(y)}`,o<r.length-1&&(n+=",")}return n+"}"}$object(e){let i=O(this,t).get(e);return i===void 0&&(O(this,t).set(e,`#${O(this,t).size}`),i=this.serializeObject(e),O(this,t).set(e,i)),i}$function(e){const i=Function.prototype.toString.call(e);return i.slice(-15)==="[native code] }"?`${e.name||""}()[native]`:`${e.name}(${e.length})${i.replace(/\s*\n\s*/g,"")}`}$Array(e){let i="[";for(let r=0;r<e.length;r++)i+=this.serialize(e[r]),r<e.length-1&&(i+=",");return i+"]"}$Date(e){try{return`Date(${e.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(e){return`ArrayBuffer[${new Uint8Array(e).join(",")}]`}$Set(e){return`Set${this.$Array(Array.from(e).sort((i,r)=>this.compare(i,r)))}`}$Map(e){return this.serializeObjectEntries("Map",e.entries())}}t=new WeakMap;for(const s of["Error","RegExp","URL"])a.prototype["$"+s]=function(e){return`${s}(${e})`};for(const s of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])a.prototype["$"+s]=function(e){return`${s}[${e.join(",")}]`};for(const s of["BigInt64Array","BigUint64Array"])a.prototype["$"+s]=function(e){return`${s}[${e.join("n,")}${e.length>0?"n":""}]`};return a}(),_e=[1779033703,-1150833019,1013904242,-1521486534,1359893119,-1694144372,528734635,1541459225],De=[1116352408,1899447441,-1245643825,-373957723,961987163,1508970993,-1841331548,-1424204075,-670586216,310598401,607225278,1426881987,1925078388,-2132889090,-1680079193,-1046744716,-459576895,-272742522,264347078,604807628,770255983,1249150122,1555081692,1996064986,-1740746414,-1473132947,-1341970488,-1084653625,-958395405,-710438585,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,-2117940946,-1838011259,-1564481375,-1474664885,-1035236496,-949202525,-778901479,-694614492,-200395387,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,-2067236844,-1933114872,-1866530822,-1538233109,-1090935817,-965641998],be="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",z=[];class Be{constructor(){k(this,"_data",new C);k(this,"_hash",new C([..._e]));k(this,"_nDataBytes",0);k(this,"_minBufferSize",0)}finalize(t){t&&this._append(t);const s=this._nDataBytes*8,e=this._data.sigBytes*8;return this._data.words[e>>>5]|=128<<24-e%32,this._data.words[(e+64>>>9<<4)+14]=Math.floor(s/4294967296),this._data.words[(e+64>>>9<<4)+15]=s,this._data.sigBytes=this._data.words.length*4,this._process(),this._hash}_doProcessBlock(t,s){const e=this._hash.words;let i=e[0],r=e[1],n=e[2],o=e[3],u=e[4],y=e[5],f=e[6],p=e[7];for(let c=0;c<64;c++){if(c<16)z[c]=t[s+c]|0;else{const h=z[c-15],d=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,_=z[c-2],v=(_<<15|_>>>17)^(_<<13|_>>>19)^_>>>10;z[c]=d+z[c-7]+v+z[c-16]}const g=u&y^~u&f,l=i&r^i&n^r&n,m=(i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22),b=(u<<26|u>>>6)^(u<<21|u>>>11)^(u<<7|u>>>25),P=p+b+g+De[c]+z[c],B=m+l;p=f,f=y,y=u,u=o+P|0,o=n,n=r,r=i,i=P+B|0}e[0]=e[0]+i|0,e[1]=e[1]+r|0,e[2]=e[2]+n|0,e[3]=e[3]+o|0,e[4]=e[4]+u|0,e[5]=e[5]+y|0,e[6]=e[6]+f|0,e[7]=e[7]+p|0}_append(t){typeof t=="string"&&(t=C.fromUtf8(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(t){let s,e=this._data.sigBytes/64;t?e=Math.ceil(e):e=Math.max((e|0)-this._minBufferSize,0);const i=e*16,r=Math.min(i*4,this._data.sigBytes);if(i){for(let n=0;n<i;n+=16)this._doProcessBlock(this._data.words,n);s=this._data.words.splice(0,i),this._data.sigBytes-=r}return new C(s,r)}}class C{constructor(t,s){k(this,"words");k(this,"sigBytes");t=this.words=t||[],this.sigBytes=s===void 0?t.length*4:s}static fromUtf8(t){const s=unescape(encodeURIComponent(t)),e=s.length,i=[];for(let r=0;r<e;r++)i[r>>>2]|=(s.charCodeAt(r)&255)<<24-r%4*8;return new C(i,e)}toBase64(){const t=[];for(let s=0;s<this.sigBytes;s+=3){const e=this.words[s>>>2]>>>24-s%4*8&255,i=this.words[s+1>>>2]>>>24-(s+1)%4*8&255,r=this.words[s+2>>>2]>>>24-(s+2)%4*8&255,n=e<<16|i<<8|r;for(let o=0;o<4&&s*8+o*6<this.sigBytes*8;o++)t.push(be.charAt(n>>>6*(3-o)&63))}return t.join("")}concat(t){if(this.words[this.sigBytes>>>2]&=4294967295<<32-this.sigBytes%4*8,this.words.length=Math.ceil(this.sigBytes/4),this.sigBytes%4)for(let s=0;s<t.sigBytes;s++){const e=t.words[s>>>2]>>>24-s%4*8&255;this.words[this.sigBytes+s>>>2]|=e<<24-(this.sigBytes+s)%4*8}else for(let s=0;s<t.sigBytes;s+=4)this.words[this.sigBytes+s>>>2]=t.words[s>>>2];this.sigBytes+=t.sigBytes}}function ke(a){return new Be().finalize(a).toBase64()}function $(a){return ke(ve(a))}const ze={trailing:!0};function Pe(a,t=25,s={}){if(s={...ze,...s},!Number.isFinite(t))throw new TypeError("Expected `wait` to be a finite number");let e,i,r=[],n,o;const u=(y,f)=>(n=$e(a,y,f),n.finally(()=>{if(n=null,s.trailing&&o&&!i){const p=u(y,o);return o=null,p}}),n);return function(...y){return n?(s.trailing&&(o=y),n):new Promise(f=>{const p=!i&&s.leading;clearTimeout(i),i=setTimeout(()=>{i=null;const c=s.leading?e:u(this,y);for(const g of r)g(c);r=[]},t),p?(e=u(this,y),f(e)):r.push(f)})}}async function $e(a,t,s){return await a.apply(t,s)}const Ce=Symbol.for("nuxt:client-only"),Se=a=>a==="defer"||a===!1;function Oe(...a){var g;const t=typeof a[a.length-1]=="string"?a.pop():void 0;Ee(a[0],a[1])&&a.unshift(t);let[s,e,i={}]=a;const r=M(()=>w(s));if(typeof r.value!="string")throw new TypeError("[nuxt] [useAsyncData] key must be a string.");if(typeof e!="function")throw new TypeError("[nuxt] [useAsyncData] handler must be a function.");const n=se();i.server??(i.server=!0),i.default??(i.default=xe),i.getCachedData??(i.getCachedData=Y),i.lazy??(i.lazy=!1),i.immediate??(i.immediate=!0),i.deep??(i.deep=D.deep),i.dedupe??(i.dedupe="cancel"),i._functionName,n._asyncData[r.value];const o={cause:"initial",dedupe:i.dedupe};(g=n._asyncData[r.value])!=null&&g._init||(o.cachedData=i.getCachedData(r.value,n,{cause:"initial"}),n._asyncData[r.value]=N(n,r.value,e,i,o.cachedData));const u=n._asyncData[r.value];u._deps++;const y=()=>n._asyncData[r.value].execute(o),f=i.server!==!1&&n.payload.serverRendered;{let l=function(h){const d=n._asyncData[h];d!=null&&d._deps&&(d._deps--,d._deps===0&&(d==null||d._off()))};const m=ue();if(m&&f&&i.immediate&&!m.sp&&(m.sp=[]),m&&!m._nuxtOnBeforeMountCbs){m._nuxtOnBeforeMountCbs=[];const h=m._nuxtOnBeforeMountCbs;fe(()=>{h.forEach(d=>{d()}),h.splice(0,h.length)}),de(()=>h.splice(0,h.length))}const b=m&&(m._nuxtClientOnly||he(Ce,!1));f&&n.isHydrating&&(u.error.value||u.data.value!=null)?(u.pending.value=!1,u.status.value=u.error.value?"error":"success"):m&&(!b&&n.payload.serverRendered&&n.isHydrating||i.lazy)&&i.immediate?m._nuxtOnBeforeMountCbs.push(y):i.immediate&&y();const P=le(),B=x([r,...i.watch||[]],([h],[d])=>{var _,v;if((h||d)&&h!==d){const S=((_=n._asyncData[d])==null?void 0:_.data.value)!==D.value;d&&l(d);const E={cause:"initial",dedupe:i.dedupe};(v=n._asyncData[h])!=null&&v._init||(E.cachedData=i.getCachedData(h,n,{cause:"initial"}),n._asyncData[h]=N(n,h,e,i,E.cachedData)),n._asyncData[h]._deps++,(i.immediate||S)&&n._asyncData[h].execute(E)}else u._execute({cause:"watch",dedupe:i.dedupe})},{flush:"sync"});P&&re(()=>{B(),l(r.value)})}const p={data:W(()=>{var l;return(l=n._asyncData[r.value])==null?void 0:l.data}),pending:W(()=>{var l;return(l=n._asyncData[r.value])==null?void 0:l.pending}),status:W(()=>{var l;return(l=n._asyncData[r.value])==null?void 0:l.status}),error:W(()=>{var l;return(l=n._asyncData[r.value])==null?void 0:l.error}),refresh:(...l)=>n._asyncData[r.value].execute(...l),execute:(...l)=>n._asyncData[r.value].execute(...l),clear:()=>G(n,r.value)},c=Promise.resolve(n._asyncDataPromises[r.value]).then(()=>p);return Object.assign(c,p),c}function W(a){return M({get(){var t;return(t=a())==null?void 0:t.value},set(t){const s=a();s&&(s.value=t)}})}function Ee(a,t){return!(typeof a=="string"||typeof a=="object"&&a!==null||typeof a=="function"&&typeof t=="function")}function G(a,t){t in a.payload.data&&(a.payload.data[t]=void 0),t in a.payload._errors&&(a.payload._errors[t]=D.errorValue),a._asyncData[t]&&(a._asyncData[t].data.value=void 0,a._asyncData[t].error.value=D.errorValue,a._asyncData[t].pending.value=!1,a._asyncData[t].status.value="idle"),t in a._asyncDataPromises&&(a._asyncDataPromises[t]&&(a._asyncDataPromises[t].cancelled=!0),a._asyncDataPromises[t]=void 0)}function We(a,t){const s={};for(const e of t)s[e]=a[e];return s}function N(a,t,s,e,i){var p;(p=a.payload._errors)[t]??(p[t]=D.errorValue);const r=e.getCachedData!==Y,n=s,o=e.deep?q:R,u=i!=null,y=a.hook("app:data:refresh",async c=>{(!c||c.includes(t))&&await f.execute({cause:"refresh:hook"})}),f={data:o(u?i:e.default()),pending:R(!u),error:ne(a.payload._errors,t),status:R("idle"),execute:(c={})=>{if(a._asyncDataPromises[t]){if(Se(c.dedupe??e.dedupe))return a._asyncDataPromises[t];a._asyncDataPromises[t].cancelled=!0}if(c.cause==="initial"||a.isHydrating){const l="cachedData"in c?c.cachedData:e.getCachedData(t,a,{cause:c.cause??"refresh:manual"});if(l!=null)return a.payload.data[t]=f.data.value=l,f.error.value=D.errorValue,f.status.value="success",Promise.resolve(l)}f.pending.value=!0,f.status.value="pending";const g=new Promise((l,m)=>{try{l(n(a))}catch(b){m(b)}}).then(async l=>{if(g.cancelled)return a._asyncDataPromises[t];let m=l;e.transform&&(m=await e.transform(l)),e.pick&&(m=We(m,e.pick)),a.payload.data[t]=m,f.data.value=m,f.error.value=D.errorValue,f.status.value="success"}).catch(l=>{if(g.cancelled)return a._asyncDataPromises[t];f.error.value=ce(l),f.data.value=A(e.default()),f.status.value="error"}).finally(()=>{g.cancelled||(f.pending.value=!1,delete a._asyncDataPromises[t])});return a._asyncDataPromises[t]=g,a._asyncDataPromises[t]},_execute:Pe((...c)=>f.execute(...c),0,{leading:!0}),_default:e.default,_deps:0,_init:!0,_hash:void 0,_off:()=>{var c;y(),(c=a._asyncData[t])!=null&&c._init&&(a._asyncData[t]._init=!1),r||oe(()=>{var g;(g=a._asyncData[t])!=null&&g._init||(G(a,t),f.execute=()=>Promise.resolve(),f.data.value=D.value)})}};return f}const xe=()=>D.value,Y=(a,t,s)=>{if(t.isHydrating)return t.payload.data[a];if(s.cause!=="refresh:manual"&&s.cause!=="refresh:hook")return t.static.data[a]};function qe(a,t,s){const[e={},i]=typeof t=="string"?[{},t]:[t,s],r=M(()=>w(a)),n=M(()=>w(e.key)||"$f"+$([i,typeof r.value=="string"?r.value:"",...Me(e)]));if(!e.baseURL&&typeof r.value=="string"&&r.value[0]==="/"&&r.value[1]==="/")throw new Error('[nuxt] [useFetch] the request URL must not start with "//".');const{server:o,lazy:u,default:y,transform:f,pick:p,watch:c,immediate:g,getCachedData:l,deep:m,dedupe:b,...P}=e,B=L({...me,...P,cache:typeof e.cache=="boolean"?void 0:e.cache}),h={server:o,lazy:u,default:y,transform:f,pick:p,immediate:g,getCachedData:l,deep:m,dedupe:b,watch:c===!1?[]:[...c||[],B]};if(!g){let v=function(){h.immediate=!0};x(n,v,{flush:"sync",once:!0}),x([...c||[],B],v,{flush:"sync",once:!0})}let d;return Oe(c===!1?n.value:n,()=>{var U;(U=d==null?void 0:d.abort)==null||U.call(d,new DOMException("Request aborted as another request to the same endpoint was initiated.","AbortError")),d=typeof AbortController<"u"?new AbortController:{};const v=w(e.timeout);let S;return v&&(S=setTimeout(()=>d.abort(new DOMException("Request aborted due to timeout.","AbortError")),v),d.signal.onabort=()=>clearTimeout(S)),(e.$fetch||globalThis.$fetch)(r.value,{signal:d.signal,...B}).finally(()=>{clearTimeout(S)})},h)}function Me(a){var s;const t=[((s=w(a.method))==null?void 0:s.toUpperCase())||"GET",w(a.baseURL)];for(const e of[a.params||a.query]){const i=w(e);if(!i)continue;const r={};for(const[n,o]of Object.entries(i))r[w(n)]=w(o);t.push(r)}if(a.body){const e=w(a.body);if(!e)t.push($(e));else if(e instanceof ArrayBuffer)t.push($(Object.fromEntries([...new Uint8Array(e).entries()].map(([i,r])=>[i,r.toString()]))));else if(e instanceof FormData){const i={};for(const r of e.entries()){const[n,o]=r;i[n]=o instanceof File?o.name:o}t.push($(i))}else if(ye(e))t.push($(L(e)));else try{t.push($(e))}catch{console.warn("[useFetch] Failed to hash body",e)}}return t}export{Te as _,Ne as a,Ue as b,Fe as g,qe as u,Ie as v};
