{"name": "makechoice-prod", "version": "0.1.0", "type": "module", "private": true, "dependencies": {"@babel/parser": "7.27.5", "@vue/compiler-core": "3.5.17", "@vue/compiler-dom": "3.5.17", "@vue/compiler-ssr": "3.5.17", "@vue/reactivity": "3.5.17", "@vue/runtime-core": "3.5.17", "@vue/runtime-dom": "3.5.17", "@vue/server-renderer": "3.5.17", "@vue/shared": "3.5.17", "devalue": "5.1.1", "entities": "4.5.0", "estree-walker": "2.0.2", "hookable": "5.5.3", "source-map-js": "1.2.1", "ufo": "1.6.1", "unhead": "2.0.11", "vue": "3.5.17", "vue-bundle-renderer": "2.1.1"}}