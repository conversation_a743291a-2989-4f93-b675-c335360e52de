import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as setHeader, a as setResponseStatus } from '../../nitro/nitro.mjs';
import { c as generateSitemapData } from '../../_/server-api.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const sitemap_xml_get = defineEventHandler(async (event) => {
  try {
    setHeader(event, "Content-Type", "application/xml");
    setHeader(event, "Cache-Control", "public, max-age=3600, s-maxage=3600");
    const urls = generateSitemapData();
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.map((url) => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join("\n")}
</urlset>`;
    return sitemap;
  } catch (error) {
    console.error("Error generating sitemap:", error);
    setResponseStatus(event, 500);
    setHeader(event, "Content-Type", "text/plain");
    return "Error generating sitemap";
  }
});

export { sitemap_xml_get as default };
//# sourceMappingURL=sitemap.xml.get.mjs.map
