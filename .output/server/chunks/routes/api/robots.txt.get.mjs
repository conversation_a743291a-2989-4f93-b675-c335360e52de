import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, c as setHeader, a as setResponseStatus } from '../../nitro/nitro.mjs';
import { b as generateRobotsContent } from '../../_/server-api.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const robots_txt_get = defineEventHandler(async (event) => {
  try {
    setHeader(event, "Content-Type", "text/plain");
    setHeader(event, "Cache-Control", "public, max-age=86400, s-maxage=86400");
    const robotsContent = generateRobotsContent();
    return robotsContent;
  } catch (error) {
    console.error("Error generating robots.txt:", error);
    setResponseStatus(event, 500);
    return "Error generating robots.txt";
  }
});

export { robots_txt_get as default };
//# sourceMappingURL=robots.txt.get.mjs.map
