import { d as defineEventHandler, s as setHeaders, a as setResponseStatus } from '../../nitro/nitro.mjs';
import { g as getCacheHeaders, d as getWheelList, h as handleServerError } from '../../_/server-api.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const wheelList_get = defineEventHandler(async (event) => {
  try {
    setHeaders(event, getCacheHeaders(3600));
    const wheelList = getWheelList();
    return wheelList;
  } catch (error) {
    console.error("Error in wheel-list API:", error);
    const errorResponse = handleServerError(error, "wheel-list API");
    setResponseStatus(event, errorResponse.statusCode);
    return {
      error: true,
      message: errorResponse.message,
      data: []
    };
  }
});

export { wheelList_get as default };
//# sourceMappingURL=wheel-list.get.mjs.map
