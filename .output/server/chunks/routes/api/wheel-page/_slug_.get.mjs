import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, e as getRouter<PERSON>aram, a as setResponseStatus, s as setHeaders } from '../../../nitro/nitro.mjs';
import { g as getCacheHeaders, f as findWheelBySlug, d as getWheelList, h as handleServerError } from '../../../_/server-api.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const _slug__get = defineEventHandler(async (event) => {
  try {
    const slug = getRouterParam(event, "slug");
    if (!slug) {
      setResponseStatus(event, 400);
      return {
        currentWheel: null,
        otherWheels: [],
        success: false,
        message: "Slug parameter is required"
      };
    }
    setHeaders(event, getCacheHeaders(3600));
    const currentWheel = findWheelBySlug(slug);
    if (!currentWheel) {
      setResponseStatus(event, 404);
      const response2 = {
        currentWheel: null,
        otherWheels: [],
        success: false,
        message: `Wheel with slug "${slug}" not found`
      };
      return response2;
    }
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter((wheel) => wheel.slug !== slug);
    const response = {
      currentWheel,
      otherWheels,
      success: true,
      message: "Wheel data retrieved successfully"
    };
    return response;
  } catch (error) {
    console.error("Error in wheel-page/[slug] API:", error);
    const errorResponse = handleServerError(error, "wheel-page/[slug] API");
    setResponseStatus(event, errorResponse.statusCode);
    const response = {
      currentWheel: null,
      otherWheels: [],
      success: false,
      message: errorResponse.message
    };
    return response;
  }
});

export { _slug__get as default };
//# sourceMappingURL=_slug_.get.mjs.map
