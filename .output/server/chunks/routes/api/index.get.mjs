import { d as defineEventHandler, s as setHeaders, a as setResponseStatus } from '../../nitro/nitro.mjs';
import { g as getCacheHeaders, a as getDefaultWheel, d as getWheelList, h as handleServerError } from '../../_/server-api.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const index_get = defineEventHandler(async (event) => {
  try {
    setHeaders(event, getCacheHeaders(3600));
    const defaultWheel = getDefaultWheel();
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter((w) => w.slug !== defaultWheel.slug);
    const response = {
      currentWheel: defaultWheel,
      otherWheels,
      success: true,
      message: "Default wheel data retrieved successfully"
    };
    return response;
  } catch (error) {
    console.error("Error in wheel-page API:", error);
    const errorResponse = handleServerError(error, "wheel-page API");
    setResponseStatus(event, errorResponse.statusCode);
    const response = {
      currentWheel: null,
      otherWheels: [],
      success: false,
      message: errorResponse.message
    };
    return response;
  }
});

export { index_get as default };
//# sourceMappingURL=index.get.mjs.map
