import { d as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as get<PERSON>ethod, a as setResponseStatus, b as getQuery } from '../../nitro/nitro.mjs';
import { h as handleServerError } from '../../_/server-api.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const revalidate_post = defineEventHandler(async (event) => {
  try {
    if (getMethod(event) !== "POST") {
      setResponseStatus(event, 405);
      return {
        success: false,
        message: "Method not allowed"
      };
    }
    const query = getQuery(event);
    const path = query.path;
    if (path) {
      console.log(`Revalidating path: ${path}`);
      return {
        success: true,
        message: `Path ${path} revalidated successfully`,
        revalidated: true,
        path
      };
    } else {
      console.log("Revalidating all paths");
      return {
        success: true,
        message: "All paths revalidated successfully",
        revalidated: true
      };
    }
  } catch (error) {
    console.error("Error in revalidate API:", error);
    const errorResponse = handleServerError(error, "revalidate API");
    setResponseStatus(event, errorResponse.statusCode);
    return {
      success: false,
      message: errorResponse.message,
      revalidated: false
    };
  }
});

export { revalidate_post as default };
//# sourceMappingURL=revalidate.post.mjs.map
