import { d as define<PERSON>vent<PERSON><PERSON><PERSON>, s as setHeaders, a as setResponseStatus } from '../../nitro/nitro.mjs';
import { g as getCacheHeaders, a as getDefaultWheel, h as handleServerError } from '../../_/server-api.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';

const prizes_get = defineEventHandler(async (event) => {
  try {
    setHeaders(event, getCacheHeaders(3600));
    const defaultWheel = getDefaultWheel();
    return defaultWheel.prizes;
  } catch (error) {
    console.error("Error fetching default prizes:", error);
    const errorResponse = handleServerError(error, "prizes API");
    setResponseStatus(event, errorResponse.statusCode);
    const fallbackPrizes = [
      { text: "Yes", color: "#22c55e" },
      { text: "No", color: "#ef4444" }
    ];
    return fallbackPrizes;
  }
});

export { prizes_get as default };
//# sourceMappingURL=prizes.get.mjs.map
