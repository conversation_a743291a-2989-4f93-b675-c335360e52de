{"version": 3, "file": "server-api.mjs", "sources": ["../../../../data/presetWheels.ts", "../../../../utils/server-api.ts"], "sourcesContent": null, "names": [], "mappings": "AAEO,MAAM,YAAA,GAA8B;AAAA,EACzC;AAAA,IACE,EAAA,EAAI,QAAA;AAAA,IACJ,IAAA,EAAM,0BAAA;AAAA,IACN,IAAA,EAAM,uBAAA;AAAA,IACN,SAAA,EAAW,IAAA;AAAA,IACX,MAAA,EAAQ;AAAA,MACN,EAAE,IAAA,EAAM,KAAA,EAAO,KAAA,EAAO,SAAA,EAAU;AAAA,MAChC,EAAE,IAAA,EAAM,IAAA,EAAM,KAAA,EAAO,SAAA;AAAU,KACjC;AAAA,IACA,GAAA,EAAK;AAAA,MACH,KAAA,EAAO,sDAAA;AAAA,MACP,WAAA,EAAa,gIAAA;AAAA,MACb,QAAA,EAAU,CAAC,iBAAA,EAAmB,eAAA,EAAiB,kBAAkB,gBAAgB;AAAA,KACnF;AAAA,IACA,QAAA,EAAU;AAAA,MACR;AAAA,QACE,KAAA,EAAO,oCAAA;AAAA,QACP,OAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAAA;AAAA,OAUX;AAAA,MACA;AAAA,QACE,KAAA,EAAO,oCAAA;AAAA,QACP,OAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAAA;AAAA;AAWX;AACF,GACF;AAAA,EACA;AAAA,IACE,EAAA,EAAI,aAAA;AAAA,IACJ,IAAA,EAAM,4BAAA;AAAA,IACN,IAAA,EAAM,4BAAA;AAAA,IACN,MAAA,EAAQ;AAAA,MACN,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA,EAAU;AAAA,MAClC,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,EAAO,SAAA,EAAU;AAAA,MACnC,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA,EAAU;AAAA,MAClC,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA,EAAU;AAAA,MAClC,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA,EAAU;AAAA,MAClC,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA,EAAU;AAAA,MAClC,EAAE,IAAA,EAAM,UAAA,EAAY,KAAA,EAAO,SAAA,EAAU;AAAA,MACrC,EAAE,IAAA,EAAM,SAAA,EAAW,KAAA,EAAO,SAAA;AAAU,KACtC;AAAA,IACA,GAAA,EAAK;AAAA,MACH,KAAA,EAAO,oDAAA;AAAA,MACP,WAAA,EAAa,oIAAA;AAAA,MACb,UAAU,CAAC,aAAA,EAAe,eAAA,EAAiB,aAAA,EAAe,qBAAqB,YAAY;AAAA,KAC7F;AAAA,IACA,QAAA,EAAU;AAAA,MACR;AAAA,QACE,KAAA,EAAO,gCAAA;AAAA,QACP,OAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAAA;AAAA;AAUX;AACF,GACF;AAAA,EACA;AAAA,IACE,EAAA,EAAI,aAAA;AAAA,IACJ,IAAA,EAAM,oBAAA;AAAA,IACN,IAAA,EAAM,oBAAA;AAAA,IACN,MAAA,EAAQ;AAAA,MACN,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,EAAO,SAAA,EAAU;AAAA,MACnC,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,EAAO,SAAA,EAAU;AAAA,MACnC,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA,EAAU;AAAA,MAClC,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,EAAO,SAAA,EAAU;AAAA,MACnC,EAAE,IAAA,EAAM,SAAA,EAAW,KAAA,EAAO,SAAA,EAAU;AAAA,MACpC,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,EAAO,SAAA,EAAU;AAAA,MACnC,EAAE,IAAA,EAAM,UAAA,EAAY,KAAA,EAAO,SAAA,EAAU;AAAA,MACrC,EAAE,IAAA,EAAM,aAAA,EAAe,KAAA,EAAO,SAAA;AAAU,KAC1C;AAAA,IACA,GAAA,EAAK;AAAA,MACH,KAAA,EAAO,kDAAA;AAAA,MACP,WAAA,EAAa,4HAAA;AAAA,MACb,UAAU,CAAC,cAAA,EAAgB,YAAA,EAAc,aAAA,EAAe,iBAAiB,gBAAgB;AAAA,KAC3F;AAAA,IACA,QAAA,EAAU;AAAA,MACR;AAAA,QACE,KAAA,EAAO,mCAAA;AAAA,QACP,OAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAAA;AAAA;AAUX;AACF,GACF;AAAA,EACA;AAAA,IACE,EAAA,EAAI,kBAAA;AAAA,IACJ,IAAA,EAAM,0BAAA;AAAA,IACN,IAAA,EAAM,0BAAA;AAAA,IACN,MAAA,EAAQ;AAAA,MACN,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,EAAO,SAAA,EAAU;AAAA,MACnC,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA,EAAU;AAAA,MAClC,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,EAAO,SAAA,EAAU;AAAA,MACnC,EAAE,IAAA,EAAM,UAAA,EAAY,KAAA,EAAO,SAAA,EAAU;AAAA,MACrC,EAAE,IAAA,EAAM,MAAA,EAAQ,KAAA,EAAO,SAAA,EAAU;AAAA,MACjC,EAAE,IAAA,EAAM,QAAA,EAAU,KAAA,EAAO,SAAA,EAAU;AAAA,MACnC,EAAE,IAAA,EAAM,YAAA,EAAc,KAAA,EAAO,SAAA,EAAU;AAAA,MACvC,EAAE,IAAA,EAAM,WAAA,EAAa,KAAA,EAAO,SAAA;AAAU,KACxC;AAAA,IACA,GAAA,EAAK;AAAA,MACH,KAAA,EAAO,sDAAA;AAAA,MACP,WAAA,EAAa,kHAAA;AAAA,MACb,UAAU,CAAC,oBAAA,EAAsB,kBAAA,EAAoB,YAAA,EAAc,iBAAiB,iBAAiB;AAAA,KACvG;AAAA,IACA,QAAA,EAAU;AAAA,MACR;AAAA,QACE,KAAA,EAAO,kCAAA;AAAA,QACP,OAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAAA;AAAA;AAUX;AACF,GACF;AAAA,EACA;AAAA,IACE,EAAA,EAAI,aAAA;AAAA,IACJ,IAAA,EAAM,oBAAA;AAAA,IACN,IAAA,EAAM,oBAAA;AAAA,IACN,MAAA,EAAQ;AAAA,MACN,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA,EAAU;AAAA,MAClC,EAAE,IAAA,EAAM,KAAA,EAAO,KAAA,EAAO,SAAA,EAAU;AAAA,MAChC,EAAE,IAAA,EAAM,SAAA,EAAW,KAAA,EAAO,SAAA,EAAU;AAAA,MACpC,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA,EAAU;AAAA,MAClC,EAAE,IAAA,EAAM,KAAA,EAAO,KAAA,EAAO,SAAA,EAAU;AAAA,MAChC,EAAE,IAAA,EAAM,OAAA,EAAS,KAAA,EAAO,SAAA;AAAU,KACpC;AAAA,IACA,GAAA,EAAK;AAAA,MACH,KAAA,EAAO,kDAAA;AAAA,MACP,WAAA,EAAa,wIAAA;AAAA,MACb,UAAU,CAAC,aAAA,EAAe,kBAAA,EAAoB,sBAAA,EAAwB,kBAAkB,cAAc;AAAA,KACxG;AAAA,IACA,QAAA,EAAU;AAAA,MACR;AAAA,QACE,KAAA,EAAO,+BAAA;AAAA,QACP,OAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAAA;AAAA;AAUX;AACF;AAEJ,CAAA;AAGO,SAAS,gBAAgB,IAAA,EAAuC;AACrE,EAAA,OAAO,YAAA,CAAa,IAAA,CAAK,CAAA,KAAA,KAAS,KAAA,CAAM,SAAS,IAAI,CAAA;AACvD;AAGO,SAAS,eAAA,GAA+B;AAC7C,EAAA,OAAO,aAAa,IAAA,CAAK,CAAA,KAAA,KAAS,MAAM,SAAS,CAAA,IAAK,aAAa,CAAC,CAAA;AACtE;AAGO,SAAS,YAAA,GAAe;AAC7B,EAAA,OAAO,YAAA,CAAa,IAAI,CAAA,KAAA,MAAU;AAAA,IAChC,IAAI,KAAA,CAAM,EAAA;AAAA,IACV,MAAM,KAAA,CAAM,IAAA;AAAA,IACZ,MAAM,KAAA,CAAM,IAAA;AAAA,IACZ,WAAA,EAAa,MAAM,GAAA,CAAI;AAAA,GACzB,CAAE,CAAA;AACJ;;ACtGO,SAAS,mBAAA,GAAsB;AACpC,EAAA,IAAI;AACF,IAAA,MAAM,SAAS,YAAA,EAAa;AAC5B,IAAA,MAAM,OAAA,GAAU,+BAAA;AAEhB,IAAA,MAAM,IAAA,GAAO;AAAA,MACX;AAAA,QACE,GAAA,EAAK,OAAA;AAAA,QACL,OAAA,EAAA,iBAAS,IAAI,IAAA,EAAK,EAAE,WAAA,EAAY;AAAA,QAChC,UAAA,EAAY,OAAA;AAAA,QACZ,QAAA,EAAU;AAAA,OACZ;AAAA,MACA,GAAG,MAAA,CAAO,GAAA,CAAI,CAAA,KAAA,MAAU;AAAA,QACtB,GAAA,EAAK,CAAA,EAAG,OAAO,CAAA,CAAA,EAAI,MAAM,IAAI,CAAA,CAAA;AAAA,QAC7B,OAAA,EAAA,iBAAS,IAAI,IAAA,EAAK,EAAE,WAAA,EAAY;AAAA,QAChC,UAAA,EAAY,QAAA;AAAA,QACZ,QAAA,EAAU;AAAA,OACZ,CAAE;AAAA,KACJ;AAEA,IAAA,OAAO,IAAA;AAAA,WACA,KAAA,EAAO;AACd,IAAA,OAAA,CAAQ,KAAA,CAAM,kCAAkC,KAAK,CAAA;AACrD,IAAA,OAAO,EAAC;AAAA;AAEZ;AAKO,SAAS,qBAAA,GAAgC;AAC9C,EAAA,MAAM,OAAA,GAAU,+BAAA;AAEhB,EAAA,OAAO,CAAA;AAAA;;AAAA;AAAA,SAAA,EAIE,OAAO,CAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,uBAAA,CAAA;AAYlB;AAKO,SAAS,iBAAA,CAAkB,OAAY,OAAA,EAAiB;AA7J/D,EAAA,IAAA,EAAA,EAAA,EAAA;AA8JE,EAAA,OAAA,CAAQ,KAAA,CAAM,CAAA,gBAAA,EAAmB,OAAO,CAAA,CAAA,CAAA,EAAK,KAAK,CAAA;AAGlD,EAAA,IAAA,CAAI,EAAA,GAAA,KAAA,CAAM,OAAA,KAAN,IAAA,GAAA,MAAA,GAAA,EAAA,CAAe,QAAA,CAAS,WAAA,CAAA,EAAc;AACxC,IAAA,OAAO;AAAA,MACL,UAAA,EAAY,GAAA;AAAA,MACZ,OAAA,EAAS;AAAA,KACX;AAAA,GACF,MAAA,IAAA,CAAW,EAAA,GAAA,KAAA,CAAM,OAAA,KAAN,IAAA,GAAA,MAAA,GAAA,EAAA,CAAe,SAAS,YAAA,CAAA,EAAe;AAChD,IAAA,OAAO;AAAA,MACL,UAAA,EAAY,GAAA;AAAA,MACZ,OAAA,EAAS;AAAA,KACX;AAAA,GACF,MAAO;AACL,IAAA,OAAO;AAAA,MACL,UAAA,EAAY,GAAA;AAAA,MACZ,OAAA,EAAS;AAAA,KACX;AAAA;AAEJ;AAsCO,SAAS,eAAA,CAAgB,SAAiB,IAAA,EAAM;AACrD,EAAA,OAAO;AAAA,IACL,eAAA,EAAiB,CAAA,gBAAA,EAAmB,MAAM,CAAA,WAAA,EAAc,MAAM,CAAA,CAAA;AAAA,IAC9D,MAAA,EAAQ;AAAA,GACV;AACF;;;;"}